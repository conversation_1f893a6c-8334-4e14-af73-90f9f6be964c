import logging
import os

import requests as req

logger = logging.getLogger(__name__)

URL_PACTO_DISCOVERY_MS = os.getenv(
    "URL_PACTO_DISCOVERY_MS", "http://host.docker.internal:8101"
)


class PactoIntegrationTools:
    def __init__(self, id_empresa: str, send_logs_to=None) -> None:
        self.id_empresa = id_empresa
        self.send_logs_to = send_logs_to
        self.username, self.password = "conversasai", "123"

    def get_url(self, type_, id_empresa_):
        logger.info(f"Request dicovery: {URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}")
        response = req.get(f"{URL_PACTO_DISCOVERY_MS}/find/{id_empresa_}", timeout=10)
        url = (
            response.json()
            .get("content", {})
            .get("serviceUrls", {})
            .get(f"{type_}", None)
        )
        return url

    def get_token_auth(self, id_empresa_):
        id_empresa_ = self.id_empresa.split("-")[0]
        self.id_empresa.split("-")[1]

        req_body = {
            "chave": id_empresa_,
            "username": self.username,
            "senha": self.password,
        }
        logger.info(f"Request: {req_body}")
        url_auth = self.get_url("loginMsUrl", id_empresa_)
        logger.info(f"request auth: {url_auth}/aut/login")
        response = req.post(
            f"{url_auth}/aut/login",
            json=req_body,
            headers={"Content-Type": "application/json"},
        )
        content = response.json().get("content", {})
        token_auth = content.get("token", 120)

        return token_auth

    def search_by_phone(self, phone):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url = f"{self.get_url('admMsUrl', id_empresa_)}/v1/cliente"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa,
        }
        params = {
            "telefone": phone.removeprefix(
                "+55"
            ),  # Telefone sem prefixo para evitar erro no sistema
            "page": "0",
            "size": "35",
            "empresa": cod_empresa,
            "colaborador": "null",
            "tipoColaborador": "null",
        }
        try:
            response = req.get(
                url, headers=headers, params=params, timeout=10
            )  # algumas academias estão demorando para responder
        except:  # noqa
            return False, "Erro ao buscar usuário."
        if len(response.json().get("content", [])) > 1:
            return False, "Mais de um usuário encontrado."
        user_id = (
            response.json().get("content", [])[0].get("codigo", None)
            if response.json().get("content", []) != []
            else None
        )
        if user_id is None:
            return False, "Usuário não encontrado."
        user_data = self.get_user_by_id(user_id)
        if user_data == {}:
            return False, "Erro ao buscar usuário."
        else:
            return True, user_data

    def get_user_by_cpf(self, cpf):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        domain = self.get_url("admMsUrl", id_empresa_)
        url = f"{domain}/v1/cliente"
        token_auth = self.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
        }
        params = {
            "pesquisa": cpf,
            "page": "0",
            "size": "35",
            "empresa": cod_empresa,
            "colaborador": "null",
            "tipoColaborador": "null",
        }
        response = req.get(url, headers=headers, params=params, timeout=5)

        return url, headers, params, response

    def get_user_by_id(self, id_usuario):
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = (
            f"{self.get_url('contatoMsUrl', id_empresa_)}/v1/ia/contextos/aluno"
        )
        logger.info(f"get_user_by_id: url_micro_servicos={url_micro_servicos}")
        token_auth = self.get_token_auth(id_empresa_)
        logger.info(f"get_user_by_id: token_auth={token_auth}")
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa,
        }
        logger.info(f"get_user_by_id: headers={headers}")
        params = {
            "empresa": cod_empresa,
            "cliente": id_usuario,
            "contextoTreino": "false",
        }
        logger.info(f"get_user_by_id: params={params}")
        response = req.get(url=url_micro_servicos, headers=headers, params=params)
        logger.info(f"get_user_by_id: response.status_code={response.status_code}")
        logger.info(f"get_user_by_id: response.text={response.text}")
        if response.status_code == 200 and response.json().get("result", [{}]) != [{}]:
            user_context = response.json().get("result", [{}])[0]
            logger.info(f"get_user_by_id: user_context={user_context}")
            return user_context
        logger.info(
            f"get_user_by_id: params={params} - headers={headers} - url={url_micro_servicos}"
        )
        return {}

    def register_lead(
        self,
        nome,
        telefone,
        cpf,
        email=None,
        mensagem="Lead gerado pelos testes de integração!",
    ) -> int | None:
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        token_auth = self.get_token_auth(id_empresa_)
        logger.info(
            f"register_lead: id_empresa_={id_empresa_}, cod_empresa={cod_empresa}"
        )
        logger.info(f"register_lead: token_auth={token_auth}")

        base_url = self.get_url("contatoMsUrl", id_empresa_)
        url = f"{base_url}/v1/lead"
        logger.info(f"register_lead: url={url}")

        payload = {
            "codigoEmpresa": cod_empresa,
            "usernameResposavel": "conversasai",
            "nome": nome,
            "email": email or "",
            "telefone": telefone,
            "cpf": cpf,
            "sexo": "",
            "cep": "",
            "mensagem": mensagem,
        }
        logger.info(f"register_lead: payload={payload}")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token_auth}",
        }
        logger.info(f"register_lead: headers={headers}")

        try:
            response = req.post(url, json=payload, headers=headers, timeout=10)
            logger.info(f"register_lead: response.status_code={response.status_code}")
            logger.info(f"register_lead: response.text={response.text}")
        except Exception as e:
            logger.error(f"register_lead: Exception during request: {e}")
            return None

        if response.status_code == 200:
            lead = response.json().get("content", {}).get("lead")
            logger.info(f"register_lead: lead={lead}")
            return lead
        else:
            logger.warning(f"register_lead: Failed with status {response.status_code}")
            return None

    def convert_lead(
        self,
        id_lead: str,
    ) -> dict | None:
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info(
            f"convert_lead: id_empresa_={id_empresa_}, cod_empresa={cod_empresa}"
        )

        base_url = self.get_url("admMsUrl", id_empresa_)
        url = f"{base_url}/v1/cliente/lead"
        logger.info(f"convert_lead: url={url}")

        token_auth = self.get_token_auth(id_empresa_)
        logger.info(f"convert_lead: token_auth={token_auth}")

        payload = {
            "codigoLead": id_lead,
            "empresa": cod_empresa,
        }
        logger.info(f"convert_lead: payload={payload}")

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token_auth}",
        }
        logger.info(f"convert_lead: headers={headers}")

        try:
            response = req.post(url, json=payload, headers=headers, timeout=10)
            logger.info(f"convert_lead: response.status_code={response.status_code}")
            logger.info(f"convert_lead: response.text={response.text}")
        except Exception as e:
            logger.error(f"convert_lead: Exception during request: {e}")
            return None

        if response.status_code == 200:
            json_response = response.json()
            logger.info(f"convert_lead: json_response={json_response}")
            codigo = json_response.get("content", {}).get("codigo", None)
            logger.info(f"convert_lead: codigo={codigo}")
            if context := self.get_user_by_id(codigo):
                logger.info(f"convert_lead: context={context}")
                return context
            else:
                logger.warning(f"convert_lead: No context found for codigo={codigo}")
                return None
        else:
            logger.warning(f"convert_lead: Failed with status {response.status_code}")
            return None
