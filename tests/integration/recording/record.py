import datetime
import os
import time
from multiprocessing import Pool

from conn import redis_client
from playwright.sync_api import sync_playwright
from run_tests import get_next_message, is_running

WEB_SERVER_PORT = os.getenv("WEB_SERVER_PORT", "8042")
WEB_SERVER_HOST = os.getenv("WEB_SERVER_HOST", "localhost")
MAX_SIMULTANEOUS_TESTS = int(os.getenv("MAX_SIMULTANEOUS_TESTS", "1"))


def log(msg):
    print(msg.text)


def run_test(test_name):
    print("Aguardando para iniciar a gravação do teste...", flush=True)

    while not is_running(test_name):
        continue

    time.sleep(2)

    print(f"A gravação do teste {test_name} irá iniciar", flush=True)

    with sync_playwright() as p:
        # Configurar o navegador em modo headless com gravação de vídeo
        browser = p.chromium.launch(headless=True)
        context = browser.new_context(
            record_video_dir="videos/",
            record_video_size={"width": 1920, "height": 1080},
        )
        page = context.new_page()
        page.set_viewport_size({"width": 1920, "height": 1080})

        try:
            page.on("console", log)

            # Acessar a página
            base_path = f"http://{WEB_SERVER_HOST}:{WEB_SERVER_PORT}/chat"
            url_args = f"?test_name={test_name}&user=tester"

            page.goto(f"{base_path}{url_args}")

            def send_message(message):
                nonlocal page
                # Espera carregar as novas mensagens
                time.sleep(2)
                page.fill("#message", message)
                time.sleep(0.5)
                page.click("#sendMessageButton")

            while True:
                message = get_next_message(test_name)
                if message and message != "WAIT" and message != "FINISH":
                    send_message(message)
                    print(f"Mensagem enviada: {message}")
                elif message == "WAIT":
                    # Esperar por uma nova mensagem
                    print("Aguardando nova mensagem...")
                elif message == "FINISH":
                    # Finalizar o teste
                    print("Teste finalizado.")
                    break
                else:
                    # Finalizar o teste
                    print("Nenhuma nova mensagem encontrada.")
                    break

            redis_client.set(f"tests:{test_name}:finished", "1")

            time.sleep(10)

            print("Teste concluído.", flush=True)

        except Exception as e:
            print(f"Erro durante o teste: {e}", flush=True)

        finally:
            context.close()
            browser.close()

            redis_client.lpush(f"tests:terminate:{test_name}", "1")

            old_path = page.video.path()

            now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            new_path = os.path.join(
                os.path.dirname(old_path), f"{test_name}_{now}.webm"
            )

            os.rename(old_path, new_path)
            print(f"Vídeo salvo em: {new_path}", flush=True)


if __name__ == "__main__":
    pool = Pool(processes=MAX_SIMULTANEOUS_TESTS)
    while True:
        # TODO: Permitir paralelização de testes
        test_name = redis_client.brpop("tests:queued", 0)[1]
        # run_test(test_name)
        pool.apply_async(run_test, args=(test_name,))
        print(f"Teste {test_name} iniciado.", flush=True)
