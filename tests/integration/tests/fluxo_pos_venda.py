import logging
import requests

from tests.engine import TestSuite, TestMessage, AdditionalStep
from tests.utils import BASE_API_URL, check_for_plan_url, delete_context

logger = logging.getLogger("__main__")


def post_assertion_tests(test_suite: TestSuite):
    """
    Função de verificação pós-teste.

    :raise:
        AssertionError: Se alguma verificação falhar.
    """
    return
    for message in test_suite.test_messages:
        if message.response is None:
            assert False, f"Mensagem sem resposta: {message.message}"

    assert True, "Todas as mensagens foram respondidas corretamente."

    # Verificar que o aluno comprou o plano
    url = f"{BASE_API_URL}/consultar_contexto_aluno/"
    params = {"empresa": test_suite.id_empresa, "telefone": test_suite.phone}
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        situacao = data.get("aluno", {}).get("situacao")
        assert situacao == "AT", f"Situação do aluno não é 'AT': {situacao}"
    else:
        assert False, (
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )

    assert True, "Verificações pós-teste concluídas com sucesso."


test_messages = [
    TestMessage("Olá, quais planos você tem na academia?"),
    TestMessage("Eu não sou aluno!"),
    TestMessage("Qual o plano mais barato?"),
    TestMessage(
        "Qual o link de compra do plano?", AdditionalStep(check_for_plan_url, "after")
    ),
    TestMessage("Ok, vou comprar o plano."),
]

test_name = "Teste de Fluxo pos Venda"


def test_suite():
    """
    Configuração do teste
    - Mensagens: Breve conversa solicitando o plano mais barato e o link de compra
    - Respostas: O bot deve responder com o link de compra do plano
    - Verificação: O bot deve responder com o link de compra do plano
    - Passo adicional: Verifica se o link de compra foi enviado
    - Verificação pós-teste: O aluno deve ter comprado o plano e estar com a situação "AT"
    - Após o fim das mensagens, o teste espera que seja enviada uma
      mensagem adicional felicitando o aluno pela compra do plano.
    """
    return TestSuite(
        test_name,
        test_messages,
        post_assertions=post_assertion_tests,
        needs_further_message=False,
        chain_mode=True,
        setup_func=delete_context,
    )
