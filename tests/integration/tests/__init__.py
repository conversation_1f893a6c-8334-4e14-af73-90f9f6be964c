# Fluxos genéricos (Mais como exemplo)
from tests.fluxo_pos_venda import test_suite as test_suite_fluxo_pos_venda
from tests.aula_experimental import test_suite as test_suite_aula_experimental

# Fluxos pré venda - Modo Rede
from tests.fluxos_pre_venda_rede import pre_venda_rede_tests

# Fluxos pré venda - Modo Normal
from tests.fluxos_pre_venda_normal import pre_venda_normal_tests

# Fluxos pós venda - Modo Rede
from tests.fluxos_pos_venda_rede import pos_venda_rede_tests

# Fluxos pós venda - Modo Normal
from tests.fluxos_pos_venda_normal import pos_venda_normal_tests

__all__ = ["tests"]

tests = {
    **pre_venda_rede_tests,
    **pre_venda_normal_tests,
    **pos_venda_rede_tests,
    **pos_venda_normal_tests,
    test_suite_fluxo_pos_venda().test_name: test_suite_fluxo_pos_venda,
    test_suite_aula_experimental().test_name: test_suite_aula_experimental,
}
