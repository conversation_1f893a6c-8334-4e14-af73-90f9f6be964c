"""Módulo para utilidades de LLM para os testes"""

import json
import logging
import os

from openai import OpenAI
from openai.types.chat import ChatCompletion

from .engine import TestMessage

logger = logging.getLogger("__main__")

current_dir = os.path.dirname(__file__)
keys_path = os.path.join(current_dir, "keys.json")

with open(keys_path) as f:
    keys = json.load(f)
    OPENAI_API_KEY = keys["OPEN_AI_API_KEY"]


def get_client() -> OpenAI:
    return OpenAI(api_key=OPENAI_API_KEY)


def get_response(
    messages, tools=[], system_prompt="", tool_choice="none"
) -> ChatCompletion:
    client = get_client()
    chat_messages = [{"role": "system", "content": system_prompt}]
    if messages:
        chat_messages.extend(messages)
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=chat_messages,
        tools=tools,
        tool_choice=tool_choice,
    )
    return response


def validate(message: TestMessage, system_prompt: str) -> bool:
    if not message.response:
        return False

    validate_tool_dict = {
        "type": "function",
        "function": {
            "name": "validate",
            "description": "Retorna True se a resposta está correta, False caso contrário.",
            "parameters": {
                "type": "object",
                "properties": {
                    "result": {
                        "type": "boolean",
                        "description": "True se a resposta está correta, False caso contrário.",
                    }
                },
                "required": ["result"],
            },
        },
    }

    prompt = (
        "Verifique para esta interação:\n"
        f"Mensagem: {message.message}\nResposta: {message.response}"
    )

    response = get_response(
        messages=[{"role": "user", "content": prompt}],
        tools=[validate_tool_dict],
        system_prompt=system_prompt,
        tool_choice="required",
    )

    if not response.choices[0].message.tool_calls:
        return False

    args = response.choices[0].message.tool_calls[0].function.arguments
    try:
        args: dict = json.loads(args)
    except json.JSONDecodeError:
        return False

    return args.get("result", False)
