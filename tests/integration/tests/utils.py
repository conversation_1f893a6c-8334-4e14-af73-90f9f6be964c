import logging
import os
import re
import time

import requests
from faker import Faker

logger = logging.getLogger("__main__")
faker = Faker("pt_BR")

BASE_API_URL = os.getenv("BASE_API_URL", "http://localhost:8300")


def delete_context(test_suite):
    """Delete user context from Redis"""
    if not hasattr(test_suite, "phone"):
        raise TypeError("TestSuite should have a phone attribute!")
    try:
        url = f"{BASE_API_URL}/apagar_contexto_aluno/"
        params = {
            "empresa": test_suite.id_empresa,
            "telefone": test_suite.phone,
            "reset_chat": True,
        }
        response = requests.delete(url, params=params)
        if response.status_code == 200:
            logger.info("User context deleted successfully.")
        else:
            logger.error(
                f"Failed to delete user context: {response.status_code} - {response.text}"
            )
    except requests.RequestException as e:
        logger.error(f"Error deleting user context: {e}")


def check_for_plan_url(message) -> str | None:
    """
    Verifica se o link de compra do plano foi enviado.
    """
    if not hasattr(message, "response"):
        raise TypeError("Message should be of type TestMessage!")

    pattern = r"(https?://[^\s]+)"
    match = re.search(pattern, message.response)
    if match:
        url = match.group(0)
        logger.info(f"Link de compra encontrado: {url}")
        return url
    return None


def enable_cpf(test):
    id_empresa = test.id_empresa
    url = f"{BASE_API_URL}/configuracao"
    
    params = {
        "empresa": id_empresa
    }
    
    payload = {
        "config": {
            "desabilitarAulaExperimental": False,
            "desabilitarCPF": False,
            "desabilitarLigacao": False,
            "desabilitarPlanos": False,
            "desabilitarProdutos": False,
            "desabilitarTurmas": False,
            "desabilitarVisita": False
        } 
    }
    
    response = requests.post(url, params=params, json=payload)
    
    if response.status_code != 201:
        logger.error("Houve uma falha ao criar a configuração de desabilitar o CPF!")
    
    time.sleep(5)


def disable_cpf(test):
    id_empresa = test.id_empresa
    url = f"{BASE_API_URL}/configuracao"
    
    params = {
        "empresa": id_empresa
    }
    
    payload = {
        "config": {
            "desabilitarAulaExperimental": False,
            "desabilitarCPF": True,
            "desabilitarLigacao": False,
            "desabilitarPlanos": False,
            "desabilitarProdutos": False,
            "desabilitarTurmas": False,
            "desabilitarVisita": False
        } 
    }
    
    response = requests.post(url, params=params, json=payload)
    
    if response.status_code != 201:
        logger.error("Houve uma falha ao criar a configuração de desabilitar o CPF!")
    
    time.sleep(5)
