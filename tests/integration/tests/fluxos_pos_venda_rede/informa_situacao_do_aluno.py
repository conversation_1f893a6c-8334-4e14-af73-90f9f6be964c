import logging

import requests

from tests.engine import TestSuite, TestMessage, AdditionalStep
from tests.utils import BASE_API_URL, faker
from tests.llm import validate
from tools.pacto import PactoIntegrationTools

logger = logging.getLogger("__main__")

test_name = "Teste Informa Situacao do Aluno - Rede"


def add_situation_verification_flag(message):
    """
    Adiciona aos metadados da mensagem uma flag de que ela deve ser verificada
    """
    message.add_metadata(
        "verify_situation",
        "Você deverá verificar se o assistente informou corretamente a situação de visitante (VI) do aluno.",
    )


test_messages = [
    TestMessage("Olá!"),
    TestMessage(
        "Qual a minha situação?",
        additional_step=AdditionalStep(
            step=add_situation_verification_flag, moment="after"
        ),
    ),
    TestMessage("Ok, obrigado"),
]


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "mensagens_tem_resposta": {
            "name": "Todas as mensagens devem ter resposta",
            "value": True,
            "message": "Todas as mensagens têm resposta",
        },
        "situacao_aluno": {
            "name": "Situação do aluno deve ser 'VI'",
            "value": True,
            "message": "Situação do aluno é 'VI'",
        },
        "informa_situacao": {
            "name": "A IA deverá informar que o aluno é visitante.",
            "value": True,
            "message": "A IA informou corretamente a situação.",
        }
    }
    for message in test_suite.test_messages:
        if message.response is None:
            result["mensagens_tem_resposta"]["value"] = False
            result["mensagens_tem_resposta"]["message"] = (
                "Nem todas as mensagens tem resposta!"
            )

    url = f"{BASE_API_URL}/consultar_contexto_aluno/"
    phone = test_suite.phone
    id_empresa = test_suite.id_empresa
    params = {
        "empresa": id_empresa,
        "telefone": phone,
    }

    response = requests.get(url, params=params)

    logger.info(f"Verificação de situação do aluno: {response}")

    if response.status_code == 200:
        try:
            data = response.json()
            logger.info(f"Dados retornados do contexto: {data} ({type(data)})")
        except Exception as e:
            logger.error(f"Erro ao converter JSON: {e}")
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = (
                f"Erro ao parsear JSON da resposta: {e}"
            )
            return result

        if isinstance(data, list):
            situacao = data[0].get("aluno", {}).get("situacao", {})
            if isinstance(situacao, dict):
                situacao = situacao.get("codigo")
            if situacao != "VI":
                result["situacao_aluno"]["value"] = False
                result["situacao_aluno"]["message"] = f"Situação do aluno: {situacao}"
                logger.error(f"Situação do aluno: {situacao}")
        else:
            logger.error(f"Formato inesperado de resposta: {data}")
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = (
                f"Resposta inesperada da API, dado é do tipo: {type(data)}"
            )
    else:
        result["situacao_aluno"]["value"] = False
        result["situacao_aluno"]["message"] = (
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )
        logger.error(
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )

    for message in test_suite.test_messages:
        if msg := message.metadata.get("verify_situation"):
            is_valid = validate(message, msg)
            if not is_valid:
                result["informa_situacao"]["value"] = False
                result["informa_situacao"]["message"] = (
                    f"A IA não informou os dados, resposta: {message.response}"
                )
    return result


def setup_func(test_suite: TestSuite) -> bool:
    id_empresa = test_suite.id_empresa
    phone = test_suite.phone.removeprefix("+55")
    cpf = faker.cpf()
    name = faker.name()
    pit = PactoIntegrationTools(id_empresa=id_empresa)
    lead_id = pit.register_lead(name, phone, cpf)
    if not lead_id:
        logger.warning("Não foi possível registrar o lead!")
        return False
    user_data = pit.convert_lead(lead_id)
    if not user_data:
        logger.warning("Não foi possível converter o lead!")
        return False

    return True


def test_suite():
    """
    Teste para verificar a informação a situação do aluno.
    """
    suite = TestSuite(
        test_name,
        test_messages,
        post_assertion_tests,
        setup_func=setup_func,
        chain_mode=True
    )
    return suite
