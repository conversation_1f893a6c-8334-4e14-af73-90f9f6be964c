from tests.fluxos_pos_venda_normal.localiza_aluno_por_cpf import (
    test_suite as test_suite_localiza_aluno_por_cpf,
)
from tests.fluxos_pos_venda_normal.localiza_aluno_por_telefone import (
    test_suite as test_suite_localiza_aluno_por_telefone,
)
from tests.fluxos_pos_venda_normal.informa_situacao_do_aluno import (
    test_suite as test_suite_informa_situacao_do_aluno,
)

__all__ = ["pos_venda_normal_tests"]

pos_venda_normal_tests = {
    test_suite_localiza_aluno_por_cpf().test_name: test_suite_localiza_aluno_por_cpf,
    test_suite_localiza_aluno_por_telefone().test_name: test_suite_localiza_aluno_por_telefone,
    test_suite_informa_situacao_do_aluno().test_name: test_suite_informa_situacao_do_aluno
}
