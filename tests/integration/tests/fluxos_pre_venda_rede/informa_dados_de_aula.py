from tests.engine import TestMessage, AdditionalStep, TestSuite
from tests.llm import validate


def add_classes_verification_flag(message):
    """
    Adiciona aos metadados da mensagem uma flag de que ela deve ser verificada
    """
    message.add_metadata(
        "verify_classes",
        "Você deverá verificar se o assistente informou sobre as aulas solicitadas.",
    )


test_name = "Teste Informa Dados De Aula - Rede"

test_messages = [
    TestMessage("Olá, quais unidades tem aí?"),
    TestMessage("Não sou aluno."),
    TestMessage("Goiânia, Goiás."),
    TestMessage("Gostaria de conhecer essa sim!"),
    TestMessage(
        "Pode me passar quais aulas tem amanhã entre as 14h e as 16h?",
        additional_step=AdditionalStep(
            step=add_classes_verification_flag, moment="after"
        ),
    ),
    TestMessage("Ok, obrigado"),
]


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "aulas": {
            "name": "A IA deverá informar os dados das aulas.",
            "value": True,
            "message": "A IA informou corretamente os dados das aulas.",
        }
    }

    for message in test_suite.test_messages:
        if msg := message.metadata.get("verify_classes"):
            is_valid = validate(message, msg)
            if not is_valid:
                result["aulas"]["value"] = False
                result["aulas"]["message"] = (
                    f"A IA não informou os dados, resposta: {message.response}"
                )

    return result


def test_suite():
    """
    Teste para verificar a informação de dados de aula para um lead.
    """
    return TestSuite(test_name, test_messages, post_assertion_tests, chain_mode=True)
