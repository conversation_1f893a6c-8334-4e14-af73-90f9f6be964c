from tests.engine import TestMessage, AdditionalStep, TestSuite
from tests.llm import validate


def add_plans_verification_flag(message):
    """
    Adiciona aos metadados da mensagem uma flag de que ela deve ser verificada
    """
    message.add_metadata(
        "verify_plans",
        "Você deverá verificar se o assistente informou sobre os planos solicitados.",
    )


test_name = "Teste Informa Dados De Planos - Rede"

test_messages = [
    TestMessage("Olá, quais unidades tem aí?"),
    TestMessage("Não sou aluno."),
    TestMessage("Goiânia, Goiás."),
    TestMessage("Gostaria de conhecer essa sim!"),
    TestMessage("Pode me passar quais planos tem disponíveis?"),
    TestMessage(
        "Só quero saber os planos.",
        additional_step=AdditionalStep(
            step=add_plans_verification_flag, moment="after"
        ),
    ),
    TestMessage("Ok, obrigado"),
]


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "planos": {
            "name": "A IA deverá informar os dados dos planos.",
            "value": True,
            "message": "A IA informou corretamente os dados dos planos.",
        }
    }

    for message in test_suite.test_messages:
        if msg := message.metadata.get("verify_plans"):
            is_valid = validate(message, msg)
            if not is_valid:
                result["planos"]["value"] = False
                result["planos"]["message"] = (
                    f"A IA não informou os dados, resposta: {message.response}"
                )

    return result


def test_suite():
    """
    Teste para verificar a informação de dados de planos para um lead.
    """
    return TestSuite(test_name, test_messages, post_assertion_tests, chain_mode=True)
