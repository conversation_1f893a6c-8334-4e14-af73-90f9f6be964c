from tests.engine import TestMessage, AdditionalStep, TestSuite
from tests.llm import validate


def add_products_verification_flag(message):
    """
    Adiciona aos metadados da mensagem uma flag de que ela deve ser verificada
    """
    message.add_metadata(
        "verify_products",
        "Você deverá verificar se o assistente informou sobre os produtos solicitados.",
    )


test_name = "Teste Informa Dados De Produtos - Rede"

test_messages = [
    TestMessage("Olá, quais unidades tem aí?"),
    TestMessage("Não sou aluno."),
    TestMessage("Goiânia, Goiás."),
    TestMessage("Gostaria de conhecer essa sim!"),
    TestMessage(
        "Pode me passar quais produtos tem disponíveis?",
        additional_step=AdditionalStep(
            step=add_products_verification_flag, moment="after"
        ),
    ),
    TestMessage("Ok, obrigado"),
]


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "produtos": {
            "name": "A IA deverá informar os dados dos produtos.",
            "value": True,
            "message": "A IA informou corretamente os dados dos produtos.",
        }
    }

    for message in test_suite.test_messages:
        if msg := message.metadata.get("verify_products"):
            is_valid = validate(message, msg)
            if not is_valid:
                result["produtos"]["value"] = False
                result["produtos"]["message"] = (
                    f"A IA não informou os dados, resposta: {message.response}"
                )

    return result


def test_suite():
    """
    Teste para verificar a informação de dados de produtos para um lead.
    """
    return TestSuite(test_name, test_messages, post_assertion_tests, chain_mode=True)
