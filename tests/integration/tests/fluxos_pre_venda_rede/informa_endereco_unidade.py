from tests.engine import TestMessage, AdditionalStep, TestSuite
from tests.llm import validate


def add_address_verification_flag(message):
    """
    Adiciona aos metadados da mensagem uma flag de que ela deve ser verificada
    """
    message.add_metadata(
        "verify_address",
        "Você deverá verificar se o assistente "
        "informou o endereço solcitado na conversa.",
    )


test_name = "Teste Informa o endereço da unidade - Rede"

test_messages = [
    TestMessage("Olá, quais unidades tem aí?"),
    TestMessage("Não sou aluno."),
    TestMessage("Goiânia, Goiás."),
    TestMessage("Gostaria de conhecer essa sim!"),
    TestMessage(
        "Pode me passar o endereço da academia?",
        additional_step=AdditionalStep(
            step=add_address_verification_flag, moment="after"
        ),
    ),
    TestMessage("Ok, obrigado"),
]


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "endereco": {
            "name": "A IA deverá informar o endereço da Unidade.",
            "value": True,
            "message": "A IA informou corretamente o endereço da Unidade",
        }
    }

    for message in test_suite.test_messages:
        if msg := message.metadata.get("verify_address"):
            is_valid = validate(message, msg)
            if not is_valid:
                result["endereco"]["value"] = False
                result["endereco"]["message"] = (
                    f"A IA não informou o endereço da Unidade, resposta: {message.response}"
                )

    return result


def test_suite():
    """
    Configuração do teste
    - Mensagens: Solicitação do endereço.
    - Respostas: O Conversas deve retornar o endereço ao usuário do teste.
    - Verificação: O endereço deve ser o correto.
    """
    return TestSuite(test_name, test_messages, post_assertion_tests, chain_mode=True)
