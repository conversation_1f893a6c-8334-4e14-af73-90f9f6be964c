from tests.fluxos_pre_venda_rede.cadastro_novo_lead import (
    test_suite as test_suite_cadastro_novo_lead_rede,
)
from tests.fluxos_pre_venda_rede.informa_endereco_unidade import (
    test_suite as test_suite_informa_endereco_unidade_rede,
)
from tests.fluxos_pre_venda_rede.informa_dados_de_produtos import (
    test_suite as test_suite_informa_dados_de_produtos_rede,
)
from tests.fluxos_pre_venda_rede.informa_dados_de_aula import (
    test_suite as test_suite_informa_dados_de_aula_rede,
)
from tests.fluxos_pre_venda_rede.informa_dados_de_planos import (
    test_suite as test_suite_informa_dados_de_planos_rede,
)
from tests.fluxos_pre_venda_rede.notificacao_de_agendamento_de_aula_experimental import (
    test_suite as test_suite_notificacao_de_agendamento_de_aula_experimental_rede,
)
from tests.fluxos_pre_venda_rede.cadastro_visitante import (
    test_suite as test_suite_cadastro_visitante_rede,
)
from tests.fluxos_pre_venda_rede.agendamento_aula_experimental import (
    test_suite as test_suite_agendamento_aula_experimental_rede,
)
from tests.fluxos_pre_venda_rede.lead_solicita_agendamento_ligacao import (
    test_suite as test_suite_lead_solicita_agendamento_ligacao_rede,
)

__all__ = ["pre_venda_rede_tests"]

pre_venda_rede_tests = {
    test_suite_cadastro_novo_lead_rede().test_name: test_suite_cadastro_novo_lead_rede,
    test_suite_informa_endereco_unidade_rede().test_name: test_suite_informa_endereco_unidade_rede,
    test_suite_informa_dados_de_produtos_rede().test_name: test_suite_informa_dados_de_produtos_rede,
    test_suite_informa_dados_de_aula_rede().test_name: test_suite_informa_dados_de_aula_rede,
    test_suite_informa_dados_de_planos_rede().test_name: test_suite_informa_dados_de_planos_rede,
    test_suite_notificacao_de_agendamento_de_aula_experimental_rede().test_name: test_suite_notificacao_de_agendamento_de_aula_experimental_rede,
    test_suite_cadastro_visitante_rede().test_name: test_suite_cadastro_visitante_rede,
    test_suite_agendamento_aula_experimental_rede().test_name: test_suite_agendamento_aula_experimental_rede,
    test_suite_lead_solicita_agendamento_ligacao_rede().test_name: test_suite_lead_solicita_agendamento_ligacao_rede,
}
