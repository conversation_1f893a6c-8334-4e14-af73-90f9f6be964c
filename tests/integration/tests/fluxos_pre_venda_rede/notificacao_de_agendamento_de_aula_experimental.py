import json
import logging
import os
import requests
import time

from conn import redis_client
from tests.engine import TestSuite, TestMessage
from tests.utils import BASE_API_URL, faker, enable_cpf

HOST = os.getenv("WEB_SERVER_HOST", "localhost")
PORT = os.getenv("WEB_SERVER_PORT", "8042")

logger = logging.getLogger("__main__")

test_name = "Teste Notificacao De Agendamento De Aula Experimental - Rede"

test_messages = [
    TestMessage("Olá, quais unidades tem aí?"),
    TestMessage("Não sou aluno."),
    TestMessage("Goiânia, Goiás."),
    TestMessage("Gostaria de conhecer essa sim!"),
    TestMessage("Sim, quero agendar uma aula experimental para amanhã de tarde."),
    TestMessage("Quero este último."),
    TestMessage("Claro, pode ser nesse horário."),
    TestMessage(f"CPF: {faker.cpf()}, Nome completo: {faker.name()}"),
    TestMessage("Pode agendar a aula experimental para amanhã de tarde."),
    TestMessage("Sim, neste horário está ótimo."),
    TestMessage("Por enquanto é só isso, obrigado."),
]


def setup_func(test_suite: TestSuite):
    """
    Cadastra agendamento especificando que deve ser mandada
    mensagem 1 minuto após agendamento de aula.
    """
    enable_cpf(test_suite)
    url = f"{BASE_API_URL}/v2/scheduler/book_class"
    params = {"empresa": test_suite.id_empresa}
    body = {
        "scheduler_text": "Quero que o cliente seja notificado 30s depois de agendar a aula experimental, fale: `Agendamento recebido!`"
    }
    requests.post(url, params=params, json=body)
    redis_client.set(
        f"tests:attributes_by_info:{test_suite.id_empresa}:{test_suite.phone}",
        json.dumps({
            "test_name": test_suite.test_name,
            "webhook_url": f"http://{HOST}:{PORT}"
        })
    )
    # Aguardar o processamento fazer efeito...
    time.sleep(10)


def post_assertion_tests(test_suite: TestSuite):
    result = {
        "mensagens_tem_resposta": {
            "name": "Todas as mensagens devem ter resposta",
            "value": True,
            "message": "Todas as mensagens têm resposta",
        },
        "situacao_aluno": {
            "name": "Situação do aluno deve ser 'VI'",
            "value": True,
            "message": "Situação do aluno é 'VI'",
        },
        "aluno_recebeu_notificacao": {
            "name": "Aluno deverá receber uma notificação após o agendamento",
            "value": True,
            "message": "Aluno recebeu a notificação",
        },
    }
    for message in test_suite.test_messages:
        if message.response is None:
            result["mensagens_tem_resposta"]["value"] = False
            result["mensagens_tem_resposta"]["message"] = (
                "Nem todas as mensagens tem resposta!"
            )

    url = f"{BASE_API_URL}/consultar_contexto_aluno/"
    phone = test_suite.phone
    id_empresa = test_suite.id_empresa
    params = {
        "empresa": id_empresa,
        "telefone": phone,
    }

    response = requests.get(url, params=params)

    logger.info(f"Verificação de situação do aluno: {response}")

    if response.status_code == 200:
        try:
            data = response.json()
            logger.info(f"Dados retornados do contexto: {data} ({type(data)})")
        except Exception as e:
            logger.error(f"Erro ao converter JSON: {e}")
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = (
                f"Erro ao parsear JSON da resposta: {e}"
            )
            return result

        if isinstance(data, list):
            situacao = data[0].get("aluno", {}).get("situacao", {})
            if isinstance(situacao, dict):
                situacao = situacao.get("codigo")
            if situacao != "VI":
                result["situacao_aluno"]["value"] = False
                result["situacao_aluno"]["message"] = f"Situação do aluno: {situacao}"
                logger.error(f"Situação do aluno: {situacao}")
        else:
            logger.error(f"Formato inesperado de resposta: {data}")
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = (
                f"Resposta inesperada da API, dado é do tipo: {type(data)}"
            )
    else:
        result["situacao_aluno"]["value"] = False
        result["situacao_aluno"]["message"] = (
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )
        logger.error(
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )

    if len(test_suite.metadata.get("extra_responses")) == 0:
        result["aluno_recebeu_notificacao"]["value"] = False
        result["aluno_recebeu_notificacao"]["message"] = (
            "O aluno não recebeu nenhuma mensagem após o agendamento..."
        )

    return result


def test_suite():
    """
    Teste para verificar a notificação de agendamento de aula experimental.
    """
    return TestSuite(
        test_name,
        test_messages,
        post_assertion_tests,
        needs_further_message=True,
        chain_mode=True,
        setup_func=setup_func,
    )
