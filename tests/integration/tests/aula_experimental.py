import logging

import requests

from tests.engine import TestSuite, TestMessage
from tests.utils import BASE_API_URL, delete_context, faker

logger = logging.getLogger("__main__")


def post_assertion_tests(test_suite: TestSuite) -> dict:
    """
    Função de verificação pós-teste.

    :raise:
        AssertionError: Se alguma verificação falhar.
    """

    result = {
        "mensagens_tem_resposta": {
            "name": "Todas as mensagens devem ter resposta",
            "value": True,
            "message": "Todas as mensagens têm resposta",
        },
        "situacao_aluno": {
            "name": "Situação do aluno deve ser 'VI'",
            "value": True,
            "message": "Situação do aluno é 'VI'",
        },
    }

    for message in test_suite.test_messages:
        if message.response is None:
            result["mensagens_tem_resposta"]["value"] = False
            result["mensagens_tem_resposta"]["message"] = (
                "Nem todas as mensagens tem resposta!"
            )

    # Verificar que o aluno comprou o plano
    url = f"{BASE_API_URL}/consultar_contexto_aluno/"
    phone = test_suite.phone
    id_empresa = test_suite.id_empresa
    params = {
        "empresa": id_empresa,
        "telefone": phone,
    }
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        situacao = data[0].get("aluno", {}).get("situacao", {})
        if isinstance(situacao, dict):
            situacao = situacao.get("codigo")
        if situacao != "VI":
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = f"Situação do aluno: {situacao}"
            logger.error(f"Situação do aluno: {situacao}")
    else:
        result["situacao_aluno"]["value"] = False
        result["situacao_aluno"]["message"] = (
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )
        logger.error(
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )

    # Verificar se o aluno tem aula agendada
    # Não implementado
    return result


test_messages = [
    TestMessage("Olá, eu gostaria de agendar uma aula experimental!"),
    TestMessage("Eu não sou aluno!"),
    TestMessage("Quero a aula experimental para amanhã de tarde..."),
    TestMessage("Pode ser a aula mais tarde, **que termine antes das 18h**"),
    TestMessage(f"CPF: {faker.cpf()}, Nome completo: {faker.name()}"),
    TestMessage("Show, pode prosseguir com o agendamento!"),
]

test_name = "Teste Agendamento de Aula Experimental"


def test_suite():
    """
    Configuração do teste
    - Mensagens: Breve conversa solicitando o agendamento de aula experimental
    - Respostas: O bot deve solicitar nome completo e CPF
    - Verificação: Foi chamada a função de verificação do CPF
    - Verificação: Foram chamadas as funções de:
        - cadastro de lead,
        - conversão de lead,
        - busca por id e
        - agendamento
    - Verificação pós-teste: O aluno deve estar como "VI" e deve ter a aula agendada
    """

    return TestSuite(
        test_name, test_messages, post_assertion_tests, False, delete_context
    )
