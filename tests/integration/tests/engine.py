"""Testes de integração para o Conversas.AI"""

import json
import logging
import os
import threading
from collections.abc import Callable
from typing import Literal

from conn import redis_client
from tests.utils import faker

logger = logging.getLogger("__main__")


BASE_API_URL = os.getenv("BASE_API_URL", "http://localhost:8300")
TEST_ID_EMPRESA = os.getenv("TEST_ID_EMPRESA", "teste-1")
TEST_ID_EMPRESA_REDE = os.getenv(
    "TEST_ID_EMPRESA_REDE", "teste-1"
)


class TestException(Exception):
    def __init__(self, msg):
        super().__init__(msg)


class ConfigurationException(TestException):
    pass


class TestResults:
    """
    Classe que representa os resultados de um teste.

    Attributes:
        test_name (str): Nome do teste.
        asserts (list): Lista de asserções realizadas.
        messages (list): Lista de mensagens enviadas.
        responses (list): Lista de respostas recebidas.
    """

    def __init__(self, results: dict):
        """
        Inicializa a classe TestResults.
        Args:
            results (dict): Dicionário com os resultados do teste.
        """
        self.test_name = results.get("test_name")
        self.asserts = results.get("asserts", [])
        self.messages = results.get("messages", [])
        self.responses = results.get("responses", [])


class AdditionalStep:
    """
    Classe que representa um passo adicional a ser executado após o envio de uma mensagem.

    Attributes:
        step (Callable): Função a ser chamada após o envio.
        moment (Literal["before", "after"]): Momento em que a função deve ser chamada.
                                             Pode ser antes de receber a resposta ou depois.
    """

    def __init__(self, step: Callable, moment: Literal["before", "after"] = "after"):
        """
        Inicializa a classe AdditionalStep.
        Args:
            step (Callable): Função a ser chamada após o envio.
            moment (Literal["before", "after"]): Momento em que a função deve ser chamada.
                                                 Pode ser antes de receber a resposta ou depois."""
        self.step = step
        self.moment = moment


class TestMessage:
    """
    Classe que representa um passo de teste.

    Attributes:
        message (str): Mensagem a ser enviada.
        response (str | None): Resposta à mensagem.
        additional_step (Callable): Função a ser chamada após o envio.
    """

    def __init__(self, message: str, additional_step: AdditionalStep = None):
        """
        Inicializa a classe TestMessage.
        Args:
            message (str): Mensagem a ser enviada.
            additional_step (Callable): Função a ser chamada após o envio.
        """
        self.message = message
        self.additional_step = additional_step
        self.response = None
        self.metadata = {}
        self.logs = []

    def add_metadata(self, name: str, data: any):
        """
        Adiciona metadados à mensagem.
        Args:
            name (str): Nome do dado.
            data (any): Dado a ser salvo.

        Raises:
            ConfigurationException: Caso seja usado um campo reservado.
        """
        self.metadata[name] = data

    def set_response(self, response: str):
        """
        Define a resposta à mensagem.

        Args:
            response (str): Resposta à mensagem.
        """
        self.response = response

    def add_log(self, log: dict):
        """
        Adiciona logs de funções e outras coisas à mensagem.

        Args:
            logs (str): Logs novos.
        """
        self.logs.append(log)


class TestSuite:
    """
    Classe de teste para o Conversas.AI.
    Esta classe é implementada semelhante a um padrão Singleton,
    garantindo que apenas uma instância da classe seja criada para cada teste.

    Attributes:
        test_name (str): Nome do teste.
        test_messages (list[TestMessage]): Lista de mensagens de teste.
        post_assertions (Callable): Função de verificação pós-teste.
        needs_further_message (bool): Indica se o teste precisa de mais mensagens.
        message_count (int): Contador de mensagens enviadas.
        timeout (int): Tempo limite para aguardar mais mensagens.
        setup_func (Callable): Função para ser chamada antes de rodarem os testes.
    Methods:
        get_next_message() -> str: Obtém a próxima mensagem a ser enviada.
        finish() -> list: Finaliza o teste, garantindo que todos os recursos sejam liberados.
    """

    _instances = {}

    def __init__(
        self,
        test_name: str,
        test_messages: list[TestMessage],
        post_assertions: Callable,
        needs_further_message: bool = False,
        setup_func: Callable | None = None,
        chain_mode: bool = False,
        metadata: dict = {},
    ):
        """
        Inicializa a classe de teste.

        Args:
            test_name (str): Nome do teste.
            test_messages (list[TestMessage]): Lista de mensagens de teste.
            post_assertions (Callable): Função de verificação pós-teste.
            test_name (str): Nome do teste.
            needs_further_message (bool): Indica se o teste precisa de mais mensagens.
            setup_func (Callable): Função para ser chamada antes de rodarem os testes.
            chain_mode (bool): Indica se o teste deve ser executado em modo rede.
        """
        self.test_name = "_".join(test_name.lower().split())
        name_in_instances = self.test_name in self._instances
        test_initialized = self._instances.get(self.test_name, {}).get(
            "initialized", False
        )
        # Isto é necessário pois o método __new__ é chamado antes do __init__
        # E o __init__ sempre é chamado, mesmo que a instância já exista
        if name_in_instances and test_initialized:
            return
        if chain_mode:
            self.id_empresa = TEST_ID_EMPRESA_REDE
            self.chain_mode = True
        else:
            self.id_empresa = TEST_ID_EMPRESA
            self.chain_mode = False
        self.phone = f"+{faker.msisdn()}"
        self.test_messages = test_messages
        self.post_assertions = post_assertions
        self.needs_further_message = needs_further_message
        self.message_count = 0
        self.timeout = 1000
        self.setup_func = setup_func

        reserved = ["extra_responses"]
        for key in metadata.keys():
            if key in reserved:
                raise ConfigurationException
        self.metadata = {
            "extra_responses": [],
            **metadata
        }

        self._instances[self.test_name]["initialized"] = True

    def __new__(cls, *args, **kwargs):
        """
        Implementa o padrão Singleton para garantir que apenas uma instância da classe seja criada.
        """
        if not isinstance(args[0], str):
            raise TypeError("O nome do teste deve ser uma string.")

        name = "_".join(args[0].lower().split())

        if name in cls._instances:
            return cls._instances[name]["instance"]
        cls._instances[name] = {
            "instance": super(TestSuite, cls).__new__(cls),
            "initialized": False,
        }
        return cls._instances[name]["instance"]

    def get_next_message(self) -> str:
        """
        Obtém a próxima mensagem a ser enviada para o Conversas.AI.

        :return:
            str: A próxima mensagem a ser enviada.
        """
        if self.message_count >= len(self.test_messages):
            logger.info("Todas as mensagens foram enviadas.")

            if self.needs_further_message and self.timeout > 0:
                logger.info("Aguardando mais mensagens...")
                self.timeout -= 1
                return "WAIT"
            del self
            return None

        next_message = self.test_messages[self.message_count].message

        # if self.test_messages[self.message_count].additional_step:
        #     step = self.test_messages[self.message_count].additional_step
        #     if step.moment == "before":
        #         step.step(self.test_messages[self.message_count])

        self.message_count += 1
        redis_client.delete(f"tests:{self.test_name}:ready")
        return next_message

    def finish(self):
        """
        Finaliza o teste, garantindo que todos os recursos sejam liberados.
        """
        logger.info("Finalizando o teste...")
        try:
            assertion_result = self.post_assertions(self)
        except Exception as e:
            assertion_result = None
            logger.error(f"Erro ao executar asserções pós-teste: {e}")

        redis_client.lpush(
            "tests:assertions",
            json.dumps(
                {
                    "test_name": self.test_name,
                    "assertions": assertion_result,
                }
            ),
        )

        logger.info("Tentando apagar as chaves de estado do Redis...")
        keys = redis_client.keys(f"tests:{self.test_name}:*")
        for key in keys:
            redis_client.delete(key)

        redis_client.hdel("tests:running", self.test_name)

        logger.info("Tentando mandar os resultados para o Redis...")

        result = {
            "test_name": self.test_name,
            "messages": [message.message for message in self.test_messages],
            "responses": [message.response for message in self.test_messages],
        }
        redis_client.hset("tests:results", self.test_name, json.dumps(result))
        termination_thread = threading.Thread(
            target=self.terminate,
            args=(),
            name=f"TerminationThread-{self.test_name}",
            daemon=True,
        )
        termination_thread.start()
        logger.info(
            f"Teste {self.test_name} finalizado, aguardando confirmação de terminação..."
        )

    def terminate(self):
        """
        Finaliza o teste, garantindo que todos os recursos sejam liberados.
        **APAGA A INSTÂNCIA DO TESTE**
        """
        can_terminate = redis_client.blpop(
            f"tests:terminate:{self.test_name}", timeout=100
        )
        if can_terminate:
            test_name = self.test_name
            try:
                del self._instances[test_name]
            except KeyError as e:
                logger.error(f"Erro ao apagar a instância do teste {test_name}: {e}")
                logger.error(f"Testes ativos: {self._instances.keys()}")
            del self

            logger.info(f"Teste {test_name} terminado com sucesso.")

    def add_response_to_last_message(self, response: str):
        """
        Adiciona a resposta à última mensagem enviada.

        Args:
            response (str): Resposta à mensagem.
        """
        last_message_without_response = None
        if self.message_count > 0:
            for message in self.test_messages:
                if message.response is None:
                    last_message_without_response = message
                    break
            if not last_message_without_response:
                self.metadata["extra_responses"].append(response)
                self.needs_further_message = False
            if last_message_without_response:
                logger.info(last_message_without_response.message)
                last_message_without_response.set_response(response)

                if last_message_without_response.additional_step:
                    step = last_message_without_response.additional_step
                    if step.moment == "after":
                        try:
                            step.step(last_message_without_response)
                        except:  # noqa
                            logger.error(
                                f"Erro ao executar o passo adicional: {step.step.__name__}"
                            )
                        logger.info(f"Passo adicional executado: {step.step.__name__}")

                logger.info(
                    f"Resposta adicionada à mensagem: {last_message_without_response.message}"
                )

    def add_log_to_last_message(self, log: dict):
        """
        Adiciona a resposta à última mensagem enviada.

        Args:
            response (str): Resposta à mensagem.
        """
        last_message_without_response = None
        if self.message_count > 0:
            for message in self.test_messages:
                if message.response is None:
                    last_message_without_response = message
                    break
            if last_message_without_response:
                last_message_without_response.add_log(log)

                logger.info(
                    f"Log adicionada à mensagem: {last_message_without_response.logs[-1]}"
                )
