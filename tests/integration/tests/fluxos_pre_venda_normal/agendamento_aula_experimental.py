import logging
import requests
import json

from tests.engine import TestSuite, TestMessage
from tests.utils import BASE_API_URL, faker, enable_cpf
from tools.pacto import PactoIntegrationTools as PIT
from datetime import datetime

logger = logging.getLogger("__main__")

def post_assertion_tests(test_suite: TestSuite):
    result = {
        "mensagens_tem_resposta": {
            "name": "Todas as mensagens devem ter resposta",
            "value": True,
            "message": "Todas as mensagens têm resposta",
        },
        "situacao_aluno": {
            "name": "Situação do aluno deve ser 'VI'",
            "value": True,
            "message": "Situação do aluno é 'VI'",
        },
        "aluno_agendado": {
            "name": "Aluno deve estar agendado",
            "value": True,
            "message": "Aluno está agendado"
        }
    }
    for message in test_suite.test_messages:
        if message.response is None:
            result["mensagens_tem_resposta"]["value"] = False
            result["mensagens_tem_resposta"]["message"] = (
                "Nem todas as mensagens tem resposta!"
            )

    url = f"{BASE_API_URL}/consultar_contexto_aluno/"
    phone = test_suite.phone
    id_empresa = test_suite.id_empresa
    params = {
        "empresa": id_empresa,
        "telefone": phone,
    }

    response = requests.get(url, params=params)

    logger.info(f"Verificação de situação do aluno: {response}")

    matricula = 0
    if response.status_code == 200:
        try:
            data = response.json()
            logger.info(f"Dados retornados do contexto: {data} ({type(data)})")
            
            if isinstance(data, list):
                situacao = data[0].get("aluno", {}).get("situacao", {})
                if isinstance(situacao, dict):
                    situacao = situacao.get("codigo")
                if situacao != "VI":
                    result["situacao_aluno"]["value"] = False
                    result["situacao_aluno"]["message"] = f"Situação do aluno: {situacao}"
                    logger.error(f"Situação do aluno: {situacao}")
                else:
                    #* Salva a matrícula do aluno para uso futuro.
                    matricula = data[0].get("aluno", {}).get("matricula", 0)
            else:
                logger.error(f"Formato inesperado de resposta: {data}")
                result["situacao_aluno"]["value"] = False
                result["situacao_aluno"]["message"] = (
                    f"Resposta inesperada da API, dado é do tipo: {type(data)}"
                )
        except Exception as e:
            logger.error(f"Erro ao converter JSON: {e}")
            result["situacao_aluno"]["value"] = False
            result["situacao_aluno"]["message"] = (
                f"Erro ao parsear JSON da resposta: {e}"
            )
    else:
        result["situacao_aluno"]["value"] = False
        result["situacao_aluno"]["message"] = (
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )
        logger.error(
            f"Erro ao consultar o contexto do aluno: {response.status_code} - {response.text}"
        )
    
    if matricula == 0:
        result["aluno_agendado"]["value"] = False
        result["aluno_agendado"]["message"] = "Aluno não é visitante, portanto não está agendado"
        #* Encerra, pois se não é visitante não tem como estar agendado.
        return result  
    
    logger.info("Verificando se o aluno está agendado na Aula Teste Totem às 23.")
    #* Procura por todas as aulas do dia para encontrar o código da aula Aula Teste Totem das 23:00.
    
    id_empresa_ = id_empresa.split("-")[0]
    cod_empresa = id_empresa.split("-")[1]
    pit = PIT(id_empresa=id_empresa)
    token_auth = pit.get_token_auth(id_empresa_)
    
    url = f"{pit.get_url(type_='treinoUrl', id_empresa_=id_empresa_)}/prest/psec/agenda-cards"
    headers = {
         "Authorization": f"Bearer {token_auth}",
         "empresaId": cod_empresa,
         "Accept": "*/*",
      }
    
    now = datetime.now().strftime('%Y%m%d')
    params = {
         "ref": int(now),
         "periodo": "DIA",
         "filtros": json.dumps({
            "tipo": "TURMA_AULA",
            "professoresIds": [],
            "ambientesIds": [],
            "alunosIds": [],
            "tipoDuracao": [],
            "disponibilidade": [],
            "modalidadesIds": [],
            "situacaoHorario": "",
            "turno": "",
            "search": ""
         })
      }
    
    response = requests.get(url, headers=headers, params=params)
    
    codigo = 0
    if response.status_code == 200:
        try:
            data = response.json()
            itens = data.get("content", {}).get("itens", [])
            
            if itens:
                #* Pega o item cujo horário é o das 23:00, que é o horário da aula agendada.
                for item in itens:
                    if item.get('horario') == '23:00':
                        aulas = item.get('dias', {}).get(now, [])
                        for aula in aulas:
                            if aula.get('turma') == "AULA TESTE TOTEM":
                                codigo = aula.get("codigo", 0) 
                                break
                        break
        except:
            logger.error(f"Erro ao converter JSON: {e}")

    #* Se encontrar a aula, procura os detalhes dela para verificar
    #* se o aluno está presente.
    if codigo != 0:
        url = f"{pit.get_url(type_='treinoUrl', id_empresa_=id_empresa_)}/prest/psec/agenda/turmas/{codigo}/aula-detalhada"
        
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "empresaId": cod_empresa
        }
        
        params = {
            "dia": now, 
            "completo": "true"
        }
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            try:
                data = response.json()
                data = json.loads(data)
                alunos = data.get("content", {}).get("alunos", [])
                
                if alunos:
                    for aluno in alunos:
                        if aluno.get("matriculaZW", 0) != matricula:
                            result["aluno_agendado"]["value"] = False
                            result["aluno_agendado"]["message"] = "Aluno não está agendado"
                
            except:
                logger.error(f"Erro ao converter JSON: {e}")

    return result


test_name = "Teste Agendamento Aula Experimental - Normal"

test_messages = [
    TestMessage("Olá, tudo bem?"),
    TestMessage("Não sou aluno."),
    TestMessage("Quero agendar uma aula experimental."),
    TestMessage("Quero ver todas as aulas de hoje."),
    TestMessage("Pode ser a Aula Teste Totem às 23."),
    TestMessage("Sim, pode agendar."),
    TestMessage(f"CPF: {faker.cpf()}, Nome completo: {faker.name()}"),
    TestMessage("Sim, confirmo."),
    TestMessage("Por enquanto é só isso, obrigado."),
]


def test_suite():
    """
    Teste para verificar o agendamento de aula experimental.

    Configuração do teste:
    - Mensagens: Contato inicial de um novo lead.
    - Respostas: O Conversas deve cadastrar o usuário do teste como lead.
    - Verificação: O Conversas deve verificar se o usuário foi cadastrado.
    """
    return TestSuite(test_name, test_messages, post_assertion_tests)
