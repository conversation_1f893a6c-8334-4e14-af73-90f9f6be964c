[project]
name = "llm-flow-test"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "faker==37.3.0",
    "flask==3.1.0",
    "inquirerpy>=0.3.4",
    "openai==1.71.0",
    "orion",
    "pandas==2.2.3",
    "playwright==1.52.0",
    "redis==5.2.1",
    "requests==2.32.3",
    "ruff>=0.11.13",
]

[tool.uv.sources]
orion = { workspace = true }
