#!/usr/bin/env python3

import os
from argparse import Argument<PERSON>ars<PERSON>


def normalize_test_name(name):
    # Lowercase, replace spaces and dashes with underscores, remove problematic chars
    import re
    name = name.lower().replace(' ', '_').replace('-', '_')
    name = re.sub(r'[^a-z0-9_]', '', name)
    return name


def title_case(name):
    return name.replace('_', ' ').replace('-', ' ').title()


def main():
    parser = ArgumentParser(description="Cria novo teste a partir do exemplo.")
    parser.add_argument("--nome", help="Nome do teste")
    parser.add_argument("--modo", choices=["rede", "individual", "default"], default="default")
    parser.add_argument("--fluxo", choices=["pre_venda", "pos_venda", "default"], default="default")
    args = parser.parse_args()


    if not args.nome:
        parser.print_help()
        exit(0)

    test_name = normalize_test_name(args.nome)
    test_title = title_case(test_name)

    script_dir = os.path.dirname(os.path.abspath(__file__))
    tests_dir = os.path.abspath(os.path.join(script_dir, "..", "tests"))
    example_path = os.path.join(tests_dir, "example.py")

    if args.modo == "rede" and args.fluxo != "default":
        dest_dir = os.path.join(tests_dir, f"fluxos_{args.fluxo}_{args.modo}")
    else:
        dest_dir = tests_dir

    os.makedirs(dest_dir, exist_ok=True)
    dest_file = os.path.join(dest_dir, f"{test_name}.py")

    with open(example_path, "r") as f:
        content = f.read()

    # Replace "Example" with test_title (title case, no _ or -)
    test_title = f"Teste {test_title}"
    if args.modo != "default":
        test_title += " - " + str(args.modo).title()
    content = content.replace("Example", test_title)

    with open(dest_file, "w") as f:
        f.write(content)

    # Add import to __init__.py
    init_path = os.path.join(tests_dir, "__init__.py")
    import_line = ""
    if args.modo == "rede" and args.fluxo != "default":
        import_line = f"from tests.fluxos_{args.fluxo}_{args.modo}.{test_name} import test_suite as test_suite_{test_name}_{args.modo}\n"
        suite_var = f"test_suite_{test_name}_{args.modo}"
    else:
        import_line = f"from tests.{test_name} import test_suite as test_suite_{test_name}\n"
        suite_var = f"test_suite_{test_name}"

    with open(init_path, "r") as f:
        init_content = f.readlines()

    # Insert import after last import
    last_import = 0
    for i, line in enumerate(init_content):
        if line.startswith("from "):
            last_import = i

    init_content.insert(last_import + 1, import_line)

    # Insert suite_var in tests dict
    for i, line in enumerate(init_content):
        if "tests = {" in line:
            dict_start = i
            break
    else:
        dict_start = -1

    if dict_start != -1:
        # Find where to insert (before closing })
        for j in range(dict_start + 1, len(init_content)):
            if "}" in init_content[j]:
                init_content.insert(j, f"    {suite_var}().test_name: {suite_var},\n")
                break

    with open(init_path, "w") as f:
        f.writelines(init_content)


if __name__ == "__main__":
    main()
