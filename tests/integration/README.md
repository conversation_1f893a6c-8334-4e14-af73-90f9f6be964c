# Como Criar um Novo Teste de Integração para o Conversas.AI

Este guia explica como criar um novo teste automatizado para o Conversas.AI, utilizando a infraestrutura de testes baseada em Python. Os testes são executados via uma interface gráfica web, disponível em `/pannel` no serviço Flask (`web/message_tester_server.py`).

---

## 1. Estrutura Básica de um Teste

Cada teste é definido em um arquivo Python dentro do diretório `tests/`. O teste consiste em:

- Uma lista de mensagens (`TestMessage`) que simulam a conversa.
- Uma função de verificação pós-teste (`post_assertion_tests`).
- Uma função `test_suite` que instancia e retorna um objeto `TestSuite`.

Exemplo básico (veja `tests/aula_experimental.py`):

```python
from tests.engine import TestSuite, TestMessage
from tests.utils import delete_context, faker

def post_assertion_tests(test_suite: TestSuite) -> dict:
    result = {
            "validacao_1": {
                "name": "Validação 1",
                "value": True,
                "message": "Validação 1 passou",
            },
            "validacao_2": {
                "name": "Validação 2",
                "value": True,
                "message": "Validação 2 passou",
            },
    }
    # Lógica para falsear as validações
    if ...:
        result["validacao_1"]["value"] = False
        result["validacao_1"]["message"] = "Validação 1 falhou"
    if ...:
        result["validacao_2"]["value"] = False
        result["validacao_2"]["message"] = "Validação 2 falhou"
    return {}

test_messages = [
    TestMessage("Olá, quero agendar uma aula experimental!"),
    TestMessage(f"CPF: {faker.cpf()}, Nome: {faker.name()}"),
]

test_name = "Teste de Agendamento de Aula Experimental"

def test_suite():
    return TestSuite(
        test_name,
        test_messages,
        post_assertion_tests,
        False,           # needs_further_message
        delete_context   # setup_func
    )
```

---

## 2. Componentes do Teste

### TestMessage

Representa uma mensagem enviada pelo usuário. Pode receber um texto e, opcionalmente, um passo adicional (`AdditionalStep`) a ser executado antes ou depois da resposta.

```python
TestMessage("Mensagem do usuário")
TestMessage("Mensagem com passo extra", AdditionalStep(funcao_extra, "after"))
```

### AdditionalStep

Permite executar funções customizadas antes ou depois da resposta do bot. Exemplo:

```python
from tests.engine import AdditionalStep

def verifica_algo(message):
    # lógica customizada
    pass

TestMessage("Mensagem", AdditionalStep(verifica_algo, "after"))
```

### TestSuite

Classe principal do teste. Parâmetros:

- `test_name` (str): Nome do teste.
- `test_messages` (list[TestMessage]): Lista de mensagens simulando a conversa.
- `post_assertions` (callable): Função chamada ao final do teste para validações.
- `needs_further_message` (bool, opcional): Se o teste espera mensagens extras após o fluxo principal.
- `setup_func` (callable, opcional): Função executada antes do teste (ex: limpar contexto).
- `chain_mode` (bool, opcional): Se o teste deve rodar em modo "rede" (empresa de rede).

---

## 3. Pós-verificações

Implemente a função `post_assertion_tests(test_suite)` para validar o resultado do teste, por exemplo, consultando APIs, checando respostas, etc. Ela recebe a instância do `TestSuite` e pode acessar:

- `test_suite.test_messages`: Lista de mensagens e respostas.
- `test_suite.phone`: Telefone usado no teste.
- `test_suite.id_empresa`: ID da empresa.

Retorne um dicionário com os resultados das verificações.

---

## 4. Registrando o Teste

No arquivo `tests/__init__.py`, registre seu teste na estrutura `tests`:

```python
from tests.seu_teste import test_suite as test_suite_seu_teste

tests = {
    test_suite_seu_teste().test_name: test_suite_seu_teste,
    # outros testes...
}
```

---

## 5. Executando os Testes pela Interface Gráfica

1. Rode o serviço pelo arquivo test-docker-compose.yml:
```bash
docker compose -f test-docker-compose.yml up
```

2. Rode os testes:
    1. Via interface web:
        - Acesse `/pannel` no navegador para visualizar e selecionar os testes disponíveis.
        - Após selecionar e iniciar um teste, acompanhe o fluxo de mensagens, logs e resultados diretamente pela interface web.
        - Os resultados e logs podem ser consultados nas abas correspondentes.
    2. Via linha de comando:
        - Acesse o container do Flask:
        ```bash
        python main.py # Este comando mostra as opções disponíveis via CLI
        ```

---

## Criando Teste Novo a Partir do Template

Existe um script utilitário para criar um novo teste dentro do diretório scripts:
```text
usage: ./scripts/new_test [-h] [--nome NOME] [--modo {rede,individual,default}] [--fluxo {pre_venda,pos_venda,default}]

Cria novo teste a partir do exemplo.

options:
  -h, --help            show this help message and exit
  --nome NOME           Nome do teste
  --modo {rede,individual,default}
  --fluxo {pre_venda,pos_venda,default}

```

Exemplo:
`./scripts/new_test --nome "lead solicita agendamento ligacao" --modo rede --fluxo pre_venda`
Cria um novo teste dentro do diretório `tests/fluxos_pre_venda_rede/`, com o nome "Teste Lead Solicita Agendamento Ligacao - Rede"

## Dicas e Possibilidades

- Use o `faker` para gerar dados aleatórios (CPF, nome, telefone).
- Utilize o `delete_context` para garantir que o contexto do usuário esteja limpo antes do teste.
- Consulte exemplos em `tests/aula_experimental.py` e `tests/fluxo_pos_venda.py` para fluxos completos.
- Explore o painel web para acompanhar logs, mensagens e resultados em tempo real.

