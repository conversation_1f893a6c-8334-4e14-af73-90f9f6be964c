import json
import logging
import os
import sys
import time

import redis
import requests
from flask import Flask, request, jsonify, render_template, Response, redirect

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from conn import redis_client

from tests.engine import TestResults

from run_tests import (
    ready_for_next_message,
    get_tests_names,
    get_test_phone_number,
    set_test,
    is_running,
    is_rede,
    finish,
    get_test_id_empresa,
    add_log_to_last_message,
)

app = Flask(__name__)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

HOST = os.getenv("WEB_SERVER_HOST", "localhost")
PORT = os.getenv("WEB_SERVER_PORT", "8042")
DEBUG = os.getenv("DEBUG", "false").lower() == "true"
BASE_API_URL = os.getenv("BASE_API_URL", "http://localhost:8300")


@app.route("/")
def index():
    """Serve the HTML frontend"""
    return redirect("/pannel")


@app.route("/summary")
def summary():
    """Serve the HTML frontend"""
    assertions = redis_client.lrange("tests:assertions", 0, -1)
    return render_template(
        "summary.html",
        assertions=[json.loads(assertion) for assertion in assertions],
    )


@app.route("/chat")
def chat():
    """Serve the HTML frontend"""
    test_name = request.args.get("test_name")
    user = request.args.get("user")

    if not is_running(test_name):
        logger.info("No test is currently running.")
        return redirect("/pannel")

    webhook_url = f"http://{HOST}:{PORT}"
    test_phone_number = get_test_phone_number(test_name)

    test_id_empresa = get_test_id_empresa(test_name)

    test_is_rede = is_rede(test_name)

    return render_template(
        "message_tester.html",
        webhook_url=webhook_url,
        test_name=test_name,
        user=user,
        phone_number=test_phone_number,
        id_empresa=test_id_empresa,
        test_is_rede=test_is_rede,
    )


@app.route("/pannel", methods=["GET"])
def get_pannel():
    """Serve the Test Control Pannel"""
    tests_list = get_tests_names()

    test_categories = {
        "Vendas e Conversão": [],
        "Agendamentos": [],
        "Conhecimento da empresa": [],
        "Cadastros": [],
        "Outros": [],
    }

    for test in tests_list:
        if "dados" in test.lower() or "informa" in test.lower():
            test_categories["Conhecimento da empresa"].append(test)
        elif "pos_venda" in test.lower() or "venda" in test.lower():
            test_categories["Vendas e Conversão"].append(test)
        elif "aula_experimental" in test.lower() or "agendamento" in test.lower():
            test_categories["Agendamentos"].append(test)
        elif "cadastro" in test.lower():
            test_categories["Cadastros"].append(test)
        else:
            test_categories["Outros"].append(test)

    running_tests = redis_client.hgetall("tests:running")
    
    return render_template("pannel.html", test_categories=test_categories, running_tests=running_tests)


@app.route("/api/test-status")
def get_test_status():
    """API endpoint to get current test execution status"""
    try:
        running_tests = redis_client.hgetall("tests:running")
        return jsonify({"running_tests": running_tests})
    except Exception as e:
        logger.error(f"Error getting test status: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/pannel", methods=["POST"])
def post_pannel():
    """Serve the Test Control Pannel"""
    selected_test = request.args.get("test_name")
    selected_tests = request.form.getlist("tests")
    logger.info(request.form.getlist("tests"))
    if not selected_tests:
        selected_tests = [selected_test]
    for test in selected_tests:
        logger.info("New test selected: %s", test)
        if set_test(test):
            ready_for_next_message(test)
    if selected_test:
        return redirect("/chat?test_name=" + selected_test)
    else:
        return redirect("/pannel")


@app.route("/result", methods=["GET"])
def get_result():
    """Serve the Test Result"""
    test_name = request.args.get("test_name")

    if not test_name:
        logger.error("No test name found in Redis.")
        return redirect("/")

    result = redis_client.hget("tests:results", test_name)
    if not result:
        logger.info("No results found in Redis.")
        return redirect("/")

    result = json.loads(result)

    return render_template("result.html", result=TestResults(result))


@app.route("/get-messages")
def get_messages():
    """Retrieve messages from Redis"""
    try:
        test_name = request.args.get("test_name")
        try:
            redis_client.ping()
        except redis.exceptions.ConnectionError:
            return jsonify({"error": "Could not connect to Redis server."}), 500

        phone_number = get_test_phone_number(test_name)

        test_id_empresa = get_test_id_empresa(test_name)

        redis_key = f"last_messages-{phone_number}-{test_id_empresa}"

        messages_json = redis_client.get(redis_key)
        if not messages_json:
            logger.info(f"No messages found for key: {redis_key}")
            return jsonify([])

        try:
            messages = json.loads(messages_json)
            if len(messages) < 2:
                logger.info("No messages found in Redis.")
                return jsonify([])
            messages = messages[
                2:
            ]  # Skip the first two messages (they are placed by default)

            if messages == []:
                logger.info("No messages found in Redis.")
                return jsonify([])

            max_retry = 10
            retry_count = 0

            while not messages[-1].get("enviado_por", "") == "assistant":
                logger.info("Waiting for the last message to be sent by the assistant.")
                time.sleep(1)
                messages_json = redis_client.get(redis_key)
                messages = json.loads(messages_json)
                retry_count += 1
                if retry_count > max_retry:
                    logger.error("Max retries reached. Exiting.")
                    break
            return jsonify(messages)
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON: {e}")
            return jsonify({"error": f"Invalid JSON format: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/get-logs")
def get_logs():
    """Retrieve logs from Redis"""
    try:
        try:
            redis_client.ping()
        except redis.exceptions.ConnectionError:
            return jsonify({"error": "Could not connect to Redis server."}), 500

        test_name = request.args.get("test_name")

        logs = redis_client.lrange(f"static_logs:{test_name}", 0, -1)
        if not logs:
            logger.info("No logs found in Redis.")
            return jsonify([])

        try:
            return jsonify(logs)
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON: {e}")
            return jsonify({"error": f"Invalid JSON format: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 500


@app.route("/send-message", methods=["POST"])
def send_message_proxy():
    """Proxy endpoint to forward message sending requests"""
    try:
        message_data = request.json

        if not message_data:
            return jsonify({"error": "No message data provided"}), 400

        test_id_empresa = message_data.get("testCompanyId")

        target_url = f"{BASE_API_URL}/receber_mensagem/?id_empresa={test_id_empresa}"
        logger.info(f"Forwarding request to {target_url}")

        response = requests.post(
            url=target_url,
            json=message_data,
            headers={
                "Content-Type": "application/json",
                "Origin": "https://api.z-api.io",
            },
        )

        logger.info(f"Response status: {response.status_code}")
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
        except:  # noqa
            logger.warning(f"Response text: {response.text}")

        return jsonify(
            response.json()
            if response.headers.get("content-type", "").startswith("application/json")
            else {"message": response.text}
        ), response.status_code

    except requests.RequestException as e:
        error_message = f"Error connecting to target service: {str(e)}"
        logger.info(error_message)
        return jsonify({"error": error_message}), 502

    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        logger.error(error_message)
        return jsonify({"error": error_message}), 500


@app.route("/message", methods=["POST"])
def wait_for_message():
    """Webhook endpoint to receive messages"""
    try:
        test_name = request.args.get("test_name")

        message_data = request.json
        if not message_data:
            return jsonify({"error": "No message data provided"}), 400

        logger.info(f"Received message: {json.dumps(message_data, indent=2)}")

        # Update timestamp for SSE
        redis_client.set(f"update_messages:{test_name}", str(time.time()))

        response = message_data.get("message")

        ready_for_next_message(test_name, response)

        return jsonify({"status": "success"}), 200

    except Exception as e:
        error_message = f"Error processing message: {str(e)}"
        logger.error(error_message)
        return jsonify({"error": error_message}), 500


@app.route("/log", methods=["POST"])
def wait_for_log():
    """Webhook endpoint to receive messages"""
    try:
        test_name = request.args.get("test_name")

        log_data = request.json

        if not log_data:
            return jsonify({"error": "No message data provided"}), 400

        logger.info(f"Received message: {json.dumps(log_data, indent=2)}")

        add_log_to_last_message(test_name, log_data)

        # Update timestamp for SSE
        redis_client.set(f"update_logs:{test_name}", str(time.time()))

        return jsonify({"status": "success"}), 200

    except Exception as e:
        error_message = f"Error processing message: {str(e)}"
        logger.error(error_message)
        return jsonify({"error": error_message}), 500


@app.route("/stream")
def stream():
    test_name = request.args.get("test_name")
    if not test_name:
        logger.error("No test name specified.")
        return redirect("/")

    def generate():
        # diz ao client para tentar reconectar em 2s se cair
        yield "retry: 2000\n\n"

        last_ts = 0

        while True:
            if test_name:
                has_finished = redis_client.get(f"tests:{test_name}:finished")
                if has_finished:
                    data = json.dumps({"status": "finished"})
                    yield f"id: {last_ts}\n"
                    yield "event: finished\n"
                    yield f"data: {data}\n\n"
                    finish(test_name)

            msg_ts = float(redis_client.get(f"update_messages:{test_name}") or 0)
            log_ts = float(redis_client.get(f"update_logs:{test_name}") or 0)

            newest_ts = max(msg_ts, log_ts)

            # heart-beat pra manter vivo
            yield ": keep-alive\n\n"

            if newest_ts > last_ts:
                data = json.dumps({"status": "running"})
                yield f"id: {newest_ts}\n"
                yield "event: update\n"
                yield f"data: {data}\n\n"
                last_ts = newest_ts

            time.sleep(1)

    return Response(generate(), mimetype="text/event-stream")


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(PORT), debug=DEBUG)
