<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testes - Conversas.AI</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="container whatsapp-container">
        <!-- WhatsApp-style header -->
        <div class="whatsapp-header">
            <div class="header-left">
                <h2>🤖 Conversas.AI - Testes</h2>
                <span class="status-indicator">● Online</span>
            </div>
            <div class="header-actions">
                <a href="/summary" class="action-button secondary">Resultados Gerais</a>
            </div>
        </div>
        
        <!-- Search bar similar to WhatsApp -->
        <div class="whatsapp-search">
            <input type="text" id="searchTests" placeholder="Pesquisar testes..." class="search-input">
        </div>
        
        <!-- Test list styled as WhatsApp contacts with folders -->
        <div class="whatsapp-contacts">
            <form id="panelForm" action="/pannel" method="POST">
                {% for category, tests in test_categories.items() %}
                {% if tests %}
                <div class="test-folder">
                    <div class="folder-header" data-folder="{{ category }}">
                        <span class="folder-icon">📁</span>
                        <span class="folder-name">{{ category }}</span>
                        <span class="folder-toggle">▼</span>
                    </div>
                    <div class="folder-content" id="folder-{{ category.replace(' ', '-') }}">
                        {% for test in tests %}
                        <div class="test-item" data-test-name="{{ test }}">
                            <div class="test-avatar">{{ test[:1].upper() }}</div>
                            <div class="test-info">
                                <div class="test-name">{{ test.replace("_", " ").title() }}</div>
                                <div class="test-description">Clique para executar este teste</div>
                                <input type="checkbox" id="chk_{{ test }}" name="tests" value="{{ test }}" style="display: none;">
                            </div>
                            <div class="test-status" data-test="{{ test }}">
                                {% if running_tests.get(test) %}
                                <span class="status-badge running">
                                    <span class="loading-spinner-small"></span>
                                    Executando
                                </span>
                                {% else %}
                                <span class="status-badge idle">Pronto</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% endfor %}
                
                <div class="whatsapp-footer">
                    <!-- Buttons removed as requested -->
                </div>
            </form>
        </div>

        <!-- Modal for multiple test selection -->
        <div id="multiTestModal" class="modal">
          <div class="backdrop"></div>
          <div class="modal-content whatsapp-modal">
            <button class="close-modal">&times;</button>
            <h3>Selecione um teste para acompanhar</h3>
            <ul id="multiTestList" class="whatsapp-list"></ul>
          </div>
        </div>
    </div>
    
    <div id="status" class="status loading-container-enhanced" style="display: none;">
        <div class="loading-spinner-enhanced"></div>
        <div class="loading-text-enhanced">Carregando<span class="loading-dots"></span></div>
    </div>
    <script>
    const form       = document.getElementById('panelForm');
    const modal      = document.getElementById('multiTestModal');
    const backdrop   = modal.querySelector('.backdrop');
    const closeModal = modal.querySelector('.close-modal');
    const testList   = document.getElementById('multiTestList');
    const searchInput = document.getElementById('searchTests');
    const testItems  = document.querySelectorAll('.test-item');

    function loading() {
        const status = document.getElementById('status');
        status.style.display = 'flex';
        status.style.opacity = '1';
    }

    function hideLoading() {
        const status = document.getElementById('status');
        status.style.opacity = '0';
        status.style.display = 'none';
    }

    window.addEventListener('load', hideLoading);
    window.addEventListener('pageshow', event => {
      if (event.persisted) {
        hideLoading();
      }
    });

    // Folder toggle functionality
    const folderHeaders = document.querySelectorAll('.folder-header');
    folderHeaders.forEach(header => {
        header.addEventListener('click', function(e) {
            e.stopPropagation();
            const folderContent = this.nextElementSibling;
            const isCollapsed = folderContent.classList.contains('collapsed');
            
            if (isCollapsed) {
                folderContent.classList.remove('collapsed');
                this.classList.remove('collapsed');
            } else {
                folderContent.classList.add('collapsed');
                this.classList.add('collapsed');
            }
        });
    });

    // Enhanced search functionality for folders
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const testFolders = document.querySelectorAll('.test-folder');
        
        testFolders.forEach(folder => {
            const testItems = folder.querySelectorAll('.test-item');
            let hasVisibleTests = false;
            
            testItems.forEach(item => {
                const testName = item.querySelector('.test-name').textContent.toLowerCase();
                if (testName.includes(searchTerm)) {
                    item.style.display = 'flex';
                    hasVisibleTests = true;
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Show/hide folder based on whether it has visible tests
            folder.style.display = hasVisibleTests ? 'block' : 'none';
            
            // Expand folders when searching
            if (searchTerm && hasVisibleTests) {
                folder.querySelector('.folder-content').classList.remove('collapsed');
                folder.querySelector('.folder-header').classList.remove('collapsed');
            }
        });
    });

    // Handle test item click
    testItems.forEach(item => {
        item.addEventListener('click', function() {
            // Clear previous selections
            testItems.forEach(i => {
                i.classList.remove('selected');
                i.querySelector('input[type="checkbox"]').checked = false;
            });
            
            // Select this item
            this.classList.add('selected');
            const checkbox = this.querySelector('input[type="checkbox"]');
            checkbox.checked = true;
            
            // Get test name and redirect
            const testName = this.getAttribute('data-test-name');
            loading();
            
            // Submit form to start the test and then redirect
            form.action = `/pannel?test_name=${encodeURIComponent(testName)}`;
            form.submit();
        });
    });

    // submit interceptor: só bloqueia quando >1 para abrir o modal
    form.addEventListener('submit', e => {
      const selected = Array.from(document.querySelectorAll('input[name="tests"]:checked'))
                            .map(i => i.value);
      if (selected.length > 1) {
        e.preventDefault();
        testList.innerHTML = '';

        // monta lista de escolhas
        selected.forEach(name => {
          const li = document.createElement('li');
          li.textContent = name.replace(/_/g,' ').toUpperCase();
          li.classList.add('whatsapp-list-item');
          li.addEventListener('click', () => {
            // após escolha, aprova o form com esse test_name
            form.action = `/pannel?test_name=${encodeURIComponent(name)}`;
            form.submit();
          });
          testList.appendChild(li);
        });

        // opção “nenhum”
        const noneLi = document.createElement('li');
        noneLi.textContent = 'NÃO ACOMPANHAR NENHUM TESTE';
        noneLi.classList.add('whatsapp-list-item');
        noneLi.style.fontStyle = 'italic';
        noneLi.addEventListener('click', () => form.submit());
        testList.appendChild(noneLi);

        modal.classList.add('is-open');
      }
    });

    // Removed button event handlers as buttons were deleted

    // fecha modal
    [backdrop, closeModal].forEach(el =>
      el.addEventListener('click', () => modal.classList.remove('is-open'))
    );

    // Real-time test status updates
    function updateTestStatus() {
        fetch('/api/test-status')
            .then(response => response.json())
            .then(data => {
                if (data.running_tests) {
                    document.querySelectorAll('.test-status').forEach(statusEl => {
                        const testName = statusEl.getAttribute('data-test');
                        const isRunning = data.running_tests[testName];
                        const badge = statusEl.querySelector('.status-badge');
                        
                        if (isRunning && !badge.classList.contains('running')) {
                            badge.className = 'status-badge running';
                            badge.innerHTML = '<span class="loading-spinner-small"></span>Executando';
                        } else if (!isRunning && badge.classList.contains('running')) {
                            badge.className = 'status-badge idle';
                            badge.textContent = 'Pronto';
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error updating test status:', error);
            });
    }

    setInterval(updateTestStatus, 2000);
    
    updateTestStatus();
    </script>
</body>
</html>
