<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resultado do Teste - {{ result.test_name.replace("_", " ").title() }}</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="container result-container">
        <!-- Header with test name and navigation -->
        <div class="result-header">
            <div class="header-left">
                <a href="/" class="back-button" title="Voltar ao Painel">←</a>
                <div class="test-info">
                    <h1 class="test-title">{{ result.test_name.replace("_", " ").title() }}</h1>
                    <p class="test-subtitle">Resultado do Teste de Integração</p>
                </div>
            </div>
            <div class="test-status">
                {% set has_failures = result.asserts and result.asserts|selectattr('lower', 'search', 'failed|error')|list|length > 0 %}
                <span class="status-badge {{ 'fail' if has_failures else 'pass' }}">
                    {{ '❌ Falhou' if has_failures else '✅ Passou' }}
                </span>
            </div>
        </div>

        <!-- Test Results Grid -->
        <div class="results-grid">
            <!-- Messages Section -->
            <div class="result-section messages-section">
                <div class="section-header">
                    <h2>💬 Mensagens</h2>
                    <span class="item-count">{{ result.messages|length }} mensagens</span>
                </div>
                <div class="section-content">
                    {% if result.messages %}
                        <ul class="messages-list">
                            {% for msg in result.messages %}
                            <li class="message-item">
                                <div class="message-content">{{ msg }}</div>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="empty-state">
                            <p>Nenhuma mensagem registrada</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Responses Section -->
            <div class="result-section responses-section">
                <div class="section-header">
                    <h2>🤖 Respostas</h2>
                    <span class="item-count">{{ result.responses|length }} respostas</span>
                </div>
                <div class="section-content">
                    {% if result.responses %}
                        <ul class="responses-list">
                            {% for resp in result.responses %}
                            <li class="response-item">
                                <div class="response-content">{{ resp }}</div>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <div class="empty-state">
                            <p>Nenhuma resposta registrada</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Assertions Section -->
        {% if result.asserts %}
        <div class="result-section assertions-section">
            <div class="section-header">
                <h2>🔍 Verificações</h2>
                <span class="item-count">{{ result.asserts|length }} verificações</span>
            </div>
            <div class="section-content">
                <div class="assertions-grid">
                    {% for assert in result.asserts %}
                    <div class="assertion-card {% if 'failed' in assert.lower() or 'error' in assert.lower() %}fail{% else %}pass{% endif %}">
                        <div class="assertion-icon">
                            {% if 'failed' in assert.lower() or 'error' in assert.lower() %}
                                ❌
                            {% else %}
                                ✅
                            {% endif %}
                        </div>
                        <div class="assertion-content">{{ assert }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Actions Footer -->
        <div class="result-actions">
            <a href="/" class="action-button primary">Ir para o Painel</a>
            <a href="/summary" class="action-button secondary">Ver Todos os Resultados</a>
        </div>
    </div>
</body>
</html>

