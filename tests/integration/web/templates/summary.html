<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Resultado dos Testes</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}" />
</head>
<body>
  <div class="container summary-container">
    <!-- Header with navigation -->
    <div class="summary-header">
      <div class="header-left">
        <a href="/" class="back-button" title="Voltar ao Painel">←</a>
        <div class="page-info">
          <h1>Resultado dos Testes</h1>
          <p class="page-subtitle">Resumo de todos os testes executados</p>
        </div>
      </div>
      <div class="summary-stats">
        {% set total_tests = assertions|length %}
        <span class="stats-badge">{{ total_tests }} testes executados</span>
      </div>
    </div>

    <!-- Tests Grid -->
    <div class="tests-grid">
      {% for test in assertions %}
        <div class="test-summary-card">
          <div class="test-card-header">
            <h2 class="test-card-title">{{ test.test_name.replace("_", " ").title() }}</h2>
            {% set test_assertions = test.assertions or {} %}
            <div class="test-card-status">
              {% if test_assertions %}
                <span class="status-badge pass">✅ Executado</span>
              {% else %}
                <span class="status-badge neutral">Sem verificações</span>
              {% endif %}
            </div>
          </div>
          
          {% if test_assertions %}
            <ul class="assertions-list">
              {% for key in test_assertions %}
              {% set assertion = test_assertions[key] %}
              <li class="assertion-item {{ 'pass' if assertion.value else 'fail' }}">
                <span class="assertion-status">
                  {% if assertion.value %}
                    <span class="status-icon pass">✔️</span>
                  {% else %}
                    <span class="status-icon fail">❌</span>
                  {% endif %}
                </span>
                <div class="assertion-details">
                  <span class="assertion-name">{{ assertion.name }}</span>
                  <span class="assertion-message">{{ assertion.message }}</span>
                </div>
              </li>
              {% endfor %}
            </ul>
          {% else %}
            <div class="empty-state">
              <p>Nenhuma verificação registrada para este teste</p>
            </div>
          {% endif %}
        </div>
      {% endfor %}
    </div>

    {% if not assertions %}
      <div class="empty-state-main">
        <h3>Nenhum teste executado ainda</h3>
        <p>Execute alguns testes para ver os resultados aqui.</p>
        <a href="/" class="action-button primary">Ir para o Painel</a>
      </div>
    {% endif %}

  </div>
</body>
</html>
