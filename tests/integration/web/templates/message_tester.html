<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testes de Mensagem</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="container">
        <div class="whatsapp-button-group">
            <button id="openLogsBtn" class="open-logs-btn">Ver Logs</button>
            <button id="showResultsButton" style="display: none;">Mostrar Resultados</button>
        </div>

        <div id="status" class="status loading-container-enhanced" style="display: none;">
            <div class="loading-spinner-enhanced"></div>
            <div class="loading-text-enhanced">Carregando<span class="loading-dots"></span></div>
        </div>

        <div id="progressContainer" class="progress-container" style="display: none;">
            <div class="progress-header">
                <span class="progress-title">Progresso do Teste</span>
                <span id="progressPercentage" class="progress-percentage">0%</span>
            </div>
            <div class="progress-bar-track">
                <div id="progressBarFill" class="progress-bar-fill"></div>
            </div>
        </div>

        <div class="message-container">
            <div class="whatsapp-header">
                <div class="header-left">
                    <a href="/pannel" class="back-button">←</a>
                    <h2>💬 {{ test_name.replace("_", " ").title() }}</h2>
                    <div class="test-subtitle">Chat de Teste - Conversas.AI</div>
                </div>
                <span class="status-indicator">● Online</span>
            </div>
            <div class="chat-section">
                <div id="messages" class="messages-wrapper"></div>
                <div id="logsModal" class="modal">
                  <div class="backdrop"></div>
                  <div class="modal-content">
                    <button class="close-modal">&times;</button>
                    <div id="logs" class="logs-wrapper"></div>
                  </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label for="message" class="message-label">Mensagem:</label>
            <textarea id="message" rows="1" placeholder="Escreva sua mensagem"></textarea>
            <button id="sendMessageButton" class="send-message-btn">Enviar mensagem</button>
        </div>
    </div>

    <script>
        window.webhookUrl = "{{ webhook_url }}";
        window.testName = "{{ test_name }}";
        window.user = "{{ user }}";
        window.phoneNumber = "{{ phone_number }}";
        window.testCompanyId = "{{ id_empresa }}";
        window.isRede = "{{ test_is_rede }}";
    </script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
