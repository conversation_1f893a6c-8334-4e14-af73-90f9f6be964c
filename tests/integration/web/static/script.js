// Function to convert markdown to HTML
function markdownToHtml(markdown) {
    return markdown
        .replace(/</g, '&lt;') // Escape HTML tags
        .replace(/>/g, '&gt;')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
        .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
        .replace(/__(.*?)__/g, '<u>$1</u>') // Underline
        .replace(/~~(.*?)~~/g, '<del>$1</del>') // Strikethrough
        .replace(/`(.*?)`/g, '<code>$1</code>') // Inline code
        .replace(/\n/g, '<br>'); // Line breaks
}

document.addEventListener('DOMContentLoaded', () => {
    const messageInput = document.getElementById('message');
    const sendMessageButton = document.getElementById('sendMessageButton');
    const statusDiv = document.getElementById('status');
    const openBtn = document.getElementById('openLogsBtn');
    const modal = document.getElementById('logsModal');
    const backdrop = modal.querySelector('.backdrop');
    const closeBtn = modal.querySelector('.close-modal');

    // abre o modal
    openBtn.addEventListener('click', () => {
        modal.classList.add('is-open');
    });

    // fecha clicando no “X” ou no backdrop
    [backdrop, closeBtn].forEach(el =>
        el.addEventListener('click', () => modal.classList.remove('is-open'))
    );

    // Function to display status messages
    function showStatus(message, isError = false) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${isError ? 'error' : 'success'}`;
        statusDiv.style.display = 'block';
        
        if (window.statusTimeout) {
            clearTimeout(window.statusTimeout);
        }
        
        window.statusTimeout = setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 4000);
    };

    function addButton(text, className, onClick) {
        const button = document.createElement('button');
        button.textContent = text;
        button.className = className;
        button.addEventListener('click', onClick);
        return button;
    };

    async function loadMessages() {
        try {
            // Using our Flask server endpoint
            const response = await fetch(`/get-messages?test_name=${window.testName}`);
            const messages = await response.json();
            if (!response.error) {
                // Display messages
                displayMessages(messages);
            } else {
                const error = await response.text();
                showStatus(`Error loading messages: ${error}`, true);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            showStatus(`Error: ${error.message}`, true);
        }
    };

    async function loadLogs() {
        try {
            // Using our Flask server endpoint
            const response = await fetch(`/get-logs?test_name=${window.testName}`);
            const logs = await response.json();
            if (!response.error) {
                // Display logs
                displayLogs(logs);
            } else {
                const error = await response.text();
                showStatus(`Error loading logs: ${error}`, true);
            }
        } catch (error) {
            console.error('Error loading logs:', error);
            showStatus(`Error: ${error.message}`, true);
        }
    };

    function smoothScrollTo(el, target, duration = 1000) {
        const start = el.scrollTop;
        const change = target - start;
        const startTime = performance.now();

        // função de easing (easeInOutQuad)
        function easeInOutQuad(t) {
            return t < 0.5
                ? 2 * t * t
                : -1 + (4 - 2 * t) * t;
        }

        function animate(now) {
            const elapsed = now - startTime;
            const progress = Math.min(elapsed / duration, 1);
            el.scrollTop = start + change * easeInOutQuad(progress);
            if (elapsed < duration) {
                requestAnimationFrame(animate);
            }
        }

        requestAnimationFrame(animate);
    };

    // Function to display messages
    function displayMessages(messages) {

        // Clear previous messages
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';

        messages.forEach((msg, index) => {
            // se a mensagem começar com "Função chamada:", fazer um dropdown simples pra mostrar o restante
            if (msg.mensagem.startsWith('Função chamada:')) {
                const dropdown = document.createElement('details');
                const summary = document.createElement('summary');
                summary.textContent = "Chamada de função";
                dropdown.appendChild(summary);

                const pre = document.createElement('div');
                pre.textContent = msg.mensagem;
                pre.className = 'chat-bubble assistant';
                dropdown.appendChild(pre);

                messagesContainer.appendChild(dropdown);
                return;
            }
            const messageRow = document.createElement('div');
            messageRow.className = 'message-row';

            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-bubble ${msg.enviado_por === 'user' ? 'user' : 'assistant'}`;

            // Convert markdown to HTML
            const markdownText = msg.mensagem;
            const htmlText = markdownToHtml(markdownText);
            messageDiv.innerHTML = htmlText;
            
            messageDiv.style.animationDelay = `${index * 0.1}s`;

            messageRow.appendChild(messageDiv);
            messagesContainer.appendChild(messageRow);
        });
        // Scroll to the bottom of the messages container
        smoothScrollTo(messagesContainer, messagesContainer.scrollHeight, 2000);
    };

    async function displayLogs(logs) {
        // Clear previous logs
        const logsContainer = document.getElementById('logs');
        logsContainer.innerHTML = '';

        logs.forEach(raw => {
            const log = JSON.parse(raw);

            // Cria linha de log
            const logRow = document.createElement('div');
            logRow.className = 'log-row';

            // Cria bolha de informações sem o cURL
            const logDiv = document.createElement('div');
            logDiv.className = 'log-bubble';
            const logDate = log.data.data_requisicao;
            const logFunction = log.data.function;
            const logStatusCodeRaw = log.data.status_code;
            // garante que seja número
            const logStatusCode = typeof logStatusCodeRaw === 'number'
                ? logStatusCodeRaw
                : parseInt(logStatusCodeRaw, 10);

            const infoText =
                `Função ${logFunction}:\n` +
                `- Data: ${logDate}\n` +
                `- Status Code: ${logStatusCode}`;
            logDiv.textContent = infoText;

            // adiciona classe de cor com base no status numérico
            if (logStatusCode >= 400 && logStatusCode < 500) {
                logDiv.classList.add('status-4xx');
            } else if (logStatusCode >= 500 && logStatusCode < 600) {
                logDiv.classList.add('status-5xx');
            } else if (logStatusCode === 200) {
                logDiv.classList.add('status-200');
            }

            logRow.appendChild(logDiv);

            // Cria textarea readonly só com o cURL
            const curlTextarea = document.createElement('textarea');
            curlTextarea.className = 'log-curl';
            curlTextarea.readOnly = true;
            curlTextarea.value = log.data.request;
            logRow.appendChild(curlTextarea);

            // Cria botão para copiar o cURL
            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.textContent = 'Copiar cURL';
            copyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(log.data.request);
                copyBtn.textContent = 'Copiado!';
                setTimeout(() => {
                    copyBtn.textContent = 'Copiar cURL';
                }, 2000);
            });
            logRow.appendChild(copyBtn);

            logsContainer.appendChild(logRow);
        });

        // Scroll suave até o fim
        smoothScrollTo(logsContainer, logsContainer.scrollHeight, 2000);
    };

    testName = window.testName;

    let source;
    let sseRetries = 0;
    const maxSseRetries = 3;

    function connectSSE() {
        if (source) {
            source.close();
        }
        source = new EventSource('/stream?test_name=' + testName);

        source.addEventListener('update', e => {
            const data = JSON.parse(e.data);
            console.log('Mensagem recebida do stream:', data);
            if (data.error) {
                showStatus(data.error, true);
            } else if (data.status == 'running') {
                if (testProgress === 0) {
                    showProgressBar();
                }
                loadMessages();
                loadLogs();
            }
        });

        source.addEventListener('finished', e => {
            if (window.user == 'tester') {
                showStatus('Teste finalizado', false);
                return;
            }
            const data = JSON.parse(e.data);
            console.log('Mensagem recebida do stream:', data);
            if (data.status == 'finished') {
                completeProgress();
                // Button to redirect to /result
                const resultButton = addButton('Ver resultado', 'result-button', () => {
                    window.location.href = '/result?test_name=' + testName;
                });
                const resultButtonContainer = document.getElementById('showResultsButton');
                resultButtonContainer.innerHTML = ''; // Clear previous buttons
                resultButtonContainer.appendChild(resultButton);
                resultButtonContainer.style.display = 'block';
                showStatus('Teste finalizado', false);
            }
        });

        source.onerror = () => {
            showStatus('Conexão perdida. Tentando novamente...', true);
            source.close();
            if (sseRetries < maxSseRetries) {
                sseRetries++;
                setTimeout(connectSSE, 2000);
            } else {
                showStatus('Falha ao reconectar à stream após várias tentativas.', true);
            }
        };
    }

    connectSSE();

    let testProgress = 0;
    let progressInterval = null;
    let testStartTime = null;

    function showProgressBar() {
        const progressContainer = document.getElementById('progressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'block';
            testProgress = 0;
            testStartTime = Date.now();
            updateProgressBar();
            startProgressSimulation();
        }
    }

    function hideProgressBar() {
        const progressContainer = document.getElementById('progressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'none';
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
        }
    }

    function updateProgressBar() {
        const progressFill = document.getElementById('progressBarFill');
        const progressPercentage = document.getElementById('progressPercentage');
        if (progressFill && progressPercentage) {
            progressFill.style.width = testProgress + '%';
            progressPercentage.textContent = Math.round(testProgress) + '%';
        }
    }

    function startProgressSimulation() {
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        progressInterval = setInterval(() => {
            if (testProgress < 95) {
                const elapsed = Date.now() - testStartTime;
                const progressIncrement = Math.max(0.5, 3 - (elapsed / 10000));
                testProgress += progressIncrement;
                updateProgressBar();
            }
        }, 1000);
    }

    function completeProgress() {
        testProgress = 100;
        updateProgressBar();
        setTimeout(() => {
            hideProgressBar();
        }, 1500);
    }

    source.addEventListener('update', e => {
        const data = JSON.parse(e.data);
        console.log('Mensagem recebida do stream:', data);
        if (data.error) {
            showStatus(data.error, true);
        } else if (data.status == 'running') {
            if (testProgress === 0) {
                showProgressBar();
            }
            loadMessages();
            loadLogs();
        }
    });

    source.addEventListener('finished', e => {
        if (window.user == 'tester') {
            showStatus('Teste finalizado', false);
            return;
        }
        const data = JSON.parse(e.data);
        console.log('Mensagem recebida do stream:', data);
        if (data.status == 'finished') {
            completeProgress();
            // Button to redirect to /result
            const resultButton = addButton('Ver resultado', 'result-button', () => {
                window.location.href = '/result?test_name=' + testName;
            });
            const resultButtonContainer = document.getElementById('showResultsButton');
            resultButtonContainer.innerHTML = ''; // Clear previous buttons
            resultButtonContainer.appendChild(resultButton);
            resultButtonContainer.style.display = 'block';
            showStatus('Teste finalizado', false);
        }
    });

    source.onerror = () => {
        showStatus('Conexão perdida. Tentando novamente...', true);
    };

    // Function to send message
    async function sendMessage() {
        const message = messageInput.value.trim();

        if (!message) {
            showStatus('Por favor, digite uma mensagem', true);
            return;
        }

        messageInput.disabled = true;
        sendMessageButton.disabled = true;
        const originalText = sendMessageButton.textContent;
        sendMessageButton.textContent = 'Enviando...';

        try {
            const response = await fetch('/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    isStatusReply: false,
                    chatLid: "148730825166915@lid",
                    connectedPhone: "556281305490",
                    waitingMessage: false,
                    isEdit: false,
                    isGroup: false,
                    instanceId: "",
                    messageId: "integration-test",
                    phone: window.phoneNumber,
                    fromMe: false,
                    momment: Date.now(),
                    status: "RECEIVED",
                    chatName: "Eu",
                    senderPhoto: null,
                    senderName: "",
                    photo: "",
                    broadcast: false,
                    participantLid: null,
                    forwarded: false,
                    type: "ReceivedCallback",
                    fromApi: false,
                    text: {
                        message: message
                    },
                    sendToWebhook: true,
                    webhookUrl: window.webhookUrl,
                    testName: testName,
                    testCompanyId: window.testCompanyId,
                    isRede: window.isRede === "True"
                }),
            });

            const data = await response.json();
            if (response.ok) {
                messageInput.value = '';
                showStatus('Mensagem enviada com sucesso', false);
            } else {
                showStatus(data.error || 'Falha ao enviar a mensagem', true);
            }
        } catch (error) {
            showStatus('Erro ao enviar a mensagem: ' + error.message, true);
        } finally {
            messageInput.disabled = false;
            sendMessageButton.disabled = false;
            sendMessageButton.textContent = originalText;
            messageInput.focus();
        }
    }

    messageInput.addEventListener('keypress', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault(); // Prevent default to avoid newline
            sendMessage();
        }
    });

    sendMessageButton.addEventListener('click', () => {
        sendMessage();
    });
});
