/* <PERSON><PERSON> e variáveis - WhatsApp Style */
:root {
  --color-outside-bg: #E5DDD5; /* WhatsApp chat background */
  --color-surface: #ffffff;
  --color-primary: #25D366; /* WhatsApp green */
  --color-primary-hover: #128C7E; /* Darker WhatsApp green */
  --color-primary-active: #0F6B5C; /* Even darker for active state */
  --color-text: #333333;
  --color-muted: #666666;
  --color-text-secondary: #8696a0;
  --shadow-light: 0 1px 2px rgba(0, 0, 0, 0.1); /* Softer shadows like WhatsApp */
  --shadow-deep: 0 2px 8px rgba(0, 0, 0, 0.15);
  --shadow-enhanced: 0 4px 16px rgba(0, 0, 0, 0.12);
  --radius: 8px;
  --radius-sm: 6px;
  --spacing-unit: 1rem;
  --font-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON><PERSON>, sans-serif; /* WhatsApp-like font stack */
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --border-light: #e9edef;
  --success-bg: #d1f4cc;
  --success-text: #0f7b0f;
  --error-bg: #ffeaea;
  --error-text: #d93025;
}

/* Global reset */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  background-color: var(--color-outside-bg);
  min-height: 100vh;
  font-family: var(--font-base);
  color: var(--color-text);
  line-height: 1.6;
}

body {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: calc(var(--spacing-unit) * 2);
}

/* Container centralizado */
.container {
  width: 100%;
  max-width: 900px;
  background-color: var(--color-surface);
  padding: calc(var(--spacing-unit) * 2);
  border-radius: 12px;
  box-shadow: var(--shadow-enhanced);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
}

.container:hover {
  box-shadow: var(--shadow-deep);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .container {
    padding: var(--spacing-unit);
    border-radius: var(--radius);
    margin: 0;
  }
}

/* Títulos */
h1, h2, h3 {
  margin-bottom: var(--spacing-unit);
  color: var(--color-text);
  font-weight: 600;
  letter-spacing: 0.5px;
}

h1 {
  font-size: 2rem;
  text-align: center;
}

/* Parágrafos */
p {
  margin-bottom: var(--spacing-unit);
}

/* Formulários */
.form-group {
  margin-bottom: calc(var(--spacing-unit) * 1.25);
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--color-muted);
  font-size: 0.95rem;
  letter-spacing: 0.4px;
  transition: color 0.2s ease;
}

input[type="text"], input[type="email"], textarea, select {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #D1D1D1;
  border-radius: 24px;
  font-family: inherit;
  font-size: 14px;
  background-color: #FFFFFF;
  transition: var(--transition);
  outline: none;
}

input:focus, textarea:focus, select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.15);
}

input:hover, textarea:hover, select:hover {
  border-color: var(--color-primary-hover);
}

textarea {
  resize: vertical;
  min-height: 80px;
  max-height: 120px;
  border-radius: 12px;
}

input::placeholder, textarea::placeholder {
  color: var(--color-text-secondary);
  opacity: 1;
}

.send-message-btn {
  background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  min-height: 48px;
  transition: var(--transition);
  box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
  white-space: nowrap;
}

.send-message-btn:hover {
  background: linear-gradient(135deg, #22c55e 0%, #10b981 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
}

.send-message-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(37, 211, 102, 0.2);
}

.send-message-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Botões - WhatsApp Style */
button {
  display: inline-block;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background: var(--color-primary);
  border: none;
  border-radius: 24px; /* More rounded like WhatsApp */
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  opacity: 0;
  transition: var(--transition);
}

button:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

button:hover::before {
  opacity: 1;
}

button:active {
  background: var(--color-primary-active);
  transform: translateY(0);
}

button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Status */
.status {
  position: fixed;
  top: calc(var(--spacing-unit) * 1);
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  display: inline-block;
  padding: 12px 20px;
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  font-weight: 500;
  text-align: center;
  line-height: 1.4;
  max-width: 80%;
  word-wrap: break-word;
  font-size: 14px;
  animation: slideIn 0.3s ease-out;
  border-left: 4px solid;
}

.status.success {
  background-color: var(--success-bg);
  color: var(--success-text);
  border-left-color: var(--color-primary);
}

.status.error {
  background-color: var(--error-bg);
  color: var(--error-text);
  border-left-color: #d93025;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.message-container .chat-section {
  display: flex;
  gap: var(--spacing-unit);
}

.messages-wrapper {
  flex: 2;
  height: 500px;
  overflow-y: auto;
  padding: 20px;
  background-color: #E5DDD5; /* WhatsApp chat background */
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 0;
  border: none;
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.messages-wrapper:empty::before {
  content: 'Nenhuma mensagem ainda. Envie uma mensagem para começar a conversa.';
  color: var(--color-text-secondary);
  font-style: italic;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 80%;
  font-size: 14px;
}

.logs-wrapper {
  flex: 1;
  max-height: 50vh;
  overflow-y: auto;
  padding: var(--spacing-unit);
  border: 1px solid #e5e7eb;
  border-radius: var(--radius);
  background-color: var(--color-surface);
  box-shadow: var(--shadow-light);
}

.message {
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  background-color: #f9fafb;
  border-radius: var(--radius);
  line-height: 1.4;
}

.chat-bubble {
  position: relative;
  padding: 10px 14px;
  margin-bottom: 12px;
  border-radius: 12px;
  max-width: 75%;
  word-wrap: break-word;
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: messageSlide 0.3s ease-out;
}

.chat-bubble.user {
  background-color: #DCF8C6; /* WhatsApp outgoing message color */
  margin-left: auto;
  margin-right: 12px;
  border-bottom-right-radius: 3px; /* WhatsApp's tail effect */
}

.chat-bubble.user::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-left-color: #DCF8C6;
  border-bottom: 0;
}

.chat-bubble.assistant {
  background-color: #FFFFFF; /* WhatsApp incoming message color */
  margin-right: auto;
  margin-left: 12px;
  border-bottom-left-radius: 3px; /* WhatsApp's tail effect */
  border: 1px solid #E0E0E0;
}

.chat-bubble.assistant::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-right-color: #FFFFFF;
  border-bottom: 0;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-row {
  display: flex;
  width: 100%;
  align-items: flex-start;
}
/* textarea para cURL */
.log-curl {
  width: 100%;
  font-family: monospace;
  margin-top: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: var(--radius);
  background-color: #f9fafb;
  resize: vertical;
  min-height: 3rem;
}

/* botão de copiar */
.copy-btn {
  margin-top: 0.5rem;
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
  border: none;
  border-radius: var(--radius);
  background-color: var(--color-primary);
  color: #fff;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copy-btn:hover {
  background-color: var(--color-primary-hover);
}

.log-bubble.status-4xx {
  background-color: #FFF4E5; /* laranja claro */
}

.log-bubble.status-5xx {
  background-color: #FDECEA; /* vermelho claro */
}

.log-bubble.status-200 {
  background-color: #E6F4EA; /* verde claro */
}

/* modal inicia escondido */
.modal {
  display: none;
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  z-index: 1000;
}

/* backdrop semi-transparente */
.modal .backdrop {
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.4);
}

/* janela central */
.modal .modal-content {
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  background: var(--color-surface);
  padding: calc(var(--spacing-unit)*1.5);
  border-radius: var(--radius);
  box-shadow: var(--shadow-deep);
  width: 90%;
  max-width: 600px;
  max-height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* botão de fechar */
.close-modal {
  align-self: flex-end;
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

/* quando aberto, exibe o modal */
.modal.is-open {
  display: block;
}

/* Checkbox Button Styles */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  gap: var(--spacing-unit);
  margin-bottom: calc(var(--spacing-unit) * 1.25);
}

.checkbox-group input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkbox-group label {
  position: relative;
  padding-left: calc(var(--spacing-unit) * 2);
  cursor: pointer;
  font-size: 1rem;
  color: var(--color-text);
  user-select: none;
  line-height: 1;
  transition: color 0.2s ease;
}

.checkbox-group label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  background-color: var(--color-surface);
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.checkbox-group label::after {
  content: "";
  position: absolute;
  left: 0.375rem;
  top: calc(50% + 0.125rem);
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #fff;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-group input[type="checkbox"]:checked + label::before {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-group input[type="checkbox"]:checked + label::after {
  opacity: 1;
}

.checkbox-group input[type="checkbox"]:focus + label::before {
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.3);
}

.checkbox-group label:hover::before {
  border-color: var(--color-primary-hover);
}

.checkbox-group input[type="checkbox"]:disabled + label {
  color: var(--color-muted);
  cursor: not-allowed;
}

.checkbox-group input[type="checkbox"]:disabled + label::before {
  border-color: #ccc;
  background-color: #f9f9f9;
}

/* Link de volta */
a {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  border-radius: var(--radius);
  text-decoration: none;
  transition: background 0.2s ease-in-out, transform 0.1s ease, box-shadow 0.2s ease;
}

a:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--color-primary));
  transform: translateY(-1px);
}

a:active {
  transform: translateY(0);
}

a:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.3);
}

/* === Resumo de Testes === */
.test-summary {
  margin-bottom: var(--spacing-unit);
  padding: var(--spacing-unit);
  background-color: var(--color-surface);
  border-radius: var(--radius);
  box-shadow: var(--shadow-light);
}

.test-summary h2 {
  margin-bottom: calc(var(--spacing-unit) / 2);
  font-size: 1.25rem;
  color: var(--color-text);
}

.assertions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.assertion-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: var(--radius);
  background-color: #f9fafb;
  align-items: center;
  transition: background 0.2s;
}

/* status indicators */
.assertion-item.pass {
  border-left: 4px solid #34A853;   /* verde */
}
.assertion-item.fail {
  border-left: 4px solid #EA4335;   /* vermelho */
}

.assertion-item.pass:hover {
  background-color: #E6F4EA;        /* verde claro */
}
.assertion-item.fail:hover {
  background-color: #FDECEA;        /* vermelho claro */
}

.assertion-name {
  font-weight: 500;
  color: var(--color-text);
}

.assertion-message {
  color: var(--color-muted);
  font-size: 0.95rem;
  margin-left: var(--spacing-unit);
}

/* === Painel === */
#multiTestList {
  list-style: none;
  padding: 0;
  margin: var(--spacing-unit) 0;
}
#multiTestList li {
  padding: 0.5rem;
  border-radius: var(--radius);
  transition: background 0.2s;
}
#multiTestList li:hover {
  background: var(--color-muted);
  color: #fff;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0,0,0,0.2);
  border-radius: 4px;
  transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0,0,0,0.3);
}

/* Enhanced responsiveness */
@media (max-width: 768px) {
  body {
    padding: var(--spacing-unit);
  }

  .container {
    padding: var(--spacing-unit);
  }

  h1 {
    font-size: 1.5rem;
  }

  a {
    padding: 0.5rem 1rem;
  }

  .whatsapp-header {
    padding: 12px 16px;
  }

  .whatsapp-header h2 {
    font-size: 16px;
  }

  .messages-wrapper {
    height: 400px;
    padding: 16px;
  }

  .chat-bubble {
    max-width: 85%;
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .messages-results li, .responses-results li {
    padding: 0.5rem;
  }

  .container {
    padding: 12px;
  }

  .messages-wrapper {
    height: 350px;
    padding: 12px;
  }

  .chat-bubble {
    max-width: 90%;
  }

  .form-group {
    padding: 12px;
  }
}

/* Results Screen Styles */
.result-container {
  max-width: 1200px;
  padding: 24px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: 32px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.test-info h1.test-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text);
}

.test-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: var(--color-text-secondary);
}

.test-status .status-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.status-badge.pass {
  background-color: var(--success-bg);
  color: var(--success-text);
}

.status-badge.fail {
  background-color: var(--error-bg);
  color: var(--error-text);
}

.status-badge.neutral {
  background-color: #f8f9fa;
  color: var(--color-text-secondary);
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.result-section {
  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: var(--transition);
}

.result-section:hover {
  box-shadow: var(--shadow-deep);
  transform: translateY(-2px);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--border-light);
}

.section-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

.item-count {
  font-size: 12px;
  color: var(--color-text-secondary);
  background: rgba(255,255,255,0.8);
  padding: 4px 8px;
  border-radius: 12px;
}

.section-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.messages-list, .responses-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item, .response-item {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--color-primary);
  transition: var(--transition);
}

.message-item:hover, .response-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.message-content, .response-content {
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text);
  word-wrap: break-word;
}

.assertions-section {
  grid-column: 1 / -1;
}

.assertions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.assertion-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  transition: var(--transition);
}

.assertion-card.pass {
  background: var(--success-bg);
  border-left: 4px solid var(--success-text);
}

.assertion-card.fail {
  background: var(--error-bg);
  border-left: 4px solid var(--error-text);
}

.assertion-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.assertion-content {
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.assertion-card.pass .assertion-content {
  color: var(--success-text);
}

.assertion-card.fail .assertion-content {
  color: var(--error-text);
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-secondary);
  font-style: italic;
}

.result-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px 0;
  border-top: 1px solid var(--border-light);
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 24px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  min-width: 160px;
  justify-content: center;
}

.action-button.primary {
  background: var(--color-primary);
  color: white;
}

.action-button.primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.action-button.secondary {
  background: #f8f9fa;
  color: var(--color-text);
  border: 1px solid var(--border-light);
}

.action-button.secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* Summary Screen Styles */
.summary-container {
  max-width: 1200px;
  padding: 24px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 2px solid var(--border-light);
  margin-bottom: 32px;
}

.page-info h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text);
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: var(--color-text-secondary);
}

.summary-stats .stats-badge {
  padding: 8px 16px;
  background: var(--color-primary);
  color: white;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.tests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.test-summary-card {
  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: var(--transition);
}

.test-summary-card:hover {
  box-shadow: var(--shadow-deep);
  transform: translateY(-2px);
}

.test-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--border-light);
}

.test-card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text);
}

.assertions-list {
  list-style: none;
  padding: 20px;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.assertion-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: var(--transition);
}

.assertion-item.pass {
  background: var(--success-bg);
}

.assertion-item.fail {
  background: var(--error-bg);
}

.assertion-item:hover {
  transform: translateX(4px);
}

.status-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.assertion-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.assertion-name {
  font-weight: 600;
  font-size: 14px;
}

.assertion-item.pass .assertion-name {
  color: var(--success-text);
}

.assertion-item.fail .assertion-name {
  color: var(--error-text);
}

.assertion-message {
  font-size: 13px;
  opacity: 0.8;
}

.assertion-item.pass .assertion-message {
  color: var(--success-text);
}

.assertion-item.fail .assertion-message {
  color: var(--error-text);
}

.empty-state-main {
  text-align: center;
  padding: 80px 20px;
  color: var(--color-text-secondary);
}

.empty-state-main h3 {
  margin: 0 0 16px 0;
  font-size: 20px;
  color: var(--color-text);
}

.empty-state-main p {
  margin: 0 0 24px 0;
  font-size: 16px;
}

.summary-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px 0;
  border-top: 1px solid var(--border-light);
}

/* Results screen responsive design */
@media (max-width: 768px) {
  .result-container, .summary-container {
    padding: 16px;
  }
  
  .result-header, .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .section-content {
    max-height: 300px;
  }
  
  .assertions-grid {
    grid-template-columns: 1fr;
  }
  
  .result-actions, .summary-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tests-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .test-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .test-info h1.test-title, .page-info h1 {
    font-size: 20px;
  }
  
  .test-card-title {
    font-size: 16px;
  }
  
  .section-header {
    padding: 12px 16px;
  }
  
  .section-content, .assertions-list {
    padding: 16px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* WhatsApp-style header */
.whatsapp-header {
  background: linear-gradient(135deg, #075E54 0%, #128C7E 100%);
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--radius) var(--radius) 0 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
}

.whatsapp-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255,255,255,0.1);
}

.whatsapp-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF; /* White text for contrast */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.test-subtitle {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 2px;
  color: #FFFFFF;
}

.back-button {
  color: white;
  text-decoration: none;
  font-size: 20px;
  padding: 8px;
  border-radius: 50%;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.back-button:hover {
  background: rgba(255,255,255,0.15);
  transform: scale(1.1);
}

.status-indicator {
  font-size: 12px;
  color: #4fc3f7;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(255,255,255,0.1);
  border-radius: 12px;
}

/* Message input area - WhatsApp style */
.form-group {
  background-color: #F0F0F0;
  padding: 16px 20px;
  border-top: 1px solid #D1D1D1;
  border-radius: 0 0 var(--radius) var(--radius);
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

@media (max-width: 768px) {
  .form-group {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

/* Message label styling for better alignment */
.message-label {
  font-weight: 500;
  color: #075E54;
  margin-bottom: 8px;
  text-align: center;
  display: block;
  font-size: 14px;
  }
}

/* WhatsApp container styles */
.whatsapp-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: #FFFFFF;
  border-radius: var(--radius);
  box-shadow: var(--shadow-deep);
  overflow: hidden;
}

/* WhatsApp search bar */
.whatsapp-search {
  background-color: #F6F6F6;
  padding: 8px 16px;
  border-bottom: 1px solid #E0E0E0;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  background-color: #FFFFFF;
  font-size: 14px;
}

/* WhatsApp contacts/tests list */
.whatsapp-contacts {
  background-color: #FFFFFF;
  overflow-y: auto;
  max-height: 70vh;
}

.test-list {
  display: flex;
  flex-direction: column;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #F2F2F2;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-item:hover {
  background-color: #F5F5F5;
}

.test-item.selected {
  background-color: #EBEBEB;
}

.test-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #25D366;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 16px;
}

.test-info {
  flex: 1;
}

.test-name {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 4px;
}

.test-description {
  font-size: 13px;
  color: #8C8C8C;
}

.test-status {
  margin-left: 12px;
  flex-shrink: 0;
}

.status-badge.running {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: pulse 2s infinite;
}

.status-badge.idle {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.loading-spinner-small {
  width: 12px;
  height: 12px;
  border: 2px solid #bbdefb;
  border-top: 2px solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* WhatsApp header with actions */
.whatsapp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}


.header-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.header-actions .action-button {
  background-color: #25D366;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.header-actions .action-button:hover {
  background-color: #128C7E;
}

/* WhatsApp footer */
.whatsapp-footer {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  background-color: #F0F0F0;
  border-top: 1px solid #E0E0E0;
}

.whatsapp-button {
  background-color: #25D366;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.whatsapp-button:hover {
  background-color: #128C7E;
}

/* WhatsApp modal */
.whatsapp-modal {
  background-color: #FFFFFF;
  border-radius: 12px;
  max-width: 400px;
}

.whatsapp-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.whatsapp-list-item {
  padding: 12px 16px;
  border-bottom: 1px solid #F2F2F2;
  cursor: pointer;
}

.whatsapp-list-item:hover {
  background-color: #F5F5F5;
}

/* Header left with back button */
.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  color: white;
  font-size: 24px;
  text-decoration: none;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* WhatsApp button group */
.whatsapp-button-group {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin: 16px 0;
  padding: 0 16px;
}

/* Loading spinner animation */
.status {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #E0E0E0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(37, 211, 102, 0.3);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin 1s ease-in-out infinite;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: #075E54;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Test folder organization */
.test-folder {
  border-bottom: 1px solid #E0E0E0;
}

.folder-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #F8F8F8;
  cursor: pointer;
  border-bottom: 1px solid #E0E0E0;
  transition: background-color 0.2s;
}

.folder-header:hover {
  background-color: #F0F0F0;
}

.folder-icon {
  margin-right: 12px;
  font-size: 18px;
}

.folder-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
  color: #075E54;
}

.folder-toggle {
  font-size: 12px;
  color: #8C8C8C;
  transition: transform 0.2s;
}

.folder-header.collapsed .folder-toggle {
  transform: rotate(-90deg);
}

.folder-content {
  max-height: 500px;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding-bottom: 24px;
}

.folder-content.collapsed {
  max-height: 0;
}

.test-folder .test-item {
  padding-left: 48px;
}

/* Progress bar for test execution */
.progress-container {
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-size: 14px;
  font-weight: 500;
  color: #075E54;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 500;
  color: #25D366;
}

.progress-bar-track {
  width: 100%;
  height: 4px;
  background-color: #F0F0F0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: #25D366;
  border-radius: 2px;
  transition: width 0.3s ease-in-out;
  width: 0%;
}

/* Enhanced loading animations */
.loading-container-enhanced {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  padding: 16px;
  margin: 20px auto;
  width: 100%;
  max-width: 500px;
  box-sizing: border-box;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.loading-spinner-enhanced {
  width: 32px;
  height: 32px;
  border: 4px solid rgba(37, 211, 102, 0.2);
  border-radius: 50%;
  border-top-color: #25D366;
  animation: spin-enhanced 1.2s ease-in-out infinite;
}

.loading-text-enhanced {
  font-size: 16px;
  font-weight: 500;
  color: #075E54;
  text-align: center;
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes spin-enhanced {
  to { transform: rotate(360deg); }
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Test title styling */
.test-subtitle {
  font-size: 12px;
  color: #8C8C8C;
  font-weight: normal;
  margin-top: 2px;
}
