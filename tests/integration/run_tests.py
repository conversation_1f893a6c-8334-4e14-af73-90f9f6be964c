"""Testes de integração para o Conversas.AI"""

import logging
import os

from conn import redis_client
from tests import tests
from tests.engine import TestSuite

logger = logging.getLogger("__main__")

BASE_API_URL = os.getenv("BASE_API_URL", "http://localhost:8300")

TEST_SERVER_URL = os.getenv("TEST_SERVER_URL", "http://web:8042")


def get_tests_names() -> list[str]:
    """
    Obtém os nomes de todos os testes disponíveis.

    :return:
        list: Lista de nomes dos testes.
    """
    return list(tests.keys())


def get_test(test_name: str) -> TestSuite | None:
    """
    Obtém o teste com o nome especificado.

    :param str test_name: Nome do teste a ser obtido.
    :return:
        TestSuite: O teste correspondente ao nome fornecido ou None se não encontrado.
        None: Se o teste não for encontrado.
    """
    test_name = test_name.strip().lower()
    test = tests.get(test_name)
    if not test:
        logger.error(f"Teste {test_name} não encontrado.")
        return None
    return test()


def get_test_phone_number(test_name: str) -> str | None:
    """
    Obtém o número de telefone do teste.

    :param str test_name: Nome do teste a ser obtido.
    :return:
        str: Número de telefone do teste.
        None: Se o teste não for encontrado ou se o número não estiver definido.
    """
    test = get_test(test_name)
    if not test:
        logger.error(f"Teste {test_name} não encontrado.")
        return None
    return test.phone


def get_test_id_empresa(test_name: str) -> str | None:
    """
    Obtém o ID da empresa do teste.

    :param str test_name: Nome do teste a ser obtido.
    :return:
        str: ID da empresa do teste.
        None: Se o teste não for encontrado ou se o ID não estiver definido.
    """
    test = get_test(test_name)
    if not test:
        logger.error(f"Teste {test_name} não encontrado.")
        return None
    return test.id_empresa


def set_test(test_name) -> bool:
    """
    Define o teste a ser executado.

    :param str test_name: Nome do teste a ser definido.
    :return:
        bool: True se o teste foi definido com sucesso, False caso contrário.
    """
    test = get_test(test_name)

    if not test:
        logger.error(f"Teste {test_name} não encontrado.")
        return False

    # Verifica se o teste já está em execução
    if is_running(test_name):
        logger.error(f"Teste {test_name} já está em execução.")
        return False

    # Verifica se o setup do teste já foi executado
    setup_happended = redis_client.get(f"tests:{test_name}:setup")
    # Se o setup não foi executado, executa-o
    if test.setup_func is not None and not setup_happended:
        if test.setup_func(test) is not False:
            redis_client.set(f"tests:{test_name}:setup", "1")
        else:
            logger.error("Erro ao executar setup do teste.")
            return False
    else:
        logger.info("Setup já foi executado ou não é necessário.")

    logger.info(f"Definindo o teste: {test_name}")
    # Define o teste como "Rodando"
    redis_client.hset("tests:running", test_name, "1")
    # Adiciona o teste à fila de execução
    redis_client.lpush("tests:queued", test_name)
    return True


def test_suite(test_name: str) -> TestSuite | None:
    """
    Obtém a instância do teste em execução.

    :param str test_name: Nome do teste a ser obtido.
    :return:
        TestSuite: A instância do teste correspondente ao nome fornecido ou None se não encontrado.
        None: Se o teste não for encontrado ou não estiver em execução.
    """
    if test := get_test(test_name):
        test_running = redis_client.hget("tests:running", test_name)
        if test_running:
            return test

    return None


def get_next_message(test_name: str) -> str | None:
    """
    Obtém a próxima mensagem a ser enviada para o Conversas.AI.

    :return:
        str: A próxima mensagem a ser enviada.
        None: Se não houver mais mensagens ou se o teste não estiver em execução.
    """
    next_message = redis_client.brpop(f"tests:{test_name}:next_message", 0)
    if next_message:
        if next_message[1] == "FINISH":
            logger.info("Teste finalizado.")
            return None
        message = next_message[1]
        return message
    else:
        logger.info("Nenhuma mensagem disponível.")


def ready_for_next_message(test_name: str, response: str | None = None) -> None:
    """
    Envia a próxima mensagem para o gravador de teste.

    :param str test_name: Nome do teste a ser enviado.
    :param str response: Resposta a ser adicionada à última mensagem.
    :return:
        None: Sem retorno.
    """
    test = test_suite(test_name)
    if response:
        test.add_response_to_last_message(response)
        logger.info(f"Resposta adicionada à última mensagem: {response}")

    next_message = test.get_next_message()
    if next_message:
        redis_client.rpush(f"tests:{test.test_name}:next_message", next_message)
        logger.info(f"Próxima mensagem enviada: {next_message}")
    else:
        redis_client.rpush(f"tests:{test.test_name}:next_message", "FINISH")
        logger.info("Nenhuma mensagem disponível.")


def add_log_to_last_message(test_name: str, log: dict) -> None:
    """
    Adiciona um log à última mensagem enviada.

    :param str test_name: Nome do teste a ser enviado.
    :param dict response: Log a ser adicionado à última mensagem.
    :return:
        None: Sem retorno.
    """
    test = test_suite(test_name)
    if test:
        test.add_log_to_last_message(log)


def is_running(test_name: str) -> bool:
    """
    Verifica se o teste está em execução.

    :return:
        bool: True se o teste estiver em execução, False caso contrário.
    """
    if test_suite(test_name) is None:
        logger.info(f"Teste {test_name} não encontrado.")
        return False
    if not redis_client.hget("tests:running", test_name):
        logger.info(f"Teste {test_name} não está em execução.")
        return False

    return True


def is_last_message(test_name: str) -> bool:
    """
    Verifica se a última mensagem foi enviada.

    :return:
        bool: True se a última mensagem foi enviada, False caso contrário.
    """
    if not test_suite(test_name):
        return False
    return test_suite(test_name).message_count >= len(
        test_suite(test_name).test_messages
    )


def is_rede(test_name: str) -> bool:
    """Verifica se o teste está em modo rede"""
    if not test_suite(test_name):
        return False
    return test_suite(test_name).chain_mode


def finish(test_name: str) -> None:
    """
    Finaliza o teste, garantindo que todos os recursos sejam liberados.

    :param str test_name: Nome do teste a ser finalizado.
    :return:
        None: Sem retorno.
    """
    test_suite(test_name).finish()
    logger.info("Teste finalizado.")
