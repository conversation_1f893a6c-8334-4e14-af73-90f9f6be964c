import logging
import os
from argparse import ArgumentParser

import requests

from run_tests import (
    get_tests_names,
    set_test,
    ready_for_next_message,
    get_test,
    get_test_id_empresa,
)

TEST_SERVER_URL = os.getenv("TEST_SERVER_URL", "http://localhost:8042")

print("__main__")
print(logging.INFO)

if __name__ == "__main__":
    parser = ArgumentParser(
        description="Executa os testes de integração do Conversas.AI"
    )
    parser.add_argument(
        "-i",
        "--interactive",
        default=True,
        action="store_true",
        help="Modo interativo para selecionar e rodar testes.",
    )
    parser.add_argument(
        "-t",
        "--test",
        type=str,
        help="Nome do teste selecionado. Se não for especificado"
        " o argumento --run, irei te mostrar detalhes do teste.",
    )
    parser.add_argument(
        "-l",
        "--list",
        action="store_true",
        help="Lista todos os testes disponíveis.",
    )
    parser.add_argument(
        "-r",
        "--run",
        action="store_true",
        help="Executa o teste especificado. Se não for especificado, todos os testes serão executados.",
    )

    args = parser.parse_args()

    if not (any(vars(args).values())):
        parser.print_help()
        exit(1)

    if args.interactive:
        try:
            from InquirerPy import inquirer
        except ImportError:
            print("Por favor, instale o pacote InquirerPy: pip install InquirerPy")
            print("Ou rode com -i false para desativar o modo interativo.")
            exit(1)
        try:
            tests_names = get_tests_names()
            # Selector for mode
            while True:
                # Selector for mode
                mode = inquirer.select(
                    message="Selecione o modo:",
                    choices=[
                        {"name": "Normal", "value": "normal"},
                        {"name": "Rede", "value": "rede"},
                        {"name": "Outros", "value": "outros"},
                    ],
                    default="normal",
                    vi_mode=True,
                ).execute()
                # Filter tests by mode
                if mode == "rede":
                    filtered_tests = [t for t in tests_names if t.endswith("rede")]
                elif mode == "normal":
                    filtered_tests = [t for t in tests_names if t.endswith("normal")]
                else:
                    filtered_tests = [
                        t
                        for t in tests_names
                        if not (t.endswith("rede") or t.endswith("normal"))
                    ]
                if not filtered_tests:
                    print("Nenhum teste disponível para o modo selecionado.")
                    exit(0)
                filtered_tests = sorted(filtered_tests)
                # Show tests in Title Case, but keep original value
                choices = [
                    {"name": t.replace("_", " ").title(), "value": t}
                    for t in filtered_tests
                ]
                choices.insert(0, {"name": "< Voltar", "value": "__back"})
                selected = inquirer.checkbox(
                    message="Selecione os testes para rodar (Espaço para marcar, Enter para rodar):",
                    choices=choices,
                    instruction="Use ↑/↓ para navegar, espaço para selecionar, enter para rodar.",
                    transformer=lambda result: ", ".join(
                        [c["name"] for c in choices if c["value"] in result]
                    ),
                    vi_mode=True,
                ).execute()
                if "__back" in selected:
                    continue
                if not selected:
                    print("Nenhum teste selecionado.")
                    exit(0)
                if len(selected) == 1:
                    test_name = selected[0]
                    response = requests.post(
                        f"{TEST_SERVER_URL}/pannel?test_name={test_name}"
                    )
                    if response.status_code == 200:
                        print(f"Teste {test_name} definido.")
                    else:
                        print(f"teste {test_name} não será executado.")
                elif len(selected) > 1:
                    response = requests.post(
                        f"{TEST_SERVER_URL}/pannel", data={"tests": selected}
                    )
                    if response.status_code == 200:
                        print("Testes definidos.")
                    else:
                        print("Os testes não serão executados.")
                break
        except KeyboardInterrupt:
            pass
        exit(0)

    if args.list:
        tests_names = get_tests_names()
        print("Testes disponíveis:")
        for test_name in tests_names:
            print(f"- {test_name}")
        exit(0)

    if args.test and args.run:
        test_name = args.test
        if test_name not in get_tests_names():
            print(f"Teste {test_name} não encontrado.")
            exit(1)
        set_test(test_name)
        ready_for_next_message(test_name)
        print(f"Teste {test_name} definido.")

    elif args.run:
        tests_names = get_tests_names()
        for test_name in tests_names:
            set_test(test_name)
            ready_for_next_message(test_name)
            print(f"Teste {test_name} definido.")

    elif args.test:
        test_name = args.test
        if test_name not in get_tests_names():
            print(f"Teste {test_name} não encontrado.")
            exit(1)
        test = get_test(test_name)
        if test:
            print(f"Teste: {test_name}")
            print(f"ID da empresa: {get_test_id_empresa(test_name)}")
            print(f"Modo de rede: {test.chain_mode}")
            print("Mensagens do teste:")
            for message in test.test_messages:
                print(f"- {message.message}")
        else:
            print(f"Teste {test_name} não encontrado.")
