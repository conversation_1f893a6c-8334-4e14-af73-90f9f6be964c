#!/usr/bin/env python3
import os
import json
import time
import argparse
import requests
from getpass import getpass
from datetime import datetime, timedelta

# Configuration
def get_config():
    """Load configuration from environment variables or defaults"""
    
    # Get base URL from environment variable or use default
    base_url = os.getenv("BASE_URL", "https://api.conversas.ai")
    
    # Remove trailing slash if present
    if base_url.endswith('/'):
        base_url = base_url[:-1]
    
    # Default configuration with all URLs derived from base_url
    config = {
        "BASE_URL": base_url,
        "AUTH_URL": f"{base_url}/auth/",
        "MEMORIES_URL": f"{base_url}/users/memories",
        "TOKEN_FILE": os.path.expanduser(os.getenv("TOKEN_FILE", "~/.orion_token.json"))
    }
    
    return config

def get_saved_token():
    """Retrieve saved token if it exists and is still valid"""
    config = get_config()
    TOKEN_FILE = config["TOKEN_FILE"]
    if not os.path.exists(TOKEN_FILE):
        return None
    
    try:
        with open(TOKEN_FILE, 'r') as f:
            token_data = json.load(f)
        
        # Check if token is still valid (with 5 minute buffer)
        expiration_time = token_data.get('saved_at', 0) + token_data.get('expiration', 0) - 300
        if expiration_time > time.time():
            return token_data.get('auth_token')
    except Exception as e:
        print(f"Error reading saved token: {e}")
    
    return None

def save_token(token_data):
    """Save token data to local file"""
    data_to_save = {
        'auth_token': token_data['auth_token'],
        'expiration': token_data['expiration'],
        'saved_at': time.time()
    }
    
    try:
        config = get_config()
        TOKEN_FILE = config["TOKEN_FILE"]
        with open(TOKEN_FILE, 'w') as f:
            json.dump(data_to_save, f)
        print(f"Token saved to {TOKEN_FILE}")
    except Exception as e:
        print(f"Error saving token: {e}")

def get_auth_token(api_key):
    """Get authentication token using API key"""
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    payload = {
        "api_key": api_key
    }
    
    try:
        config = get_config()
        AUTH_URL = config["AUTH_URL"]
        response = requests.post(AUTH_URL, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        token_data = response.json()
        save_token(token_data)
        return token_data['auth_token']
    except requests.exceptions.RequestException as e:
        print(f"Authentication error: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def get_user_memories(token, empresa, telefone):
    """Get memories for a specific phone number"""
    headers = {
        "accept": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    params = {
        "id_empresa": empresa,
        "phone_number": telefone
    }
    
    try:
        config = get_config()
        MEMORIES_URL = config["MEMORIES_URL"] 
        response = requests.get(MEMORIES_URL, headers=headers, params=params, timeout=15)
        response.raise_for_status()
        
        memories = response.json()
        return memories
    except requests.exceptions.RequestException as e:
        print(f"Error retrieving memories: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Get user memories from Orion system')
    parser.add_argument('--empresa', required=True, help='Company ID (e.g., teste-1)')
    parser.add_argument('--telefone', required=True, help='Phone number with country code (e.g., +5562996026753)')
    parser.add_argument('--api-key', default="a1a3e1c3ffaa14b5f77326b9aae4294e57495aa7f6587547dce3dcc4c2f473ab", help='API key (if not provided, will be requested)')
    parser.add_argument('--dev', action='store_true', default=True, required=False, help='Use development environment (localhost:8300)')
    parser.add_argument('--prod', action='store_true', default=False, required=False, help='Use production environment (api.conversas.ai)')
    
    args = parser.parse_args()

    # Set BASE_URL environment variable based on arguments
    if args.dev:
        print("Development mode enabled. Using localhost:8300")
        os.environ["BASE_URL"] = "http://localhost:8300"
    elif args.prod:
        print("Production mode enabled. Using api.conversas.ai")
        os.environ["BASE_URL"] = "https://api.conversas.ai"
    else:
        # Default to production if no environment is specified
        os.environ["BASE_URL"] = "https://api.conversas.ai"
    
    # Get token (from saved file or by authenticating)
    token = get_saved_token()
    if not token:
        api_key = args.api_key or getpass("Enter your API key: ")
        token = get_auth_token(api_key)
        if not token:
            print("Failed to obtain authentication token. Exiting.")
            return
    
    # Get user memories
    memories = get_user_memories(token, args.empresa, args.telefone)
    print(f"Retrieved {len(memories)} memories for {args.telefone}")
    print(memories)


if __name__ == "__main__":
    main()