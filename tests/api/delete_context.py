import os
import argparse
import requests
import json

def get_or_create_cache():
    """Create cache file if it doesn't exist"""
    if not os.path.exists('storage.json'):
        with open('storage.json', 'w') as f:
            json.dump({}, f)
    return 'storage.json'

def get_cache_value(key):
    """Get value from cache (file storage.json)"""
    try:
        cache_file = get_or_create_cache()
        with open(cache_file, 'r') as f:
            data = json.load(f)
            return data.get(key)
    except json.JSONDecodeError:
        print("Error decoding JSON from cache file. Returning None.")
        return None
    except Exception as e:
        print(f"Unexpected error while reading cache: {e}")
        return None
    
def set_cache_value(key, value):
    """Set value in cache (file storage.json)"""
    try:
        cache_file = get_or_create_cache()
        with open(cache_file, 'r') as f:
            data = json.load(f)
        data[key] = value
        with open(cache_file, 'w') as f:
            json.dump(data, f, indent=4)
    except json.JSONDecodeError:
        print("Error decoding JSO<PERSON> from cache file. Unable to set value.")
    except Exception as e:
        print(f"Unexpected error while writing to cache: {e}")

def get_config():
    """Load configuration from environment variables or defaults"""
    return {
        "CONVERSAS_URL": os.getenv("CONVERSAS_URL"),
        "DISCOVERY_URL": os.getenv("DISCOVERY_URL")
    }

def get_auth_token(api_key):
    """Get authentication token using API key"""
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    payload = {
        "api_key": api_key
    }
    
    try:
        config = get_config()
        auth_url = config["CONVERSAS_URL"] + "auth/"
        response = requests.post(auth_url, headers=headers, json=payload, timeout=10)
        response.raise_for_status()
        token_data = response.json()
        return token_data['auth_token']
    except requests.exceptions.RequestException as e:
        print(f"Authentication error: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return None

def delete_context(token, empresa, telefone):
    """Delete context for a specific phone number"""
    headers = {
        "accept": "application/json",
        "Authorization": token
    }
    
    params = {
        "empresa": empresa,
        "telefone": telefone,
        "reset_chat": True
    }
    
    try:
        config = get_config()
        delete_user_context_url = config["CONVERSAS_URL"] + "apagar_contexto_aluno/"
        response = requests.delete(delete_user_context_url, headers=headers, params=params, timeout=15)
        response.raise_for_status()
        print(f"Context deleted successfully for {telefone}")
        print(f"Response: {response.json()}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"Error deleting context: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"Response: {e.response.text}")
        return False

def main():
    
    api_key = get_cache_value("api_key")
    empresa = get_cache_value("empresa")
    telefone = get_cache_value("telefone")
    no_auth = get_cache_value("no_auth")
    rede = get_cache_value("rede")
    env = get_cache_value("env")
    
    parser = argparse.ArgumentParser(description='Excluir contexto de usuário do sistema Orion')
    parser.add_argument('--empresa', required=True, help='ID da empresa (ex.: e0aa2e5c7ed462647540be7a58465a26-1)', default=empresa)
    parser.add_argument('--rede', required=False, help='ID da rede (ex.: a4eaccb9b154a6c749240f8235677ba9-5)', default=rede)
    parser.add_argument('--telefone', required=True, help='Número de telefone com código do país (ex.: +5562996026753)', default=telefone)
    parser.add_argument('--api-key', help='Chave da API (se não fornecida, será solicitada)', default=api_key)
    parser.add_argument('--env', choices=['prod', 'dev'], help='Definir o ambiente (prod ou dev)', default=env)
    parser.add_argument('--no-auth', help='Pular autenticação (para fins de teste) (t/f)', default=no_auth)

    args = parser.parse_args()
    
    set_cache_value("api_key", args.api_key)
    set_cache_value("empresa", args.empresa)
    set_cache_value("telefone", args.telefone)
    set_cache_value("no_auth", args.no_auth)
    set_cache_value("rede", args.rede)
    set_cache_value("env", args.env)
    
    auth = str(args.no_auth).startswith('f') or not args.no_auth
    
    if args.env == 'dev':
        print("Production mode enabled. This is for testing purposes only.")
        os.environ["CONVERSAS_URL"] = "http://localhost:8300/"
        os.environ["DISCOVERY_URL"] = "https://discovery.ms.pactosolucoes.com.br/"
    elif args.env == 'prod':
        print("Production mode enabled. This is for testing purposes only.")
        os.environ["CONVERSAS_URL"] = "https://api.conversas.ai/"
        os.environ["DISCOVERY_URL"] = "https://discovery.ms.pactosolucoes.com.br/"


    token = get_cache_value("token")
    if auth and not token:
        token = get_auth_token(api_key)
        if not token:
            print("Failed to obtain authentication token. Exiting.")
            return
    
    # Delete context
    delete_context(token, args.empresa, args.telefone)

if __name__ == "__main__":
    main()
