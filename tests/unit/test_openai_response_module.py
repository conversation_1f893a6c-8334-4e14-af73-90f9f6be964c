import os
import pytest
from unittest.mock import MagicMock, patch

# Set environment variables before importing the module
os.environ["MODE"] = "worker"
os.environ["DOMAIN"] = "http://example.com"

from src.worker.llm_modules.openai.openai_response_module import OpenAIResponseModule

class TestOpenAIResponseModuleInitMethods:
    """Testes para os métodos _init_system_content e _init_functions da classe OpenAIResponseModule"""

    @pytest.fixture
    def mock_dependencies(self):
        """Mock das dependências principais"""
        with patch('src.worker.llm_modules.openai.openai_response_module.bq') as mock_bq, \
             patch('src.worker.llm_modules.openai.openai_response_module.PIT') as mock_pit, \
             patch('src.worker.llm_modules.openai.openai_response_module.connections') as mock_connections, \
             patch('src.worker.llm_modules.openai.openai_response_module.GBIT') as mock_gbit:
            
            # Configure mock_bq
            mock_bq_instance = MagicMock()
            mock_bq.return_value = mock_bq_instance
            
            # Configure basic returns for bq methods
            mock_bq_instance.get_gym_context.return_value = {
                "razaoSocial": "Academia Teste",
                "nomeEstado": "São Paulo", 
                "nomeCidade": "São Paulo"
            }
            mock_bq_instance.get_phase_context.return_value = {
                "descricao_fase": "Fase de teste",
                "instrucao_ia_fase": "Objetivo de teste"
            }
            mock_bq_instance.get_config.return_value = {
                "desabilitarPlanos": False,
                "desabilitarProdutos": False,
                "desabilitarTurmas": False,
                "desabilitarAulaExperimental": False,
                "desabilitarLigacao": False,
                "desabilitarVisita": False
            }
            mock_bq_instance.get_personality_context.return_value = "Personalidade teste"
            mock_bq_instance.get_memories.return_value = {}
            mock_bq_instance.get_chain_context.return_value = []
            mock_bq_instance.get_departament_descriptions.return_value = []
            
            # Configure mock_connections
            mock_connections.openai_client = MagicMock()
            mock_connections.redis_client = MagicMock()
            mock_connections.redis_client.get.return_value = None
            mock_connections.redis_client.set.return_value = True
            
            yield {
                'bq': mock_bq,
                'pit': mock_pit,
                'connections': mock_connections,
                'gbit': mock_gbit
            }

    @pytest.fixture
    def sample_user_context(self):
        """Contexto de usuário de exemplo"""
        return {
            "aluno": {
                "pessoa": {
                    "nome": "João Silva",
                    "cpf": "12345678901"
                },
                "situacao": "LEAD",
                "codigo_lead": None
            }
        }

    def test_init_system_content_individual_structure(self, mock_dependencies, sample_user_context):
        """Testa _init_system_content para estrutura individual"""
        with patch('src.worker.llm_modules.openai.openai_response_module.dp') as mock_dp, \
             patch('src.worker.llm_modules.openai.openai_response_module.timestamp_formatado') as mock_timestamp:
            
            mock_dp.process_gym_data.return_value = "Dados da academia processados"
            mock_dp.process_user_data.return_value = "Dados do usuário processados"
            mock_timestamp.return_value = "Segunda-feira, 18 de junho de 2025"
            
            # Create instance
            module = OpenAIResponseModule(
                user_context=sample_user_context,
                id_empresa="test-123",
                phase="LEADS_HOJE",
                telefone="11999999999",
                first_message=False,
                is_group=False,
                origin="z_api",
                is_rede=False
            )
            
            # Verify attributes set by _init_system_content
            assert module.personality == "Personalidade teste"
            assert module.estrutura == "individual"
            assert module.vinculo == "com-vinculo"
            assert module.atuacao == "intramunicipal"
            assert len(module.estados) == 1
            assert len(module.cidades) == 1
            assert module.estados[0] == "São Paulo"
            assert module.cidades[0] == "São Paulo"
            
            # Verify content structure
            assert "role" in module.content
            assert module.content["role"] == "system"
            assert "content" in module.content
            assert "SEU CONTEXTO" in module.content["content"]
            assert "SEU OBJETIVO" in module.content["content"]
            assert "SUAS INSTRUÇÕES" in module.content["content"]

    def test_init_system_content_rede_structure(self, mock_dependencies, sample_user_context):
        """Testa _init_system_content para estrutura de rede"""
        # Configure chain context for rede
        mock_dependencies['bq'].return_value.get_chain_context.return_value = [
            {"estado": "São Paulo", "cidade": "São Paulo", "id_empresa": "test-123"},
            {"estado": "Rio de Janeiro", "cidade": "Rio de Janeiro", "id_empresa": "test-456"}
        ]
        
        with patch('src.worker.llm_modules.openai.openai_response_module.dp') as mock_dp, \
             patch('src.worker.llm_modules.openai.openai_response_module.timestamp_formatado') as mock_timestamp:
            
            mock_dp.process_gym_data.return_value = "Dados da academia processados"
            mock_dp.process_user_data.return_value = "Dados do usuário processados"
            mock_timestamp.return_value = "Segunda-feira, 18 de junho de 2025"
            
            # Create instance for rede
            module = OpenAIResponseModule(
                user_context=sample_user_context,
                id_empresa="test-123",
                phase="LEADS_HOJE",
                telefone="11999999999",
                first_message=False,
                is_group=False,
                origin="z_api",
                is_rede=True,
                id_matriz="matriz-123"
            )
            
            # Verify rede-specific attributes
            assert module.estrutura == "rede"
            assert module.vinculo == "sem-vinculo"  # No saved empresa in redis
            assert module.atuacao == "intermunicipal"  # Multiple states/cities
            assert len(module.estados) == 2
            assert len(module.cidades) == 2
            assert "São Paulo" in module.estados
            assert "Rio de Janeiro" in module.estados

    def test_init_functions_lead_without_codigo_lead(self, mock_dependencies, sample_user_context):
        """Testa _init_functions para LEAD sem codigo_lead"""
        # Create instance
        mock_dependencies['connections'].redis_client.get.return_value = b"False"
        module = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=False
        )
        
        # Verify function_descriptions contains expected functions for LEAD
        expected_functions = [
            "save_additional_info",
            "check_classes_day",
            "get_additional_context",
            "warn_user",
            "save_user_name",
            "end_conversation"
        ]

        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
        
        for expected_func in expected_functions:
            assert expected_func in function_descriptions_names
        
        # Verify suggest_gyms_func_name is set correctly
        assert module.suggest_gyms_func_name == "suggest_gyms"  # Single state and city

    def test_init_functions_lead_with_codigo_lead(self, mock_dependencies):
        """Testa _init_functions para LEAD com codigo_lead"""
        mock_dependencies['connections'].redis_client.get.return_value = b"False"
        user_context_with_codigo = {
            "aluno": {
                "pessoa": {"nome": "João Silva"},
                "situacao": "LEAD",
                "codigo_lead": "LEAD123"
            }
        }
        
        # Create instance
        module = OpenAIResponseModule(
            user_context=user_context_with_codigo,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=False
        )
        
        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
    
        # Verify function_descriptions contains register_origin for LEAD with codigo_lead
        assert "register_origin" in function_descriptions_names
        
        # Should also contain basic functions
        basic_functions = [
            "save_additional_info",
            "warn_user", 
            "end_conversation"
        ]
        
        for func in basic_functions:
            assert func in function_descriptions_names

    def test_init_functions_group_context(self, mock_dependencies, sample_user_context):
        """Testa _init_functions para contexto de grupo"""
        # Configure is_client as "False" to avoid early return
        mock_dependencies['connections'].redis_client.get.return_value = b"False"

        # Create instance for group
        module = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=True,  # Group context
            origin="z_api",
            is_rede=False
        )

        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
            # Verify function_descriptions contains dont_respond for groups
        assert "dont_respond" in function_descriptions_names

    def test_init_functions_rede_without_saved_empresa(self, mock_dependencies, sample_user_context):
        """Testa _init_functions para rede sem empresa salva"""
        # Configure chain context for rede
        mock_dependencies['bq'].return_value.get_chain_context.return_value = [
            {"estado": "São Paulo", "cidade": "São Paulo", "id_empresa": "test-123"},
            {"estado": "São Paulo", "cidade": "Campinas", "id_empresa": "test-456"}
        ]

        # Configure redis calls to avoid early return and ensure we reach rede logic
        # Use a function to return different values based on the key
        def mock_redis_get(key):
            if "is_client:" in key:
                return b"False"
            elif "save_empresa:" in key:
                return None
            return None

        mock_dependencies['connections'].redis_client.get.side_effect = mock_redis_get

        with patch('src.worker.llm_modules.openai.openai_response_module.convert_to_openai_tool') \
                as mock_convert:
            mock_convert.side_effect = lambda func: f"{func.__name__}"

            # Create instance for rede
            module = OpenAIResponseModule(
                user_context=sample_user_context,
                id_empresa="test-123",
                phase="LEADS_HOJE",
                telefone="11999999999",
                first_message=False,
                is_group=False,
                origin="z_api",
                is_rede=True,
                id_matriz="matriz-123"
            )

            # Verify rede-specific functions
            rede_functions = [
                "save_user_empresa",
                "save_user_name",
                "suggest_gyms_by_city"  # Same state, multiple cities
            ]

            for func in rede_functions:
                assert func in module.function_descriptions

            # Verify suggest_gyms_func_name for multiple cities in same state
            assert module.suggest_gyms_func_name == "suggest_gyms_by_city"

    def test_init_functions_gym_bot_origin_with_departments(self, mock_dependencies, sample_user_context):
        """Testa _init_functions para origem gym_bot com departamentos"""
        # Configure departments
        mock_dependencies['bq'].return_value.get_departament_descriptions.return_value = [
            {
                "departamento": "Vendas",
                "id_departamento": "dept-1",
                "descricao": "Departamento de vendas"
            }
        ]
        
        # Create instance for gym_bot origin
        module = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="gym_bot",  # gym_bot origin
            is_rede=False
        )
        
        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
        
        # Verify transfer function is included for gym_bot with departments
        assert "transfer_user_departament" in function_descriptions_names
        
        # Verify departamentos attribute is set
        assert len(module.departamentos) == 1
        assert module.departamentos[0]["departamento"] == "Vendas"

    def test_init_functions_client_status_scenarios(self, mock_dependencies, sample_user_context):
        """Testa _init_functions para diferentes cenários de status de cliente"""
        # Test scenario: is_client = "True"
        mock_dependencies['connections'].redis_client.get.return_value = b"True"

        module = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=False
        )

        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
        # Should include search_by_cpf function for existing clients
        assert "search_by_cpf" in function_descriptions_names

        # Reset mock for next test
        mock_dependencies['connections'].redis_client.reset_mock()

        # Test scenario: is_client = None (unknown status)
        mock_dependencies['connections'].redis_client.get.return_value = None

        module2 = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-456",
            phase="LEADS_HOJE",
            telefone="11888888888",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=False
        )

        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module2.function_descriptions
        ]
        # Should include register_is_client_status function for unknown status
        assert "register_is_client_status" in function_descriptions_names

    def test_init_system_content_with_memories(self, mock_dependencies, sample_user_context):
        """Testa _init_system_content com memórias do usuário"""
        # Configure memories
        mock_dependencies['bq'].return_value.get_memories.return_value = {
            "preferencias": "Gosta de musculação",
            "restricoes": "Não pode fazer exercícios de impacto"
        }

        with patch('src.worker.llm_modules.openai.openai_response_module.dp') as mock_dp, \
             patch('src.worker.llm_modules.openai.openai_response_module.timestamp_formatado') \
                as mock_timestamp:

            mock_dp.process_gym_data.return_value = "Dados da academia processados"
            mock_dp.process_user_data.return_value = "Dados do usuário processados"
            mock_timestamp.return_value = "Segunda-feira, 18 de junho de 2025"

            # Create instance
            module = OpenAIResponseModule(
                user_context=sample_user_context,
                id_empresa="test-123",
                phase="LEADS_HOJE",
                telefone="11999999999",
                first_message=False,
                is_group=False,
                origin="z_api",
                is_rede=False
            )

            # Verify memories are included in llm_context
            assert "preferencias: Gosta de musculação" in module.llm_context
            assert "restricoes: Não pode fazer exercícios de impacto" in module.llm_context

    def test_init_system_content_config_disabled_features(self, mock_dependencies,
                                                          sample_user_context):
        """Testa _init_system_content com recursos desabilitados na configuração"""
        # Configure disabled features
        mock_dependencies['bq'].return_value.get_config.return_value = {
            "config": {
                "desabilitarPlanos": True,
                "desabilitarProdutos": True,
                "desabilitarTurmas": True,
                "desabilitarAulaExperimental": True,
                "desabilitarLigacao": True,
                "desabilitarVisita": True
            }
        }

        with patch('src.worker.llm_modules.openai.openai_response_module.dp') as mock_dp, \
             patch('src.worker.llm_modules.openai.openai_response_module.timestamp_formatado') \
                as mock_timestamp:

            mock_dp.process_gym_data.return_value = "Dados da academia processados"
            mock_dp.process_user_data.return_value = "Dados do usuário processados"
            mock_timestamp.return_value = "Segunda-feira, 18 de junho de 2025"

            # Create instance
            module = OpenAIResponseModule(
                user_context=sample_user_context,
                id_empresa="test-123",
                phase="LEADS_HOJE",
                telefone="11999999999",
                first_message=False,
                is_group=False,
                origin="z_api",
                is_rede=False
            )

            # Verify config is loaded correctly
            assert module.config["disable_plans_context"] is True
            assert module.config["disable_products_context"] is True
            assert module.config["disable_class_context"] is True
            assert module.config["disable_class"] is True
            assert module.config["disable_call"] is True
            assert module.config["disable_visit"] is True

            # Verify instructions reflect disabled features
            assert "Você não pode agendar aulas experimentais" in module.llm_instructions

    def test_init_functions_disabled_class_booking(self, mock_dependencies, sample_user_context):
        """Testa _init_functions com agendamento de aulas desabilitado"""
        # Configure disabled class booking
        mock_dependencies['bq'].return_value.get_config.return_value = {
            "config": {
                "desabilitarPlanos": False,
                "desabilitarProdutos": False,
                "desabilitarTurmas": False,
                "desabilitarAulaExperimental": True,  # Disabled
                "desabilitarLigacao": False,
                "desabilitarVisita": False
            }
        }

        # Create instance
        module = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=False
        )

        function_descriptions_names = [
            fun.get("function", {}).get("name", "") for fun in module.function_descriptions
        ]
        # Verify book_class function is NOT included when disabled
        assert "book_class" not in function_descriptions_names

    def test_init_functions_suggest_gyms_func_name_selection(self, mock_dependencies, sample_user_context):
        """Testa seleção correta do suggest_gyms_func_name baseado em estados e cidades"""
        # Test case 1: Multiple states and cities -> suggest_gyms_by_state_city
        mock_dependencies['bq'].return_value.get_chain_context.return_value = [
            {"estado": "São Paulo", "cidade": "São Paulo", "id_empresa": "test-123"},
            {"estado": "Rio de Janeiro", "cidade": "Rio de Janeiro", "id_empresa": "test-456"},
            {"estado": "Minas Gerais", "cidade": "Belo Horizonte", "id_empresa": "test-789"}
        ]

        module1 = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11999999999",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=True,
            id_matriz="matriz-123"
        )

        assert module1.suggest_gyms_func_name == "suggest_gyms_by_state_city"

        # Test case 2: Single state, multiple cities -> suggest_gyms_by_city
        mock_dependencies['bq'].return_value.get_chain_context.return_value = [
            {"estado": "São Paulo", "cidade": "São Paulo", "id_empresa": "test-123"},
            {"estado": "São Paulo", "cidade": "Campinas", "id_empresa": "test-456"},
            {"estado": "São Paulo", "cidade": "Santos", "id_empresa": "test-789"}
        ]

        module2 = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11888888888",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=True,
            id_matriz="matriz-123"
        )

        assert module2.suggest_gyms_func_name == "suggest_gyms_by_city"

        # Test case 3: Single state, single city -> suggest_gyms
        mock_dependencies['bq'].return_value.get_chain_context.return_value = [
            {"estado": "São Paulo", "cidade": "São Paulo", "id_empresa": "test-123"}
        ]

        module3 = OpenAIResponseModule(
            user_context=sample_user_context,
            id_empresa="test-123",
            phase="LEADS_HOJE",
            telefone="11777777777",
            first_message=False,
            is_group=False,
            origin="z_api",
            is_rede=True,
            id_matriz="matriz-123"
        )

        assert module3.suggest_gyms_func_name == "suggest_gyms"


if __name__ == "__main__":
    pytest.main(["tests/unit/test_openai_response_module.py"])
