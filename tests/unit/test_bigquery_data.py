import os
import pandas as pd
import pytest
import json
from unittest.mock import patch, MagicMock, call

from src.data.bigquery_data import (
    get_from_empresa,
    get_from_instance,
    redis_client_get,
    redis_client_set,
    BigQueryData
)

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")

@pytest.fixture
def mock_connections():
    with patch('src.data.bigquery_data.Connections') as mock:
        yield mock

@pytest.fixture
def mock_logger():
    with patch('src.data.bigquery_data.logger') as mock:
        yield mock

@pytest.fixture
def mock_redis_client():
    with patch('src.data.bigquery_data.connections.redis_client') as mock:
        yield mock

@pytest.fixture
def mock_bigquery_client():
    with patch('src.data.bigquery_data.connections.bigquery_client') as mock:
        yield mock

class TestBigQueryData:
    def setup_class(self):
        self.bq_data = BigQueryData("1")

    def test_get_from_empresa(self, mock_redis_client, mock_bigquery_client):
        def test_cache_hit():
            mock_redis_client.get.return_value = json.dumps({"instance_id": "123", "token": "abc"})
            instance_id, token = get_from_empresa("1")
            assert instance_id == "123"
            assert token == "abc" 


        def test_cache_miss():
            mock_redis_client.get.return_value = None
            mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = MagicMock(
                to_json=lambda *args, **kwargs: json.dumps([{"instance_id": "123", "token": "abc"}])
            )
            instance_id, token = get_from_empresa("1")
            assert instance_id == "123"
            assert token == "abc"

        test_cache_hit()
        test_cache_miss()

    def test_get_from_instance(self, mock_redis_client, mock_bigquery_client):
        def test_cache_hit():
            mock_redis_client.get.return_value = json.dumps({"id_empresa": "1", "token": "abc"})
            id_empresa, token = get_from_instance("123")
            assert id_empresa == "1"
            assert token == "abc"

        def test_cache_miss():
            mock_redis_client.get.return_value = None
            mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = MagicMock(
                to_json=lambda *args, **kwargs: json.dumps([{"id_empresa": "1", "token": "abc"}])
            )
            id_empresa, token = get_from_instance("123")
            assert id_empresa == "1"
            assert token == "abc"

        test_cache_hit()
        test_cache_miss()

    def test_redis_client(self, mock_redis_client):
        def test_redis_client_get():
            mock_redis_client.get.return_value = "value"
            assert redis_client_get("key") == "value"
        
        def test_redis_client_set():
            redis_client_set("key", "value")
            mock_redis_client.set.assert_called_with("key", "value", ex=None)
            assert redis_client_get("key") == "value"

        test_redis_client_get()
        test_redis_client_set()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_gym_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_redis_client.get.return_value = json.dumps({"context": "gym"})
        
        # Chama o método a ser testado
        result = self.bq_data.get_gym_context()
        
        # Verifica o resultado
        assert result == {"context": "gym"}
        
        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("gym_context-1")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_gym_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Configura o mock para simular cache miss
        mock_redis_client.get.return_value = None

        # Simula o retorno do BigQuery
        mock_dataframe = MagicMock()
        mock_dataframe.iloc.__getitem__.return_value = {'contexto_academia_json': json.dumps({"context": "gym"})}
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = mock_dataframe

        # Chama o método a ser testado
        result = self.bq_data.get_gym_context()

        # Verifica o resultado
        assert result == {"context": "gym"}

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once_with("gym_context-1", json.dumps({"context": "gym"}), ex=28800)

    @patch('src.data.bigquery_data.Connections')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_update_gym_context(self, mock_redis_client, mock_bigquery_client, mock_connections):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 1
        self.bq_data.update_gym_context({"context": "gym"})
        mock_redis_client.get.return_value = None
        mock_redis_client.set.assert_called_with("gym_context-1", json.dumps({"context": "gym"}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.Connections')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_insert_gym_context(self, mock_redis_client, mock_bigquery_client, mock_connections):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0
        self.bq_data.update_gym_context({"context": "gym"})
        mock_redis_client.set.assert_called_with("gym_context-1", json.dumps({"context": "gym"}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_plans_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_redis_client.get.return_value = json.dumps({"plans": []})

        # Chama o método a ser testado
        result = self.bq_data.get_plans_context()

        # Verifica o resultado
        assert result == {"plans": []}

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("plans_context-1")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_plans_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Configura o mock para simular cache miss
        mock_redis_client.get.return_value = None

        # Simula o retorno do BigQuery usando um DataFrame real
        df = pd.DataFrame({'planos_json': [json.dumps({"plans": []})]})
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = df

        # Chama o método a ser testado
        result = self.bq_data.get_plans_context()

        # Verifica o resultado
        assert result == {"plans": []}

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()
        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once_with("plans_context-1", json.dumps({"plans": []}), ex=28800)


    @patch('src.data.bigquery_data.Connections')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_update_plans_context(self, mock_redis_client, mock_bigquery_client, mock_connections):
        bq_data = BigQueryData("1")
        bq_data.update_plans_context({"plans": []})
        mock_redis_client.set.assert_called_with("plans_context-1", json.dumps({"plans": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.Connections')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_insert_plans_context(self, mock_redis_client, mock_bigquery_client, mock_connections):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0
        self.bq_data.update_plans_context({"plans": []})
        mock_redis_client.set.assert_called_with("plans_context-1", json.dumps({"plans": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_phase_context_cache_hit(self, mock_redis_client):
        # Configura o mock para simular cache hit
        mock_phase_context = [
            {
                "nome_fase": "Leads Hoje",
                "descricao_fase": "Leads Hoje",
                "instrucao_ia_fase": "Instrução"
            }
        ]
        mock_redis_client.get.return_value = json.dumps(mock_phase_context)

        # Chama o método a ser testado
        result = self.bq_data.get_phase_context()

        # Verifica o resultado
        expected_result = mock_phase_context[0]
        assert result == expected_result

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("phases_context-1")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_phase_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Configura o mock para simular cache miss
        mock_redis_client.get.return_value = None

        # Simula o retorno do BigQuery
        mock_phase_context = [
            {
                "nome_fase": "Leads Hoje",
                "descricao_fase": "Leads Hoje",
                "instrucao_ia_fase": "Instrução"
            }
        ]
        df = pd.DataFrame(mock_phase_context)
        # O método espera uma lista JSON serializada
        df_json = df.to_json(orient='records')

        # Configura o mock do BigQuery para retornar o DataFrame simulado
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = df

        # Chama o método a ser testado
        result = self.bq_data.get_phase_context()

        # Verifica o resultado
        expected_result = mock_phase_context[0]
        assert result == expected_result

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once()

        # Captura os argumentos com os quais o mock foi chamado
        args, kwargs = mock_redis_client.set.call_args
        key_called = args[0]
        value_called = args[1]

        # Verifica que a chave é a esperada
        assert key_called == "phases_context-1"

        # Desserializa as strings JSON
        expected_json = json.loads(df_json)
        actual_json = json.loads(value_called)

        # Compara os objetos Python
        assert expected_json == actual_json

    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    def test_update_phases_context(self, mock_bigquery_client, mock_redis_client):
        mock_redis_client.get.return_value = None

        # Simula que a linha já existe no BigQuery
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 1

        context = [{
            "codigo": 1,
            "name": "Leads Hoje",
            "descricao": "Leads Hoje",
            "instrucao_ia": "Instrução"
        }]

        # Chama o método a ser testado
        self.bq_data.update_phases_context(context)

        # Verifica se o BigQuery foi chamado
        assert mock_bigquery_client.query.called

        # Verifica se o Redis foi atualizado
        assert mock_redis_client.set.called

    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    def test_insert_phases_context(self, mock_bigquery_client, mock_redis_client):
        mock_redis_client.get.return_value = None

        # Simula que a linha não existe no BigQuery
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0

        context = [{
            "codigo": 1,
            "name": "Leads Hoje",
            "descricao": "Leads Hoje",
            "instrucao_ia": "Instrução"
        }]

        # Chama o método a ser testado
        self.bq_data.update_phases_context(context)

        # Verifica se o BigQuery foi chamado
        assert mock_bigquery_client.query.called

        # Verifica se o Redis foi atualizado
        assert mock_redis_client.set.called

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_classes_context_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_redis_client.get.return_value = json.dumps({"classes": []})

        # Chama o método a ser testado
        result = self.bq_data.get_classes_context()

        # Verifica o resultado
        assert result == {"classes": []}

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("classes_context-1")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_classes_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Simula cache miss
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_data = {
            'turmas': [json.dumps({"classes": []})]
        }
        df = pd.DataFrame(mock_data)
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = df

        # Chama o método a ser testado
        result = self.bq_data.get_classes_context()

        # Verifica o resultado
        assert result == '{"classes": []}'

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once_with("classes_context-1", df['turmas'].iloc[0], ex=28800)

    def test_update_classes_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 1
        self.bq_data.update_classes_context({"classes": []})
        mock_redis_client.set.assert_called_with("classes_context-1", json.dumps({"classes": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    def test_insert_classes_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0
        self.bq_data.update_classes_context({"classes": []})
        mock_redis_client.set.assert_called_with("classes_context-1", json.dumps({"classes": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_products_context_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_products = {"products": []}
        mock_redis_client.get.return_value = json.dumps(mock_products)

        # Chama o método a ser testado
        result = self.bq_data.get_products_context()

        # Verifica o resultado
        assert result == mock_products

        # Verifica se o Redis foi chamado corretamente
        mock_redis_client.get.assert_called_once_with("products_context-1")
    
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_products_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Simula cache miss
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_products = {"products": []}
        produtos_json = json.dumps(mock_products)
        mock_data = {'produtos': [produtos_json]}
        df = pd.DataFrame(mock_data)
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = df

        # Chama o método a ser testado
        result = self.bq_data.get_products_context()

        # Verifica o resultado
        assert result == mock_products

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once_with("products_context-1", produtos_json, ex=28800)

    def test_update_products_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 1
        self.bq_data.update_products_context({"products": []})
        mock_redis_client.set.assert_called_with("products_context-1", json.dumps({"products": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    def test_insert_products_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0
        self.bq_data.update_products_context({"products": []})
        mock_redis_client.set.assert_called_with("products_context-1", json.dumps({"products": []}), ex=28800)
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_user_context_cache_hit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache hit
        mock_user_context = {"user": {}, "fase_atual": "LEADS_HOJE"}
        mock_redis_client.get.return_value = json.dumps(mock_user_context)

        # Chama o método
        result = self.bq_data.get_user_context(telefone)

        # Verifica o resultado
        expected_result = (mock_user_context, "LEADS_HOJE")
        assert result == expected_result

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"{telefone}-{self.bq_data.id_empresa}")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_user_context_cache_miss(self, mock_redis_client, mock_bigquery_client):
        telefone = "123456789"
        # Simula cache miss
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_user_context = {"user": {}}
        contexto_usuario_json = json.dumps(mock_user_context)
        data = {'contexto_usuario_json': [contexto_usuario_json], 'id_empresa': ['1'], 'origin_last_update': ['test_unit']}
        df = pd.DataFrame(data)
        mock_result = MagicMock()
        mock_result.to_dataframe.return_value = df
        mock_bigquery_client.query.return_value.result.return_value = mock_result

        # Chama o método
        result = self.bq_data.get_user_context(telefone)

        # Verifica o resultado
        expected_fase_atual = "LEADS_HOJE"  # Ajuste conforme a lógica do método
        expected_context = mock_user_context.copy()
        expected_context['fase_atual'] = expected_fase_atual
        expected_result = (expected_context, expected_fase_atual)
        assert result == expected_result

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        expected_context = {
            'origin_last_update': 'test_unit',
            **expected_context
        }
        # Verifica se o Redis foi atualizado
        mock_redis_client.set.assert_called_once_with(f"{telefone}-{self.bq_data.id_empresa}", json.dumps(expected_context), ex=28800)

    def test_save_user_context(self, mock_redis_client, mock_bigquery_client):
        self.bq_data.save_user_context(json.dumps({"user": {}}), "123456789", "fase_atual", "test_unit")
        mock_redis_client.set.assert_called_with("123456789-1", json.dumps({"origin_last_update":"test_unit", "user": {}, "fase_atual": "fase_atual"}), ex=28800)
        mock_bigquery_client.query.assert_called()

    def test_get_personality_context_cache_hit(self, mock_redis_client, mock_bigquery_client, mock_connections):
        # Configura o cache para retornar a personalidade
        personalidade = {"personalidade": "Voz feminina sempre."}
        mock_redis_client.get.return_value = json.dumps(personalidade)
        result = self.bq_data.get_personality_context()
        assert result == personalidade.get("personalidade")
        mock_redis_client.get.assert_called_once_with("personality_context-1")

    def test_get_personality_context_cache_miss(self, mock_redis_client, mock_bigquery_client, mock_connections):
        # Configura o cache para retornar None
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_data = {
            'personalidade': ["Voz feminina sempre."]
        }
        df = pd.DataFrame(mock_data)
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = df

        # Chama o método
        result = self.bq_data.get_personality_context()

        # Verifica o resultado
        assert result == "Voz feminina sempre."

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        mock_redis_client.set.assert_called_once_with(
            "personality_context-1",
            json.dumps({"personalidade": "Voz feminina sempre."}),
            ex=28800
        )


    def test_update_personality_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 1
        self.bq_data.update_personality_context({"personalidade": {}})
        mock_redis_client.set.assert_called_with("personality_context-1", json.dumps({"personalidade": {}}), ex=28800)
        mock_bigquery_client.query.assert_called()

    def test_insert_personality_context(self, mock_redis_client, mock_bigquery_client):
        mock_bigquery_client.query.return_value.result.return_value.total_rows = 0
        self.bq_data.update_personality_context({"personalidade": {}})
        mock_redis_client.set.assert_called_with("personality_context-1", json.dumps({"personalidade": {}}), ex=28800)
        mock_bigquery_client.query.assert_called()

    def test_save_message(self, mock_bigquery_client):
        self.bq_data.save_message("user", "Hello", "123456789")
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_last_messages_cache_hit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache hit
        mock_messages = [{"message": "Hello"}]
        mock_redis_client.get.return_value = json.dumps(mock_messages)

        # Chama o método
        result = self.bq_data.get_last_messages(telefone)

        # Verifica o resultado
        expected_df = pd.DataFrame(mock_messages)
        pd.testing.assert_frame_equal(result, expected_df)

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"last_messages-{telefone}-{self.bq_data.id_empresa}")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_last_messages_cache_miss(self, mock_redis_client, mock_bigquery_client):
        telefone = "123456789"
        # Simula cache miss
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_messages = [{"enviado_por": "user", "mensagem": "Hello"}]
        df = pd.DataFrame(mock_messages)
        mock_result = MagicMock()
        mock_result.to_dataframe.return_value = df
        mock_bigquery_client.query.return_value.result.return_value = mock_result

        # Chama o método
        result = self.bq_data.get_last_messages(telefone)

        # Verifica o resultado
        expected_df = df.iloc[::-1]  # Inverte a ordem conforme o método
        pd.testing.assert_frame_equal(result, expected_df)

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Como o método não atualiza o Redis em cache miss, não precisamos verificar o set do Redis

    def test_save_pacto_data(self, mock_redis_client, mock_bigquery_client):
        self.bq_data.save_pacto_data("login", "senha")
        mock_redis_client.set.assert_called()
        mock_bigquery_client.query.assert_called()

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_pacto_data_cache_hit(self, mock_redis_client):
        # Simula cache hit
        mock_pacto_data = {"login": "login_user", "senha": "password123"}
        mock_redis_client.get.return_value = json.dumps(mock_pacto_data)

        # Chama o método
        result = self.bq_data.get_pacto_data()

        # Verifica o resultado
        assert result == ("login_user", "password123")

        # Verifica se o Redis foi chamado
        mock_redis_client.get.assert_called_once_with(f"pacto:{self.bq_data.id_empresa}")

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_pacto_data_cache_miss(self, mock_redis_client, mock_bigquery_client):
        # Simula cache miss
        mock_redis_client.get.return_value = None

        # Simula retorno do BigQuery
        mock_data = {
            'login': ['login_user'],
            'senha': ['password123']
        }
        df = pd.DataFrame(mock_data)
        mock_result = MagicMock()
        mock_result.to_dataframe.return_value = df
        mock_bigquery_client.query.return_value.result.return_value = mock_result

        # Chama o método
        result = self.bq_data.get_pacto_data()

        # Verifica o resultado
        assert result == ("login_user", "password123")

        # Verifica se o BigQuery foi chamado
        mock_bigquery_client.query.assert_called()

        # Verifica se o resultado foi armazenado no Redis
        expected_cache_value = json.dumps({"login": "login_user", "senha": "password123"})
        mock_redis_client.set.assert_called_once_with(f"pacto:{self.bq_data.id_empresa}", expected_cache_value, ex=28800)

    @patch('src.data.bigquery_data.connections.bigquery_client')
    def test_register_log(self, mock_bigquery_client):
        # Chama o método
        self.bq_data.register_log({"data": "test"}, "test_table")

        # Verifica se o BigQuery foi chamado corretamente
        mock_bigquery_client.insert_rows_json.assert_called_once_with(
            f'{GCP_BIGQUERY_DATASET}.logs_test_table',
            [{'data': 'test'}]
        )

    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_last_messages_limit(self, mock_redis_client):
        telefone = "123456789"
        # Simula cache com menos mensagens do que o limite
        mock_redis_client.get.return_value = json.dumps([{"message": "Test"}])
        result = self.bq_data.get_last_messages(telefone, limit=5)
        assert len(result) == 1  # Apenas uma mensagem no cache

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_model_source_cache_miss(self, mock_redis_client, mock_bigquery_client):
        mock_redis_client.get.return_value = None
        mock_redis_client.exists.return_value = False
        mock_bigquery_client.query.return_value.result.return_value.to_dataframe.return_value = pd.DataFrame({"model_source": ["custom_model"]})
        result = self.bq_data.get_model_source()
        assert result == "custom_model"

    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.connections.redis_client')
    def test_get_model_source_cache_hit(self, mock_redis_client, mock_bigquery_client):
        mock_redis_client.exists.return_value = True
        mock_redis_client.get.return_value = b'custom_model'
        result = self.bq_data.get_model_source()
        assert result == "custom_model"

    @patch('src.data.bigquery_data.Connections')
    @patch('src.data.bigquery_data.redis_client_set')
    @patch('src.data.bigquery_data.BigQueryData._save_dataframe_to_bq')
    def test_save_model_source(self, mock_save_dataframe_to_bq, mock_redis_client_set, mock_connections):
        # Cria a instância da classe
        bq_data = BigQueryData("empresa-1")
        model_source = "custom_model"
        
        # Simula execução bem-sucedida
        mock_save_dataframe_to_bq.return_value = None
        
        # Chama o método
        bq_data.save_model_source(model_source)
        
        # Verifica se os dados foram salvos no Redis
        mock_redis_client_set.assert_called_once_with(
            f"model_source:{bq_data.id_empresa}",
            model_source,
            ex=28800
        )
        
        # Verifica se a query foi executada corretamente
        mock_save_dataframe_to_bq.assert_called_once_with(
            f"UPDATE {GCP_BIGQUERY_DATASET}.contexto_academia SET model_source = '{model_source}' WHERE id_empresa = '{bq_data.id_empresa}'"
        )


    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    def test_save_connected_phone(self, mock_bigquery_client, mock_redis_client):
        mock_redis_client.get.return_value = None
        telefone = "123456789"
        self.bq_data.save_connected_phone(telefone)
        # mock_redis_client.set.assert_called_once_with(f"connected_phone:{self.bq_data.id_empresa}", telefone, ex=28800)
        mock_redis_client.set.assert_has_calls([
            call(f"connected_phone:{self.bq_data.id_empresa}", telefone, ex=28800),
            call(f"empresas_telefone:{telefone}", self.bq_data.id_empresa, ex=28800)
        ])

    @pytest.mark.parametrize("exists_instance, exists_empresa, expected_query, use_id_empresa", [
        # Caso 1: exists_instance=True, exists_empresa=False
        (True, False,
        f"INSERT INTO {GCP_BIGQUERY_DATASET}.instances (instance_id, token, id_empresa) VALUES ('instance-1', 'token-123', 'empresa-2')", 
        False),
        # Caso 2: exists_instance=False, exists_empresa=True
        (False, True,
        f"UPDATE {GCP_BIGQUERY_DATASET}.instances SET instance_id = 'instance-1', token = 'token-123', id_empresa = 'empresa-2'", 
        True),
        # Caso 3: exists_instance=True, exists_empresa=True
        (True, True,
        f"UPDATE {GCP_BIGQUERY_DATASET}.instances SET instance_id = 'instance-1', token = 'token-123', id_empresa = 'empresa-2'", 
        True),
        # Caso 4: exists_instance=False, exists_empresa=False
        (False, False,
        f"INSERT INTO {GCP_BIGQUERY_DATASET}.instances (instance_id, token, id_empresa) VALUES ('instance-1', 'token-123', 'empresa-2')", 
        False),
    ])
    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.connections.bigquery_client')
    @patch('src.data.bigquery_data.redis_client_set')
    @patch('src.data.bigquery_data.BigQueryData._check_if_row_exists')
    @patch('src.data.bigquery_data.BigQueryData._execute_query')
    @patch('src.data.bigquery_data.get_from_instance')
    @patch('src.data.bigquery_data.get_from_empresa')
    def test_save_instance_data(self, 
                                mock_get_from_empresa,
                                mock_get_from_instance,
                                mock_execute_query,
                                mock_check_if_row_exists,
                                mock_redis_client_set,
                                mock_bigquery_client,
                                mock_redis_client,
                                exists_instance,
                                exists_empresa,
                                expected_query,
                                use_id_empresa
                                ):
        # Configura os mocks
        mock_get_from_instance.return_value = ("empresa-1", None)
        mock_get_from_empresa.return_value = ("instance-old", None)
        mock_check_if_row_exists.side_effect = [exists_instance, exists_empresa]
        
        # Instancia o objeto da classe
        bq_data = BigQueryData("empresa-2")

        # Chama o método
        instance_id = "instance-1"
        token = "token-123"
        bq_data.save_instance_data(instance_id, token)

        # Verifica chamadas no Redis
        mock_redis_client_set.assert_any_call(
            "instances:empresa-2",
            '{"instance_id": "instance-1", "token": "token-123"}',
            ex=28800
        )
        mock_redis_client_set.assert_any_call(
            "empresas:instance-1",
            '{"id_empresa": "empresa-2", "token": "token-123"}',
            ex=28800
        )

        # Chamadas a serem verificadas
        instance_query = f"UPDATE {GCP_BIGQUERY_DATASET}.instances SET instance_id = '', token = '' WHERE instance_id = '{instance_id}'"
        instance_call = call(query=instance_query, use_id_empresa=False)

        empresa_call = call(query=expected_query, use_id_empresa=use_id_empresa)
        
        calls = [empresa_call]

        if exists_instance:
            calls.insert(0, instance_call)

        # Verifica chamada ao método _execute_query
        mock_execute_query.assert_has_calls(calls)

    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.Connections')
    def test_create_empresa(self, mock_connections, mock_redis_client):
        data = {"nome": "Empresa Exemplo"}
        dados_json = json.dumps(data, ensure_ascii=False)

        with patch.object(self.bq_data, '_save_dataframe_to_bq', return_value=None) as mock_save:
            self.bq_data.create_empresa(data)
            mock_redis_client.set.assert_called_once_with(f"empresas:{self.bq_data.id_empresa}", dados_json, ex=28800)
            mock_save.assert_called_once()

    @patch('src.data.bigquery_data.Connections')
    def test_delete_empresa(self, mock_connections):
        with patch.object(self.bq_data, '_execute_query', return_value=None) as mock_execute:
            self.bq_data.delete_empresa()
            mock_execute.assert_called_once_with(f"DELETE FROM {GCP_BIGQUERY_DATASET}.empresas WHERE id_empresa = '{self.bq_data.id_empresa}'")

    @patch('src.data.bigquery_data.connections.redis_client')
    @patch('src.data.bigquery_data.Connections')
    def test_update_empresa(self, mock_connections, mock_redis_client):
        data = {"nome": "Empresa Atualizada"}
        dados_json = json.dumps(data, ensure_ascii=False)

        with patch.object(self.bq_data, '_save_dataframe_to_bq', return_value=None) as mock_save:
            self.bq_data.update_empresa(data)
            mock_redis_client.set.assert_called_once_with(f"empresas:{self.bq_data.id_empresa}", dados_json, ex=28800)
            mock_save.assert_called_once()

    @patch('src.data.bigquery_data.redis_client_get')
    @patch('src.data.bigquery_data.Connections')
    def test_get_empresa(self, mock_connections, mock_redis_client_get):
        # Dados esperados
        data = {"nome": "Empresa Exemplo"}
        dados_json = json.dumps(data)

        # Simula cache hit
        mock_redis_client_get.return_value = dados_json
        result = self.bq_data.get_empresa()
        assert result == data

        # Simula cache miss
        mock_redis_client_get.return_value = None

        # Mock do retorno do _execute_query para suportar to_dataframe
        mock_query_result = MagicMock()
        mock_query_result.to_dataframe.return_value = pd.DataFrame([{"contexto_empresa_json": dados_json}])
        
        with patch.object(self.bq_data, '_execute_query', return_value=mock_query_result) as mock_execute_query:
            result = self.bq_data.get_empresa()
            mock_execute_query.assert_called_once_with(f"SELECT TO_JSON_STRING(contexto_empresa_json) FROM {GCP_BIGQUERY_DATASET}.empresas")

        # Verifica o resultado do cache miss
        assert result == data

if __name__ == '__main__':
    pytest.main(args=["-vv"])
