# Testes Unitários para OpenAIResponseModule

Este documento descreve os testes unitários implementados para as funções `_init_system_content` e `_init_functions` da classe `OpenAIResponseModule`.

## Arquivo de Teste

**Localização:** `tests/unit/test_openai_response_module.py`

## Funções Testadas

### 1. `_init_system_content`

Esta função é responsável por inicializar o conteúdo do sistema para o modelo GPT, incluindo:
- Configuração da personalidade
- Definição da estrutura (individual/rede)
- Configuração do vínculo (com-vinculo/sem-vinculo)
- Definição da atuação (intramunicipal/intermunicipal)
- Criação do contexto LLM
- Configuração das instruções

### 2. `_init_functions`

Esta função é responsável por inicializar as funções disponíveis para o modelo OpenAI, baseando-se em:
- Situação do usuário (LEAD, AT, etc.)
- Configurações da empresa
- Tipo de origem (z_api, gym_bot)
- Estrutura (individual/rede)
- Status do cliente

## Cenários de Teste Implementados

### Testes para `_init_system_content`

1. **test_init_system_content_individual_structure**
   - Testa configuração para estrutura individual
   - Verifica atributos: `personality`, `estrutura`, `vinculo`, `atuacao`
   - Valida estrutura do conteúdo do sistema

2. **test_init_system_content_rede_structure**
   - Testa configuração para estrutura de rede
   - Verifica múltiplos estados e cidades
   - Valida configuração intermunicipal

3. **test_init_system_content_with_memories**
   - Testa inclusão de memórias do usuário no contexto
   - Verifica se as memórias são incluídas no `llm_context`

4. **test_init_system_content_config_disabled_features**
   - Testa configuração com recursos desabilitados
   - Verifica se as instruções refletem recursos desabilitados

### Testes para `_init_functions`

1. **test_init_functions_lead_without_codigo_lead**
   - Testa funções para LEAD sem código de lead
   - Verifica funções básicas incluídas

2. **test_init_functions_lead_with_codigo_lead**
   - Testa funções para LEAD com código de lead
   - Verifica inclusão da função `register_origin`

3. **test_init_functions_active_student**
   - Testa funções para aluno ativo (situação "AT")
   - Verifica inclusão da função `generate_train`

4. **test_init_functions_group_context**
   - Testa funções para contexto de grupo
   - Verifica inclusão da função `dont_respond`

5. **test_init_functions_rede_without_saved_empresa**
   - Testa funções para rede sem empresa salva
   - Verifica funções específicas de rede

6. **test_init_functions_gym_bot_origin_with_departments**
   - Testa funções para origem gym_bot com departamentos
   - Verifica inclusão da função `transfer_user_departament`

7. **test_init_functions_client_status_scenarios**
   - Testa diferentes cenários de status de cliente
   - Verifica funções baseadas no status (True/None/False)

8. **test_init_functions_disabled_class_booking**
   - Testa configuração com agendamento de aulas desabilitado
   - Verifica que `book_class` não é incluída

9. **test_init_functions_suggest_gyms_func_name_selection**
   - Testa seleção correta do nome da função de sugestão de academias
   - Verifica diferentes cenários: single state/city, multiple cities, multiple states

## Estrutura dos Mocks

### Dependências Mockadas

- **BigQuery (`bq`)**: Mock para operações de banco de dados
- **PactoIntegrationTools (`PIT`)**: Mock para integrações
- **Connections**: Mock para conexões Redis e OpenAI
- **GymbotIntegrationTools (`GBIT`)**: Mock para integrações gym_bot

### Configurações de Mock

- **Redis Client**: Configurado para retornar valores específicos baseados na chave
- **BigQuery Methods**: Configurados para retornar dados de teste apropriados
- **Convert to OpenAI Tool**: Mock para conversão de funções

## Como Executar os Testes

```bash
# Executar todos os testes do módulo
python -m pytest tests/unit/test_openai_response_module.py -v

# Executar um teste específico
python -m pytest tests/unit/test_openai_response_module.py::TestOpenAIResponseModuleInitMethods::test_init_system_content_individual_structure -v

# Executar com cobertura
python -m pytest tests/unit/test_openai_response_module.py --cov=src.worker.llm_modules.openai.openai_response_module
```

## Cobertura de Teste

Os testes cobrem:
- Diferentes estruturas (individual/rede)
- Diferentes situações de usuário (LEAD, AT)
- Diferentes origens (z_api, gym_bot)
- Configurações habilitadas/desabilitadas
- Diferentes status de cliente
- Contextos de grupo
- Cenários com/sem departamentos
- Cenários com/sem memórias
- Seleção de funções de sugestão de academias

## Observações Importantes

1. **Mocks Complexos**: Os testes utilizam mocks complexos para simular o comportamento do Redis e outras dependências
2. **Side Effects**: Alguns mocks utilizam `side_effect` para retornar valores diferentes baseados nos parâmetros
3. **Early Returns**: Alguns testes precisam configurar mocks específicos para evitar retornos antecipados nas funções
4. **Environment Variables**: Os testes configuram variáveis de ambiente necessárias antes de importar o módulo

## Manutenção

Ao modificar as funções `_init_system_content` ou `_init_functions`:
1. Verifique se os testes existentes ainda são válidos
2. Adicione novos testes para novos cenários
3. Atualize os mocks se necessário
4. Execute todos os testes para garantir que não há regressões
