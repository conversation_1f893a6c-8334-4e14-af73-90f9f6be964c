server {
    listen 80;
    server_name localhost;

    # Configuração básica de autenticação
    auth_basic "Restricted Access";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    # Para depuração - registra erros de autenticação mais detalhadamente
    error_log /var/log/nginx/error.log debug;
    
    location / {
        proxy_pass http://jaeger:16686;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Assegura que os arquivos estáticos são carregados corretamente
        sub_filter_once off;
        sub_filter 'href="/' 'href="/';
        sub_filter 'src="/' 'src="/';
    }

    # Permitir acesso a arquivos estáticos sem autenticação (opcional)
    location /static/ {
        auth_basic off;
        proxy_pass http://jaeger:16686/static/;
    }
}
