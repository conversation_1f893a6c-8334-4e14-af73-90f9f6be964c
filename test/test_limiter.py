import requests

url = "http://localhost:8080/atualizar_contexto_academia/"

querystring = {"empresa":"TESTE-1"}

payload = {
    "usuarioPactoLogin": "pactoconversas",
    "usuarioPactoSenha": "Subinoonibusemmarrocos1*",
    "cep": "74.270-170",
    "cidade": 1,
    "cnpj": "11.111.111/1111-11",
    "complemento": "NA RUA DO POSTO DE SAUDE DO JARDIM AMÉRICA",
    "email": "",
    "endereco": "RUA C 200",
    "estado": 1,
    "fase_lead": "LEADS_HOJE",
    "horario_funcionamento": "A academia tem funcionamento entre 05:00 até as 23h de segunda a sábado e domingo de 06:00 as 14:00",
    "inscEstadual": "ISENTO",
    "inscMunicipal": "",
    "instagram": "https://www.instagram.com/sistemapacto",
    "latitude": "",
    "longitude": "",
    "nomeFantasia": None,
    "numero": "246",
    "permiteContratosConcomitantes": False,
    "permiteHorariosConcorrentesParaProfessor": False,
    "proposito": "",
    "razaoSocial": "Equilíbrio IA",
    "setor": "JARDIM AMÉRICA",
    "site": "https://google.com",
    "sitescraping": False,
    "telComercial1": "(62)34140314",
    "telComercial2": "",
    "telComercial3": "",
    "timezoneDefault": "Brazil/East",
    "urlAgendaAulasLojaVendaOnline": "https://vendas.online.sistemapacto.com.br/agenda-aulas?un=1&k=e0aa2e5c7ed462647540be7a58465a26",
    "urlLojaVendaOnline": "https://vendas.online.sistemapacto.com.br/loja?uni=1&k=e0aa2e5c7ed462647540be7a58465a26",
    "urlPlanosLojaVendaOnline": "https://vendas.online.sistemapacto.com.br/planos?uni=1&k=e0aa2e5c7ed462647540be7a58465a26",
    "urlProdutosLojaVendaOnline": "https://vendas.online.sistemapacto.com.br/produtos?un=1&k=e0aa2e5c7ed462647540be7a58465a26"
}
headers = {
    "Content-Type": "application/json",
    "User-Agent": "insomnia/9.3.2"
}
for i in range(62):
    response = requests.request("POST", url, json=payload, headers=headers, params=querystring)

    print(response.text)
    print(response.status_code) 
