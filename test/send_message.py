import sys
import requests
import time

def send_message(message, m_type="message", update_momment = True):
    body = {}
    if update_momment:
        momment = time.time_ns() / 1_000_000
    else:
        momment = 1726855579062
    url = "http://localhost:8300/receber_mensagem"
    if m_type == "message":
        body = {
             "isStatusReply": False,
             "chatLid": "148730825166915@lid",
             "connectedPhone": "5562988887777",
             "waitingMessage": False,
             "isEdit": False,
             "isGroup": False,
             "isNewsletter": False,
             "instanceId": "3DB4A8AA8CA74006DC791E1C7791D352",
             "messageId": "E39ED711D3A6A8B057825B379A7519AF",
             "phone": "556291327271",
             "fromMe": False,
             "momment": momment,
             "status": "RECEIVED",
             "chatName": "Eu",
             "senderPhoto": None,
             "senderName": "Mateus",
             "photo": "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIAcS8PzBJzfXfqMlBhFUaHqXzWH-vGsnIrBaSOiqu2fe&oe=66FABE25&_nc_sid=5e03e0&_nc_cat=100",
             "broadcast": False,
             "participantLid": None,
             "forwarded": False,
             "type": "ReceivedCallback",
             "fromApi": False,
             "text": {
                 "message": message
             }
         }
    elif m_type == "invalid":
        body = {
             "isStatusReply": False,
             "chatLid": "148730825166915@lid",
             "connectedPhone": "556281305490",
             "waitingMessage": False,
             "isEdit": False,
             "isGroup": False,
             "isNewsletter": False,
             "instanceId": "3DB4A8AA8CA74006DC791E1C7791D352",
             "messageId": "E39ED711D3A6A8B057825B379A7519AF",
             "phone": "556281823352",
             "fromMe": True,
             "momment": momment,
             "status": "SENT",
             "chatName": "Eu",
             "senderPhoto": None,
             "senderName": "Mateus",
             "photo": "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIAcS8PzBJzfXfqMlBhFUaHqXzWH-vGsnIrBaSOiqu2fe&oe=66FABE25&_nc_sid=5e03e0&_nc_cat=100",
             "broadcast": False,
             "participantLid": None,
             "forwarded": False,
             "type": "ReceivedCallback",
             "fromApi": False,
             "text": {
                 "message": message
             }
         }
    elif m_type == "reaction":
        body = {
                "isStatusReply": False,
                "chatLid": "148730825166915@lid",
                "connectedPhone": "556282046653",
                "waitingMessage": False,
                "isEdit": False,
                "isGroup": False,
                "isNewsletter": False,
                "instanceId": "3D6DF422A8DDD0B60543BA68CA125D91",
                "messageId": "A98DA58BBB304B53442AA32562522F1D",
                "phone": "556281823352",
                "fromMe": False,
                "momment": momment,
                "status": "RECEIVED",
                "chatName": "Mateus",
                "senderPhoto": None,
                "senderName": "Mateus",
                "photo": "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIDCe6P_lUrE3UwXR5YdWJCRp6a9_dIozzBaVDb-OP-Xx&oe=67376325&_nc_sid=5e03e0&_nc_cat=100",
                "broadcast": False,
                "participantLid": None,
                "forwarded": False,
                "type": "ReceivedCallback",
                "fromApi": False,
                "reaction": {
                    "value": "\ud83d\ude02",
                    "time": momment,
                    "reactionBy": "556281823352",
                    "referencedMessage": {
                        "messageId": "3EB003127B723C9B428F63",
                        "fromMe": True,
                        "phone": "556282046653",
                        "participant": None
                    }
                }
            }

    elif m_type == "audio":
        #https://tempstorage.download/instances/3D78E970A0F5E07ABD402272D360C213/3A940CDFB3831CCA82B9.ogg
       body = {
                "isStatusReply": False,
                "chatLid": "148730825166915@lid",
                "connectedPhone": "556282046653",
                "waitingMessage": False,
                "isEdit": False,
                "isGroup": False,
                "isNewsletter": False,
                "instanceId": "3D78E970A0F5E07ABD402272D360C213",
                "messageId": "A98DA58BBB304B53442AA32562522F1D",
                "phone": "556281823352",
                "fromMe": False,
                "momment": momment,
                "status": "RECEIVED",
                "chatName": "Mateus",
                "senderPhoto": None,
                "senderName": "Mateus",
                "photo": "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIDCe6P_lUrE3UwXR5YdWJCRp6a9_dIozzBaVDb-OP-Xx&oe=67376325&_nc_sid=5e03e0&_nc_cat=100",
                "broadcast": False,
                "participantLid": None,
                "forwarded": False,
                "type": "ReceivedCallback",
                "fromApi": False,
                "audio": {
                    "value": "\ud83d\ude02",
                    "time": momment,
                    "audioUrl": "https://tempstorage.download/instances/3D78E970A0F5E07ABD402272D360C213/3A940CDFB3831CCA82B9.ogg",
                    "mimeType": "audio/ogg; codecs=opus"
                }
            }
       
    elif m_type == "group":
        body = {
                "isStatusReply": False,
                "chatLid": "148730825166915@lid",
                "connectedPhone": "556282046653",
                "waitingMessage": False,
                "isEdit": False,
                "isGroup": True,
                "isNewsletter": False,
                "instanceId": "3DB1AD1FA649405D097D86569E4C4757",
                "messageId": "A98DA58BBB304B53442AA32562522F1D",
                "phone": "11111111111-group",
                "participantPhone": "556281823352",
                "fromMe": False,
                "momment": momment,
                "status": "RECEIVED",
                "chatName": "Mateus",
                "senderPhoto": None,
                "senderName": "Mateus",
                "photo": "https://pps.whatsapp.net/v/t61.24694-24/458259204_1035396347776230_2402709047193960581_n.jpg?ccb=11-4&oh=01_Q5AaIDCe6P_lUrE3UwXR5YdWJCRp6a9_dIozzBaVDb-OP-Xx&oe=67376325&_nc_sid=5e03e0&_nc_cat=100",
                "broadcast": False,
                "participantLid": None,
                "forwarded": False,
                "type": "ReceivedCallback",
                "fromApi": False,
                "text": {
                    "message": message
                }
            }
    # from random import random
    # from time import sleep
    # for i in range(10):
    #     body["phone"] = str(random()).zfill(11)
    resp = requests.post(url=url, json=body, headers={'Origin': 'https://api.z-api.io'})
    print(resp.json())
        # sleep(0.1)

if __name__ == '__main__':
    message = sys.argv[1] if len(sys.argv) > 1 else None
    m_type = sys.argv[2] if len(sys.argv) > 2 else "message"
    update_momment = sys.argv[3] == "True" if len(sys.argv) > 3 else True
    if not message:
        message = "Oi"
    send_message(message, m_type, update_momment)
    
