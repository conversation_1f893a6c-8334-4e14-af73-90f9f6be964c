<!DOCTYPE html>
<html lang="pt-br">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document PDF Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        button:hover {
            background-color: #45a049;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .status {
            padding: 10px;
            margin-top: 15px;
            border-radius: 4px;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        #documentText {
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 15px;
            background-color: #f9f9f9;
        }

        #documentPreview {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            margin-top: 15px;
        }

        .tab {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }

        .tab button {
            background-color: inherit;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 20px;
            color: #333;
            transition: 0.3s;
        }

        .tab button:hover {
            background-color: #ddd;
        }

        .tab button.active {
            background-color: #ccc;
        }

        .tabcontent {
            display: none;
            padding: 6px 12px;
            animation: fadeEffect 1s;
        }

        @keyframes fadeEffect {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <h1>Document PDF Tester</h1>

    <div class="card">
        <div class="form-group">
            <label for="companyId">ID da Empresa:</label>
            <input type="text" id="companyId" placeholder="Ex.: empresa-1">
        </div>
    </div>

    <div class="card">
        <h2>Upload de Documento</h2>
        <div class="form-group">
            <label for="documentFile">Arquivo PDF:</label>
            <input type="file" id="documentFile" accept=".pdf">
        </div>
        <div class="form-group">
            <label for="action">Ação:</label>
            <select id="action">
                <option value="create">Criar novo documento</option>
                <option value="update">Atualizar documento existente</option>
                <option value="delete">Excluir documento</option>
            </select>
        </div>
        <button id="uploadButton" onclick="uploadDocument()">Enviar</button>
        <div id="uploadStatus" class="status" style="display: none;"></div>
    </div>

    <div class="card">
        <h2>Visualizar Documento</h2>
        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'textTab')">Texto Extraído</button>
            <button class="tablinks" onclick="openTab(event, 'pdfTab')">Visualizar PDF</button>
        </div>

        <div id="textTab" class="tabcontent" style="display: block;">
            <button id="getTextButton" onclick="getDocumentText()">Carregar Texto</button>
            <div id="documentText"></div>
            <div id="textStatus" class="status" style="display: none;"></div>
        </div>

        <div id="pdfTab" class="tabcontent">
            <button id="getPdfButton" onclick="getDocumentPdf()">Carregar PDF</button>
            <iframe id="documentPreview" src=""></iframe>
            <div id="pdfStatus" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Function to upload a document
        async function uploadDocument() {
            const companyId = document.getElementById('companyId').value;
            const fileInput = document.getElementById('documentFile');
            const action = document.getElementById('action').value;
            const statusElement = document.getElementById('uploadStatus');

            // Hide previous status messages
            statusElement.style.display = 'none';

            // Validate input
            if (!companyId) {
                showStatus('Por favor, informe o ID da empresa.', 'error', statusElement);
                return;
            }

            if (action !== 'delete' && (!fileInput.files || fileInput.files.length === 0)) {
                showStatus('Por favor, selecione um arquivo PDF.', 'error', statusElement);
                return;
            }

            try {
                // Disable button during upload
                const uploadButton = document.getElementById('uploadButton');
                uploadButton.disabled = true;

                // Create FormData and append the required data
                const formData = new FormData();
                formData.append('companyId', companyId);
                formData.append('action', action);

                if (action !== 'delete') {
                    formData.append('file', fileInput.files[0]);
                }

                // Make the request to our server
                const response = await fetch('/upload-doc', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                console.log('Response data:', data);

                if (response.ok) {
                    let message;
                    switch (action) {
                        case 'create':
                            message = 'Documento enviado com sucesso! O processamento pode levar alguns segundos.';
                            break;
                        case 'update':
                            message = 'Documento atualizado com sucesso! O processamento pode levar alguns segundos.';
                            break;
                        case 'delete':
                            message = 'Documento excluído com sucesso!';
                            break;
                    }
                    showStatus(message, 'success', statusElement);

                    // Clear file input after successful upload
                    if (action !== 'delete') {
                        fileInput.value = '';
                    }
                } else {
                    showStatus(`Erro: ${data.error || data.message || 'Falha na operação'}`, 'error', statusElement);
                }
            } catch (error) {
                console.error('Error:', error);
                showStatus(`Erro: ${error.message}`, 'error', statusElement);
            } finally {
                // Re-enable button
                document.getElementById('uploadButton').disabled = false;
            }
        }

        // Function to get the document text
        async function getDocumentText() {
            const companyId = document.getElementById('companyId').value;
            const statusElement = document.getElementById('textStatus');
            const textElement = document.getElementById('documentText');

            // Hide previous status messages and clear text
            statusElement.style.display = 'none';
            textElement.textContent = '';

            // Validate input
            if (!companyId) {
                showStatus('Por favor, informe o ID da empresa e a chave de API.', 'error', statusElement);
                return;
            }

            try {
                // Disable button during request
                const getTextButton = document.getElementById('getTextButton');
                getTextButton.disabled = true;

                // Make the request to our server
                const response = await fetch(`/get-doc-text?companyId=${encodeURIComponent(companyId)}`);

                const data = await response.json();
                console.log('Response data:', data);

                if (response.status === 200) {
                    // Success - show the text
                    textElement.textContent = data.data;
                    showStatus('Texto do documento carregado com sucesso!', 'success', statusElement);
                } else if (response.status === 202) {
                    // Document is still being processed
                    showStatus('O documento ainda está sendo processado. Tente novamente em alguns segundos.', 'info', statusElement);
                } else if (response.status === 404) {
                    // Document not found
                    showStatus('Documento não encontrado. Faça o upload primeiro.', 'error', statusElement);
                } else {
                    // Other error
                    showStatus(`Erro: ${data.error || data.message || 'Falha ao carregar o texto do documento'}`, 'error', statusElement);
                }
            } catch (error) {
                console.error('Error:', error);
                showStatus(`Erro: ${error.message}`, 'error', statusElement);
            } finally {
                // Re-enable button
                document.getElementById('getTextButton').disabled = false;
            }
        }

        // Function to get the document PDF
        async function getDocumentPdf() {
            const companyId = document.getElementById('companyId').value;
            const statusElement = document.getElementById('pdfStatus');
            const previewElement = document.getElementById('documentPreview');

            // Hide previous status messages and clear PDF
            statusElement.style.display = 'none';
            previewElement.src = '';

            // Validate input
            if (!companyId) {
                showStatus('Por favor, informe o ID da empresa e a chave de API.', 'error', statusElement);
                return;
            }

            try {
                // Disable button during request
                const getPdfButton = document.getElementById('getPdfButton');
                getPdfButton.disabled = true;

                // Make the request to our server
                const response = await fetch(`/get-doc-pdf?companyId=${encodeURIComponent(companyId)}`);

                // Check if we got a JSON response (error or pending)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    console.log('Response data:', data);

                    if (response.status === 202) {
                        // Document is still being processed
                        showStatus('O documento ainda está sendo processado. Tente novamente em alguns segundos.', 'info', statusElement);
                    } else if (response.status === 404) {
                        // Document not found
                        showStatus('Documento não encontrado. Faça o upload primeiro.', 'error', statusElement);
                    } else {
                        // Other error
                        showStatus(`Erro: ${data.error || data.message || 'Falha ao carregar o PDF'}`, 'error', statusElement);
                    }
                } else if (response.status === 200) {
                    // Success - PDF data received
                    const pdfBlob = await response.blob();
                    const pdfUrl = URL.createObjectURL(pdfBlob);
                    previewElement.src = pdfUrl;
                    showStatus('PDF carregado com sucesso!', 'success', statusElement);
                } else {
                    // Unexpected response
                    showStatus('Resposta inesperada do servidor', 'error', statusElement);
                }
            } catch (error) {
                console.error('Error:', error);
                showStatus(`Erro: ${error.message}`, 'error', statusElement);
            } finally {
                // Re-enable button
                document.getElementById('getPdfButton').disabled = false;
            }
        }

        // Function to display status messages
        function showStatus(message, type, element) {
            element.textContent = message;
            element.className = 'status ' + type;
            element.style.display = 'block';
        }

        // Function to handle tab switching
        function openTab(evt, tabName) {
            // Hide all tab content
            const tabcontent = document.getElementsByClassName("tabcontent");
            for (let i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // Remove active class from all tab buttons
            const tablinks = document.getElementsByClassName("tablinks");
            for (let i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }

            // Show the current tab and add active class to the button
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        // Handle file input changes to update UI
        document.getElementById('action').addEventListener('change', function () {
            const fileInput = document.getElementById('documentFile');
            const action = this.value;

            if (action === 'delete') {
                fileInput.disabled = true;
            } else {
                fileInput.disabled = false;
            }
        });
    </script>
</body>

</html>