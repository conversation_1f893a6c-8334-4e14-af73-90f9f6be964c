from flask import Flask, request, jsonify, send_file
import redis
import requests

app = Flask(__name__)

# Configure Redis connection
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True
)

@app.route('/')
def index():
    """Serve the HTML frontend"""
    return send_file('doc_tester.html')

@app.route('/get-doc-text')
def get_doc_text():
    """Retrieve document text from the API"""
    try:
        company_id = request.args.get('companyId')
        
        if not company_id:
            return jsonify({"error": "Company ID and API key are required"}), 400
        


        
        # Now get the document text
        doc_response = requests.get(
            url=f'http://localhost:8300/doc/text?empresa={company_id}'
        )
        
        return jsonify(doc_response.json()), doc_response.status_code
    
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/get-doc-pdf')
def get_doc_pdf():
    """Retrieve document PDF from the API"""
    try:
        company_id = request.args.get('companyId')

        # Now get the document PDF
        doc_response = requests.get(
            url=f'http://localhost:8300/doc/pdf?empresa={company_id}'
        )
        
        if doc_response.status_code == 200:
            response_data = doc_response.json()
            # Check if we got a pending status
            if isinstance(response_data.get('data'), dict) and response_data['data'].get('status') == 'pending':
                return jsonify(response_data), 202
            
            # Get the binary PDF data
            pdf_data = response_data.get('data')
            print(pdf_data[0:100])  # Print the first 100 bytes for debugging
            
            # Return the PDF data with appropriate headers
            response = app.response_class(
                pdf_data,
                mimetype='application/pdf',
                headers={
                    'Content-Disposition': f'inline; filename=document-{company_id}.pdf'
                }
            )
            return response
        else:
            return jsonify(doc_response.json()), doc_response.status_code
    
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/upload-doc', methods=['POST'])
def upload_doc():
    """Upload a document to the API"""
    try:
        company_id = request.form.get('companyId')
        action = request.form.get('action', 'create')  # create, update, or delete
        
        # Define the endpoint based on the action
        if action == 'create':
            endpoint = 'http://localhost:8300/doc'
            method = 'POST'
        elif action == 'update':
            endpoint = 'http://localhost:8300/doc'
            method = 'PUT'
        elif action == 'delete':
            endpoint = 'http://localhost:8300/doc'
            method = 'DELETE'
        else:
            return jsonify({"error": "Invalid action. Must be 'create', 'update', or 'delete'"}), 400
        
        # For create or update, we need the file
        if action in ['create', 'update']:
            # Check if a file was provided
            if 'file' not in request.files:
                return jsonify({"error": "No file part"}), 400
            
            file = request.files['file']
            
            if file.filename == '':
                return jsonify({"error": "No selected file"}), 400
            
            # Make the request with the file
            doc_response = requests.request(
                method=method,
                url=f'{endpoint}?empresa={company_id}',
                files={
                    'file': (file.filename, file.read(), 'application/pdf')
                }
            )
        else:  # Delete operation
            # For delete, we don't need a file
            doc_response = requests.delete(
                url=f'{endpoint}?empresa={company_id}',
            )
        
        return jsonify(doc_response.json()), doc_response.status_code
    
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8001, debug=True)
