from flask import Flask, request, jsonify, send_file
import redis
import json
import requests

app = Flask(__name__)

# Configure Redis connection
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True  # This ensures we get strings rather than bytes
)

@app.route('/')
def index():
    """Serve the HTML frontend"""
    return send_file('message_tester.html')

@app.route('/get-messages')
def get_messages():
    """Retrieve messages from Redis based on phone and company ID"""
    try:
        phone = request.args.get('phone')
        company_id = request.args.get('companyId')
        
        if not phone or not company_id:
            return jsonify({"error": "Phone and company ID are required"}), 400
        
        if not phone.startswith('+'):
            phone = '+' + phone
        
        # Construct the Redis key
        redis_key = f"last_messages-{phone}-{company_id}"
        
        print(f"Looking for Redis key: {redis_key}")
        
        try:
            # Test if Redis connection works
            redis_client.ping()
        except redis.exceptions.ConnectionError:
            return jsonify({"error": "Could not connect to Redis server. Make sure Red<PERSON> is running on port 5540."}), 500
        
        # Get the messages from Redis
        messages_json = redis_client.get(redis_key)
        
        if not messages_json:
            print(f"No messages found for key: {redis_key}")
            return jsonify([])
        
        # Parse the JSON string into a Python object
        try:
            messages = json.loads(messages_json)
            return jsonify(messages)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
            print(f"Raw message content: {messages_json}")
            return jsonify({"error": f"Invalid JSON format: {str(e)}"}), 500
    
    except Exception as e:
        print(f"Error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/send-message', methods=['POST'])
def send_message_proxy():
    """Proxy endpoint to forward message sending requests to avoid CORS issues"""
    try:
        # Get the JSON data from the request
        message_data = request.json
        
        if not message_data:
            return jsonify({"error": "No message data provided"}), 400
        
        # Forward the request to the target service
        target_url = 'http://localhost:8300/receber_mensagem'
        
        # Log the request details for debugging
        print(f"Forwarding request to {target_url}")
        print(f"Request data: {json.dumps(message_data, indent=2)}")
        
        # Make the request to the target service
        response = requests.post(
            url=target_url,
            json=message_data,
            headers={
                'Content-Type': 'application/json',
                'Origin': 'https://api.z-api.io'
            }
        )
        
        # Log the response
        print(f"Response status: {response.status_code}")
        try:
            response_data = response.json()
            print(f"Response data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"Response text: {response.text}")
        
        # Return the response from the target service
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except requests.RequestException as e:
        error_message = f"Error connecting to target service: {str(e)}"
        print(error_message)
        return jsonify({"error": error_message}), 502
    
    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        print(error_message)
        return jsonify({"error": error_message}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8043, debug=True)
