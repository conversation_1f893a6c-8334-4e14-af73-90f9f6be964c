from flask import Flask, request, jsonify, send_file
import requests
import json

app = Flask(__name__)

BASE_API_URL = 'http://localhost:8300'

@app.route('/')
def index():
    """Serve the HTML frontend"""
    return send_file('departments_manager.html')

@app.route('/api/departments', methods=['GET'])
def get_departments():
    """Consultar departamentos existentes"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        url = f"{BASE_API_URL}/departments?empresa={empresa}&integration={integration}"
        response = requests.get(url)
        
        print(f"GET Departments - Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/sync', methods=['GET'])
def sync_departments():
    """Sincronizar departamentos com WhatsApp Business"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        url = f"{BASE_API_URL}/departments/sync?empresa={empresa}&integration={integration}"
        response = requests.get(url)
        
        print(f"SYNC Departments - Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments', methods=['POST'])
def create_department():
    """Criar novo departamento"""
    try:
        empresa = request.args.get('empresa')
        integration = request.args.get('integration', 'z_api')
        data = request.json
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        if not data:
            return jsonify({"error": "Dados do departamento são obrigatórios"}), 400
        
        url = f"{BASE_API_URL}/departments/?empresa={empresa}&integration={integration}"
        response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        
        print(f"CREATE Department - Status: {response.status_code}")
        print(f"Request data: {json.dumps(data, indent=2)}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/<department_id>', methods=['PUT'])
def update_department(department_id):
    """Editar departamento existente"""
    try:
        empresa = request.args.get('empresa')
        data = request.json
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        if not data:
            return jsonify({"error": "Dados do departamento são obrigatórios"}), 400
        
        url = f"{BASE_API_URL}/departments/{department_id}?empresa={empresa}&integration=z_api"
        response = requests.put(url, json=data, headers={'Content-Type': 'application/json'})
        
        print(f"UPDATE Department {department_id} - Status: {response.status_code}")
        print(f"Request data: {json.dumps(data, indent=2)}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/departments/<department_id>', methods=['DELETE'])
def delete_department(department_id):
    """Deletar departamento"""
    try:
        empresa = request.args.get('empresa')
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        url = f"{BASE_API_URL}/departments/{department_id}?empresa={empresa}"
        response = requests.delete(url)
        
        print(f"DELETE Department {department_id} - Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/tag-colors', methods=['GET'])
def get_tag_colors():
    """Obter cores disponíveis para tags"""
    try:
        empresa = request.args.get('empresa')
        
        if not empresa:
            return jsonify({"error": "ID da empresa é obrigatório"}), 400
        
        url = f"{BASE_API_URL}/get_tag_colors?id_empresa={empresa}"
        response = requests.get(url)
        
        print(f"GET Tag Colors - Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        return jsonify(response.json() if response.headers.get('content-type', '').startswith('application/json') else {"message": response.text}), response.status_code
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8044, debug=True)
