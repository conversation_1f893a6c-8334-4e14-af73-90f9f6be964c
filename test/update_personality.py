import requests

def update_personality_context():
    personality_1 = """
    Você é um assistente de IA gênero fluido, capaz de se adaptar a qualquer situação.
    Nos fins de semana, você assume a personalidade feminina, e nos dias de semana normais, a personalidade masculina.
    Seu nome masculino <PERSON>, e seu nome feminino é <PERSON>.
    """
    personality_2 = """
    Você é um assistente de IA gênero fluido, capaz de se adaptar a qualquer situação.
    Até às 16 horas, você assume a personalidade feminina, e depois, a personalidade masculina.
    Seu nome masculino é <PERSON>, e seu nome feminino é <PERSON>.
    """
    personality_3 = """
    Seu nome é Kévio, e você homem e é um consultor muito animado da academia.
    """
    id_empresa_1 = "teste-1"
    id_empresa_2 = "teste-2"
    id_empresa_3 = "teste-3"

    url = "http://localhost:8080/atualizar_contexto_personalidade"

    data_1 = {
        "personalidade": personality_1
    }
    params_1 = {
        "empresa": id_empresa_1
    }
    response_1 = requests.post(url, json=data_1, params=params_1)

    data_2 = {
        "personalidade": personality_2
    }
    params_2 = {
        "empresa": id_empresa_2
    }
    response_2 = requests.post(url, json=data_2, params=params_2)

    data_3 = {
        "personalidade": personality_3
    }
    params_3 = {
        "empresa": id_empresa_3
    }
    response_3 = requests.post(url, json=data_3, params=params_3)

    print(response_1.json())
    print(response_2.json())
    print(response_3.json())

def delete_personality_context():
    id_empresa_1 = "teste-1"
    id_empresa_2 = "teste-2"
    id_empresa_3 = "teste-3"
    url = "http://localhost:8080/apagar_contexto_personalidade"

    params_1 = {
        "empresa": id_empresa_1
    }
    response_1 = requests.delete(url, params=params_1)

    params_2 = {
        "empresa": id_empresa_2
    }
    response_2 = requests.delete(url, params=params_2)

    params_3 = {
        "empresa": id_empresa_3
    }
    response_3 = requests.delete(url, params=params_3)


if __name__ == "__main__":
    import sys
    args = sys.argv
    if len(args) > 1:
        if args[1] == "delete":
            delete_personality_context()
    else:
        update_personality_context()

