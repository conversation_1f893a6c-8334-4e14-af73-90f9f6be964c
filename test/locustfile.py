from locust import HttpUser, TaskSet, task, between

class UserBehavior(TaskSet):

    @task
    def send_request(self):
        self.client.post(
            url="/enviar_mensagem/?empresa=549a5e74402160039b0e694789505a7e-2",
            headers={
                "Content-Type": "application/json",
                "User-Agent": "insomnia/9.3.2"
            },
            json={
                "aluno": {
                    "matricula": "001120132",
                    "situacao": "VI",
                    "pessoa": {
                        "codigo": 119234,
                        "nome": "Mateus",
                        "cpf": "",
                        "nomeMae": "",
                        "nomePai": "",
                        "dataNasc": None,
                        "dataCadastro": "2023-10-28T19:26:06.048+00:00",
                        "sexo": "",
                        "naturalidade": "",
                        "nacionalidade": "",
                        "estadoCivil": "",
                        "grauInstrucao": None,
                        "profissao": None,
                        "telefonesconsulta": "556281823352",
                        "cpfPai": "",
                        "cpfMae": "",
                        "nomeConsulta": "Mateus",
                        "genero": "",
                        "emailPai": "",
                        "emailMae": "",
                        "nomeRegistro": None,
                        "enderecos": [],
                        "emails": []
                    },
                    "codigo": 185234,
                    "nomesocial": "",
                    "freepass": None,
                    "fase_crm": "0",
                    "empresa": "0002"
                }
            }
        )

class WebsiteUser(HttpUser):
    tasks = [UserBehavior]
    wait_time = between(1, 5)
    host = "https://orion.pactosolucoes.com.br"
