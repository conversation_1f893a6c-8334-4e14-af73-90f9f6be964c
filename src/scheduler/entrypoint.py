from src.connections.connections import Connections
from multiprocessing import Process
import logging
from src.extras.util import monitor_health
from src.worker.health_check.start_health_check import start_health_check_server
from src.scheduler.scheduler_worker import run_scheduler
from src.scheduler.execute_worker import run_execute
from src.scheduler.notification_scheme_worker import run_notification_scheme

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

if __name__ == "__main__":
    logger.info("Starting scheduler...")
    process_scheduler = Process(target=monitor_health(run_scheduler))
    process_scheduler.start()

    process_worker = Process(target=monitor_health(run_execute))
    process_worker.start()

    process_worker = Process(target=monitor_health(run_notification_scheme))
    process_worker.start()

    health_check_process = Process(target=start_health_check_server)
    health_check_process.start()

    
