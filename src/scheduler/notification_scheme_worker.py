import logging
import json

from src.extras.util import resolve_schedules
from src.data.bigquery_data import BigQueryData as bq
from src.connections.connections import Connections

logging.basicConfig(
    level=logging.INFO,
    format='[SCHEDULER] %(asctime)s - %(levelname)s -  %(name)s - %(message)s'
)
logger = logging.getLogger("conversas_logger")


# Cria conexão robusta com Redis
redis_conn = Connections.get_instance().redis_client


def run_notification_scheme():
    """
    Processo principal que monitora a fila notification_scheme
    e processa os textos em notificações estruturadas
    """
    logger.info("Iniciando worker para processamento de esquemas de notificação")

    while True:

        task = redis_conn.blpop("notification_scheme", timeout=30)

        if task:
            task_data = json.loads(task[1].decode('utf-8'))
            logger.info(
                "Recebida nova solicitação de processamento de notificação...")
            logger.info(f"Dados recebidos: {task_data}")

            scheduler_text = task_data.get("scheduler_text")
            id_empresa = task_data.get("id_empresa")
            notification_type = task_data.get("notification_type")
            category = task_data.get("category")
            logger.info(f"ID da empresa: {id_empresa}")

            status_key = f"notification_scheme:status:{id_empresa}:{notification_type}"
            if category:
                status_key += f":{category}"

            redis_conn.set(
                status_key, "processing", ex=24 * 60 * 60
            )

            if not scheduler_text:
                logger.info(
                    "Texto da estratégia de notificação não fornecido na tarefa"
                )
                continue

            bq_ = bq(id_empresa=id_empresa)
            logger.info(f"Conectando ao BigQuery para a empresa: {id_empresa}")

            try:
                notification_data = resolve_schedules(
                    task_data, bq_
                )

                if not notification_data:
                    logger.info(
                        "Nenhum dado de notificação encontrado para o texto fornecido")
                    continue

                logger.info(
                    f"Dados de notificação encontrados: {notification_data}"
                )

                bq_.save_notification_schema(
                    scheduler_text, notification_data, notification_type, category=category
                )
            except Exception as e:
                logger.error(
                    f"Erro ao processar o esquema de notificação: {e}"
                )
                redis_conn.set(
                    status_key, "error", ex=24 * 60 * 60
                )
                continue

            logger.info(
                f"Esquema de notificação processado: {notification_data}"
            )
            redis_conn.set(
                status_key, "success", ex=24 * 60 * 60
            )
