import redis
from rq import Worker
import os
import logging
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

redis_conn = redis.from_url(
    os.getenv("REDIS_URL", "redis://localhost:6379/0"),                
    socket_timeout=60,           
    socket_connect_timeout=30,   
    socket_keepalive=True,       
    health_check_interval=15,    
    retry_on_timeout=True,      
    #retry=3
)

def run_execute():
    while True:
        try:
            logger.info("O Worker está aguardando tarefas...")
            worker = Worker(["default"], connection=redis_conn)
            worker.work()
        
        except (redis.exceptions.TimeoutError, redis.exceptions.ConnectionError) as e:
            logger.warning(f"Timeout. Reconectando em 3 segundos...")
            time.sleep(3)  # Espera um pouco antes de tentar novamente
            continue
        except Exception as e:
            print(f"Erro ao iniciar o worker: {e}")
            continue
