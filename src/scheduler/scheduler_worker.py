import json
import logging
import os
import time
from datetime import timedelta
from multiprocessing import Process

import redis
from rq import Queue
from rq_scheduler import Scheduler
from rq.exceptions import NoSuchJobError
from rq.job import Job

from src.extras.util import task_sender

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")
scheduler_queue = os.environ.get("RQ_QUEUE_NAME", "scheduler_queue")


redis_conn: redis.Redis = redis.from_url(
    os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    socket_timeout=60,
    socket_connect_timeout=30,
    socket_keepalive=True,
    health_check_interval=15,
    retry_on_timeout=True,
    # retry=3
)
queue = Queue('default', connection=redis_conn)


def run_scheduler_forever(scheduler: Scheduler):
    """
    Executa o scheduler indefinidamente.
    """
    while True:
        try:
            scheduler.run()
        except redis.exceptions.ConnectionError as e:
            logger.error(f"Erro de conexão com o Redis: {e}")
            time.sleep(5)  # Espera um pouco antes de tentar novamente
        except Exception as e:
            logger.error(f"Erro inesperado: {e}")
            time.sleep(5)  # Espera um pouco antes de tentar novamente


def run_scheduler():
    scheduler = Scheduler(queue=queue, connection=redis_conn)

    scheduler_process = Process(
        target=run_scheduler_forever,
        args=(scheduler,),
        name="SchedulerProcess"
    )
    scheduler_process.start()

    logger.info("Scheduler iniciado com sucesso!")

    while True:
        try:
            task = redis_conn.blpop(scheduler_queue)
            if task:
                task = json.loads(task[1].decode('utf-8'))
                seconds = task.get("seconds", 0)

                task_key = task.get("key", None)

                if task.get("action") == "unschedule":
                    # Remove o job da lista de jobs agendados
                    jobs = redis_conn.lrange(f"{task_key}:jobs", 0, -1)
                    for job_id in jobs:
                        if not isinstance(job_id, bytes):
                            logger.warning("Job ID is not bytes for some reason!")
                            continue

                        job_id = job_id.decode('utf-8')

                        logger.info(f"Cancelado Job {job_id}")
                        try:
                            # Isto é necessário pois a biblioteca tem um bug
                            # Na hora de cancelar o job, ela deveria cancelar
                            # Em lugares, e acaba não fazendo isso completamente
                            job = Job.fetch(id=job_id, connection=redis_conn)
                            job.cancel()
                        except NoSuchJobError:
                            logger.error("Job %s não encontrado", job_id)

                        scheduler.cancel(job_id)

                    redis_conn.delete(f"{task_key}:jobs")
                    logger.info("Agendamento anterior cancelado com sucesso!")
                    continue

                logger.info("Scheduler recebeu uma solicitação do Worker.")

                job = scheduler.enqueue_in(
                    timedelta(seconds=seconds),
                    task_sender,
                    "messages_received",
                    **task["kwargs"]
                )

                if task_key:
                    redis_conn.lpush(f"{task_key}:jobs", job.id)

                logger.info("Tarefa agendada com sucesso!")

        except (redis.exceptions.TimeoutError, redis.exceptions.ConnectionError) as e:
            logger.warning("Timeout. Reconectando em 3 segundos...")
            time.sleep(3)  # Espera um pouco antes de tentar novamente
            continue
