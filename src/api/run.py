import os
import logging
import socket
from waitress import serve
import debugpy

from src.api.app import create_app
from src.api.migrate import run_redis_migrations
from src.extras.util import add_custom_handler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")
add_custom_handler(logger)

app = create_app()

def is_port_in_use(host: str, port: int) -> bool:
    """Verifica se a porta está em uso."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.bind((host, port))
            return False
        except socket.error:
            return True

if __name__ == "__main__":
    with app.app_context():
        run_redis_migrations()
    
    env = os.getenv("FLASK_ENV", "development")
    google_login_doc = os.getenv("GOOGLE_LOGIN_DOC", "false")
    debugpy_port = int(os.getenv("DEBUGPY_PORT", "5677"))  # Porta configurável via variável de ambiente
    logger.info(f"Running in {env} environment, with google login in docs set to {google_login_doc}")

    logger.info("Running in API mode")
    
    if env == "development":
        # Verifica se é o processo principal (não um subprocesso do hot reload)
        if os.environ.get("WERKZEUG_RUN_MAIN") != "true":
            logger.info("Processo principal detectado, inicializando debugpy")
            if not is_port_in_use("0.0.0.0", debugpy_port):
                try:
                    # Inicia o debugpy para depuração remota
                    debugpy.listen(("0.0.0.0", debugpy_port))
                    logger.info(f"Debugpy iniciado, aguardando conexão do debugger na porta {debugpy_port}")
                    # Opcional: pausar até o debugger se conectar (remova se não necessário)
                    # debugpy.wait_for_client()
                except RuntimeError as e:
                    logger.error(f"Falha ao iniciar debugpy na porta {debugpy_port}: {str(e)}")
                    raise
            else:
                logger.warning(f"Porta {debugpy_port} já está em uso, pulando inicialização do debugpy")

    if env == "production" or env == "loadtesting":
        threads = int(os.getenv("WAITRESS_THREADS", 4))
        connection_limit = int(os.getenv("WAITRESS_CONNECTION_LIMIT", 200))
        cleanup_interval = int(os.getenv("WAITRESS_CLEANUP_INTERVAL", 10))
        channel_timeout = int(os.getenv("WAITRESS_CHANNEL_TIMEOUT", 60))
        backlog = int(os.getenv("WAITRESS_BACKLOG", 1024))
        asyncore_use_poll = os.getenv("WAITRESS_ASYNCORE_USE_POLL", "true") == "true"
        logger.info("============API CONFIG============")
        logger.info("Starting waitress server")
        logger.info(f"Waitress config:")
        logger.info(f"Threads: {threads}")
        logger.info(f"Connection limit: {connection_limit}")
        logger.info(f"Cleanup interval: {cleanup_interval}")
        logger.info(f"Channel timeout: {channel_timeout}")
        logger.info(f"Backlog: {backlog}")
        logger.info(f"Asyncore use poll: {asyncore_use_poll}")
        logger.info("=================================")
        serve(
            app,
            host="0.0.0.0",
            port=8080,
            threads=threads,
            connection_limit=connection_limit,
            cleanup_interval=cleanup_interval,
            channel_timeout=channel_timeout,
            backlog=backlog,
            asyncore_use_poll=asyncore_use_poll,
        )
    elif env == "development" and google_login_doc == "true":
        app.run(host="0.0.0.0", port=8080, debug=True, ssl_context="adhoc")
    else:
        app.run(host="0.0.0.0", port=8080, debug=True)
