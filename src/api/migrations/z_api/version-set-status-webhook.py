def migrate():
    from src.connections.connections import Connections
    import requests
    import os

    GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")

    connections = Connections.get_instance()
    bigquery_client = connections.bigquery_client

    def get_instances_from_bq():
        """
        Método para buscar os dados das instâncias do BigQuery.
        """
        query = f"""
            SELECT distinct instance_id, token, id_empresa
            FROM {GCP_BIGQUERY_DATASET}.instances
        """
        try:
            query_job = bigquery_client.query(query)
            results = query_job.result()
            return [
                {
                    "instance_id": row["instance_id"],
                    "token": row["token"],
                    "id_empresa": row["id_empresa"]
                }
                for row in results if row["instance_id"] != 'None' and row["token"] != 'None'
            ]
        except Exception as e:
            return []
    
    instances = get_instances_from_bq()
    # PUT https://api.z-api.io/instances/SUA_INSTANCIA/token/SEU_TOKEN/update-webhook-message-status
    #{
    #  "value": "https://endereco-do-seu-sistema.com.br/instancia/SUA_INSTANCIA/status"
    #}
    z_api_client_token = os.getenv("Z_API_CLIENT_TOKEN")
    for instance in instances:
        url = f"https://api.z-api.io/instances/{instance['instance_id']}/token/{instance['token']}/update-webhook-message-status"
        data = {
            "value": f"{os.getenv('DOMAIN', 'https://orion.pactosolucoes.com.br/')}/status_mensagem",
        }
        response = requests.put(url, json=data, headers={'client-token': z_api_client_token})
        if response.status_code == 200:
            print(f"Webhook atualizado para a instância {instance['instance_id']}")
        else:
            print(f"Erro ao atualizar o webhook para a instância {instance['instance_id']}")
            print(response.text)