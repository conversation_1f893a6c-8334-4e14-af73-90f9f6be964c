from flask import current_app
import json

KEYS = [
    "+5562999834111-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562991218846-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562984435417-60af85257bcbbea7569312fe6ab602ed-1",
    "+5551985644591-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981471777-60af85257bcbbea7569312fe6ab602ed-1",
    "+5516982448789-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562982572234-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562982259776-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981059696-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981935215-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562992561196-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562982363446-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562996026753-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562996671522-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562996111033-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981823352-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981823352-810f691394b9cf79d33459a08861f3ed-1",
    "+5511994674719-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981635341-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981335575-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562999386624-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562991306803-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562993890479-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562985644591-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562984361512-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562982380220-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562983320654-60af85257bcbbea7569312fe6ab602ed-1"

]

CURRENT_MIGRATION = "version-adicionar-fase-contexto-aluno"


def migrate():
    try:
        add_fase_atual_to_users()
        message = "realizada com sucesso."
        color = "green"
    except Exception as e:
        message = f"falhou. Erro: {str(e)}"
        color = "red"
    finally:
        print(f"Migração {CURRENT_MIGRATION} {message}", color)


def add_fase_atual_to_users():
    for key in KEYS:
        user = current_app.redis_client.get(key)
        if user:
            user = json.loads(user)
            user['fase_atual'] = '0'
            user = json.dumps(user)
            current_app.redis_client.set(key, user)