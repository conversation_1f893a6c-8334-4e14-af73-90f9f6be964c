from flask import current_app


def migrate():
    redis_client = current_app.redis_client
    target_value = '{"fase_atual": "LEADS_HOJE"}'

    # Itera por todas as chaves no Redis
    for key in redis_client.scan_iter():
        try:
            # Obtém o valor da chave
            value = redis_client.get(key).decode('utf-8')
            # Compara o valor com o alvo
            if value == target_value:
                # Apaga a chave se o valor for igual
                redis_client.delete(key)
                print(f"Chave deletada: {key.decode('utf-8')}")
        except Exception as e:
            print(f"Erro ao processar a chave {key}: {e}")
