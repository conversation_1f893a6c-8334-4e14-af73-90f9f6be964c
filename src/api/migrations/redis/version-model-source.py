from flask import current_app
import logging
logger = logging.getLogger("conversas_logger")

def migrate():
    try:
        redis_client = current_app.redis_client
        keys = [key.decode("utf-8") for key in redis_client.keys('model_source:*')]
        for key in keys:
            a = redis_client.delete(key)
            logger.info(f"Deletou {key}, resultado: {a}")

    except Exception as e:
        print(f"Error: {e}", "red")
