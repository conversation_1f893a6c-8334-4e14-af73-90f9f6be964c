from flask import current_app

def migrate():
    try:
        redis_client = current_app.redis_client
        keys = redis_client.keys("current_conversation:*")
        for key in keys:
            value = redis_client.get(key)
            new_name = '-'.join(str(key.decode('utf-8')).split('-')[:-2])
            redis_client.set(new_name, value)
            redis_client.delete(key)

    except Exception as e:
        print(f"Error: {e}")
