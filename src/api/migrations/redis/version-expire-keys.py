# redis_migration_script.py

from flask import current_app
import re

# --- Definição dos Padrões de Chave e Tempos de Expiração (em segundos) ---
# Cada entrada é uma tupla (regex_pattern, expiration_seconds).
# A ordem é crucial: padrões mais específicos devem vir antes de padrões mais genéricos.
# Use `[^:]+` para corresponder a qualquer caractere exceto dois pontos (:)
# Use `[^-]+` para corresponder a qualquer caractere exceto hífen (-)
# Use `[^_]+` para corresponder a qualquer caractere exceto underscore (_)
# Use `$` para ancorar o fim da string e evitar correspondências parciais indesejadas.
KEY_EXPIRATIONS_REGEX = [
    # Expirações de 5 minutos
    (r"^messages_tasks:[^:]+:[^:]+:task_id$", 5 * 60),

    # Expirações de 30 dias
    (r"^is_client:[^:]+:[^:]+$", 30 * 24 * 60 * 60),

    # Expirações de 8 horas (a maioria)
    (r"^google-oauth:[^:]+$", 8 * 60 * 60),
    (r"^contexto_campanhas:[^:]+$", 8 * 60 * 60),
    (r"^notification_scheme:status:[^:]+:[^:]+$", 8 * 60 * 60), # Ex: notification_scheme:status:empresa:type
    (r"^notification_scheme:[^:]+$", 8 * 60 * 60), # Ex: notification_scheme:type
    (r"^docs:pending:[^:]+$", 8 * 60 * 60),
    (r"^memories:[^:]+:[^:]+$", 8 * 60 * 60),
    (r"^registros_meta_diaria:[^:]+$", 8 * 60 * 60),
    (r"^google_maps:[^_]+_[^_]+$", 8 * 60 * 60), # Ex: google_maps:address_components
    (r"^[^-]+:objecoes$", 8 * 60 * 60), # Ex: {id_empresa}:objecoes
    (r"^meta_diaria:[^:]+$", 8 * 60 * 60),
    (r"^system_command$", 8 * 60 * 60),
    (r"^function_descriptions:[^:]+$", 8 * 60 * 60),
    (r"^system_context:[^:]+:[^:]+$", 8 * 60 * 60),
    (r"^ground_rules:[^:]+:[^:]+$", 8 * 60 * 60),
    (r"^gymbot:[^:]+:[^:]+$", 8 * 60 * 60),
    # Padrões específicos para chaves que devem expirar
    (r"^instances:[^:]+$", 8 * 60 * 60),
    (r"^empresas:[^:]+$", 8 * 60 * 60),
    (r"^is_rede:[^:]+$", 8 * 60 * 60),
    (r"^empresas_telefone:[^:]+$", 8 * 60 * 60),
    (r"^sticker-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^gym_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^plans_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^phases_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^classes_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^products_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^personality_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^chain_context-[a-zA-Z0-9_-]+$", 8 * 60 * 60),
    (r"^responsavel:[^:]+$", 8 * 60 * 60),
    (r"^pacto:[^:]+$", 8 * 60 * 60),
    (r"^model_source:[^:]+$", 8 * 60 * 60),
    (r"^connected_phone:[^:]+$", 8 * 60 * 60),
    (r"^notification_schema:[^:]+$", 8 * 60 * 60),
    (r"^messager_channel:[^:]+$", 8 * 60 * 60),
    (r"^gymbot_token:[^:]+$", 8 * 60 * 60),
    # Padrão genérico para chaves no formato [a-zA-Z0-9_-]+-[a-zA-Z0-9_-]+, exceto as não expiráveis
    (r"^last_messages-.*$", 24 * 60 * 60),

    # Padrão genérico para chaves no formato [+a-zA-Z0-9_-]+-.*, exceto as não expiráveis
    (r"^(?!(current_conversation|save_empresa|migration|voice_schedule)-)[\+a-zA-Z0-9_-]+-.*$", 8 * 60 * 60),
]

def migrate():
    """
    Varre as chaves do Redis e aplica a expiração com base nos padrões predefinidos.
    Aplica a expiração apenas se a chave não tiver um TTL (-1).
    """
    r = current_app.redis_client
    cursor = 0
    keys_scanned = 0
    keys_expired = 0
    keys_skipped_existing_ttl = 0
    keys_skipped_no_match = 0

    print("Iniciando a migração de chaves do Redis...")

    while True:
        # Usa SCAN para iterar sobre as chaves em blocos (chunks)
        # Ajuste 'count' conforme a necessidade para otimizar o desempenho vs. carga no Redis
        cursor, keys = r.scan(cursor=cursor, count=1000)

        if not keys:
            break # Não há mais chaves

        for key in keys:
            keys_scanned += 1
            key = key.decode('utf-8')
            ttl = r.ttl(key)

            if ttl == -1: # A chave existe, mas não tem expiração
                matched = False
                for pattern, expiration_seconds in KEY_EXPIRATIONS_REGEX:
                    if re.match(pattern, key):
                        try:
                            r.expire(key, expiration_seconds)
                            print(f"  Definido EXPIRE para a chave '{key}' para {expiration_seconds} segundos (padrão: '{pattern}')")
                            keys_expired += 1
                            matched = True
                            break # Move para a próxima chave assim que uma correspondência é encontrada e a expiração é aplicada
                        except Exception as e:
                            print(f"  Erro ao definir EXPIRE para a chave '{key}': {e}")
                            matched = True # Trata como correspondente para evitar contagem de "sem correspondência"
                            break
                if not matched:
                    keys_skipped_no_match += 1
                    # Descomente a linha abaixo para ver as chaves que não corresponderam a nenhum padrão
                    # print(f"  Ignorada chave '{key}': Nenhum padrão de expiração correspondente encontrado.")
            elif ttl >= 0: # A chave já tem uma expiração
                keys_skipped_existing_ttl += 1
                # Descomente a linha abaixo para ver as chaves que já tinham TTL
                # print(f"  Ignorada chave '{key}': Já possui TTL de {ttl} segundos.")
            # else: ttl == -2, a chave não existe (não deve acontecer com resultados de SCAN)

        if cursor == 0:
            break # O SCAN completou a iteração

    print("\n--- Resumo da Migração ---")
    print(f"Total de chaves verificadas: {keys_scanned}")
    print(f"Chaves com nova expiração definida: {keys_expired}")
    print(f"Chaves ignoradas (já possuíam TTL): {keys_skipped_existing_ttl}")
    print(f"Chaves ignoradas (nenhum padrão correspondente encontrado): {keys_skipped_no_match}")
    print("Migração concluída.")
