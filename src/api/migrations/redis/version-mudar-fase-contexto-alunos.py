from flask import current_app
import json

KEYS = [
    "+5562984340200-549a5e74402160039b0e694789505a7e-2",
    "+5562991220129-549a5e74402160039b0e694789505a7e-2",
    "+5562992433357-549a5e74402160039b0e694789505a7e-2",
    "+5562986272347-549a5e74402160039b0e694789505a7e-2",
    "+5511985757804-549a5e74402160039b0e694789505a7e-2",
    "+5562981948635-549a5e74402160039b0e694789505a7e-2",
    "+5562982763702-549a5e74402160039b0e694789505a7e-2",
    "+5511916270552-549a5e74402160039b0e694789505a7e-2",
    "+5562985925065-549a5e74402160039b0e694789505a7e-2",
    "+5564996538640-549a5e74402160039b0e694789505a7e-2",
    "+5562983173014-549a5e74402160039b0e694789505a7e-2",
    "+5562984361512-549a5e74402160039b0e694789505a7e-2",
    "+5562982093130-549a5e74402160039b0e694789505a7e-2",
    "+5564999630064-549a5e74402160039b0e694789505a7e-2",
    "+5511977121638-549a5e74402160039b0e694789505a7e-2",
    "+5562981336313-549a5e74402160039b0e694789505a7e-2",
    "+5562998696193-549a5e74402160039b0e694789505a7e-2",
    "+5562993890479-549a5e74402160039b0e694789505a7e-2",
    "+5562985764440-549a5e74402160039b0e694789505a7e-2",
    "+5521986360175-549a5e74402160039b0e694789505a7e-2",
    "+5562981935215-549a5e74402160039b0e694789505a7e-2",
    "+5562936387922-549a5e74402160039b0e694789505a7e-2",
    "+5521988138826-549a5e74402160039b0e694789505a7e-2",
    "+5562993493331-549a5e74402160039b0e694789505a7e-2",
    "+5511998993639-549a5e74402160039b0e694789505a7e-2",
    "+5562984961283-549a5e74402160039b0e694789505a7e-2",
    "+5562996447243-549a5e74402160039b0e694789505a7e-2",
    "+5562984855746-549a5e74402160039b0e694789505a7e-2",
    "+5531999182341-549a5e74402160039b0e694789505a7e-2",
    "+5562992136129-549a5e74402160039b0e694789505a7e-2",
    "+5562998349189-549a5e74402160039b0e694789505a7e-2",
    "+5562992463658-549a5e74402160039b0e694789505a7e-2",
    "+5562991266650-549a5e74402160039b0e694789505a7e-2",
    "+5562993466586-549a5e74402160039b0e694789505a7e-2",
    "+5562982351410-549a5e74402160039b0e694789505a7e-2",
    "+5562992561196-549a5e74402160039b0e694789505a7e-2",
    "+5562981151308-549a5e74402160039b0e694789505a7e-2",
    "+5562996602176-549a5e74402160039b0e694789505a7e-2",
    "+5562998564656-549a5e74402160039b0e694789505a7e-2",
    "+5561982564722-549a5e74402160039b0e694789505a7e-2",
    "+5562996698865-549a5e74402160039b0e694789505a7e-2",
    "+5562983176495-549a5e74402160039b0e694789505a7e-2",
    "+5541995289811-549a5e74402160039b0e694789505a7e-2",
    "+5562983170392-549a5e74402160039b0e694789505a7e-2",
    "+5562982936901-549a5e74402160039b0e694789505a7e-2",
    "+5562999043079-549a5e74402160039b0e694789505a7e-2",
    "+5531988717122-549a5e74402160039b0e694789505a7e-2",
    "+5562982510954-549a5e74402160039b0e694789505a7e-2",
    "+5562981329626-549a5e74402160039b0e694789505a7e-2",
    "+5562991102438-549a5e74402160039b0e694789505a7e-2",
    "+5562995149756-549a5e74402160039b0e694789505a7e-2",
    "+5562986263868-549a5e74402160039b0e694789505a7e-2",
    "+5562983320654-60af85257bcbbea7569312fe6ab602ed-1",
    "250-60af85257bcbbea7569312fe6ab602ed-1",
    "380-60af85257bcbbea7569312fe6ab602ed-1",
    "680-60af85257bcbbea7569312fe6ab602ed-1",
    "245-60af85257bcbbea7569312fe6ab602ed-1",
    "199-60af85257bcbbea7569312fe6ab602ed-1",
    "246-60af85257bcbbea7569312fe6ab602ed-1",
    "1143-60af85257bcbbea7569312fe6ab602ed-1",
    "130-60af85257bcbbea7569312fe6ab602ed-1",
    "235-60af85257bcbbea7569312fe6ab602ed-1",
    "240-60af85257bcbbea7569312fe6ab602ed-1",
    "243-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562991306803-60af85257bcbbea7569312fe6ab602ed-1",
    "780-60af85257bcbbea7569312fe6ab602ed-1",
    "239-60af85257bcbbea7569312fe6ab602ed-1",
    "241-60af85257bcbbea7569312fe6ab602ed-1",
    "340-60af85257bcbbea7569312fe6ab602ed-1",
    "440-60af85257bcbbea7569312fe6ab602ed-1",
    "286-60af85257bcbbea7569312fe6ab602ed-1",
    "236-60af85257bcbbea7569312fe6ab602ed-1",
    "242-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562985764440-60af85257bcbbea7569312fe6ab602ed-1",
    "238-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562996111033-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562981335575-60af85257bcbbea7569312fe6ab602ed-1",
    "556293890479-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562993890479-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562998696193-60af85257bcbbea7569312fe6ab602ed-1",
    "+5562998696193-810f691394b9cf79d33459a08861f3ed-1",
    "+5562993890479-810f691394b9cf79d33459a08861f3ed-1",
    "+5562981305490-e0aa2e5c7ed462647540be7a58465a26-1",
    "+55629602-e0aa2e5c7ed462647540be7a58465a26-1",
    "+556299602-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984340200-e0aa2e5c7ed462647540be7a58465a26-1",
    "+556298182-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562983320654-e0aa2e5c7ed462647540be7a58465a26-1",
    "+556298332-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981823352-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562996026753-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981232046-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531984312637-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5532988812615-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5535998983616-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5546999261147-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5551996882492-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5553999293905-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5554999056460-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561991593559-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981169687-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984691652-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984777445-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991463179-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992096262-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562993174103-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562999666305-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5564992260456-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5566999103166-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5571981522350-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5571991645856-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5575999794900-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5579998334133-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581984474512-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581995123002-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581996009435-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5583993290277-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585988278897-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585991964632-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5591981900929-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5591999932618-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5593991217331-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511940273624-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511948425685-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511959400008-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511966113473-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511984863484-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5515988407457-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5516992290338-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5516996015191-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521968720585-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521979817349-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521983042358-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521991594556-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521992521842-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5524998758782-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527999786045-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527999923402-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519992629090-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562995609063-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5517996210276-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991623883-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521999036541-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585986145232-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521998810641-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585985939934-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511989129626-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5571992154301-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5583996498652-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585989571887-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561992057792-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5591983611777-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511993469175-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562985286797-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581999403461-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5575998049223-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531985762907-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585987653004-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531991866896-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981329626-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521983655137-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5547984057604-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511985757804-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5551999485416-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5512981577760-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511983492926-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5592994906710-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585981641312-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984822549-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5583988151301-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511997389911-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585987783289-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562996602176-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5553999085787-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531986470565-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527988191417-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5592981887022-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521986243139-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531997061243-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5535988758778-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562993400229-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5535999557792-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519992924884-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5584999683171-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531999514030-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5587998057659-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511981752217-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527998517125-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581985174994-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982169350-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581998367375-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511983891212-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5554991280476-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527992291101-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5591981034279-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531997752812-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511976773276-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519981102486-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5535991746678-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562998696193-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5516982230001-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521982526011-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519988020686-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5538997276146-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511999864244-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585986681809-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531997844541-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521996065681-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511989124046-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561991231676-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521991850797-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581991453969-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585988921593-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511941104205-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982786548-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5524992194678-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521967162418-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585981393932-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5566992921233-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521968115618-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992686254-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561982437185-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5563984062365-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527999507609-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5548991428800-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981352712-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5531975774477-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991102438-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5583996197006-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5563992217277-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984361512-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521994440949-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982222709-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5588996906623-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991218846-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562993890479-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5571993146349-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5585996508622-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5581983231195-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981637255-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5564999597676-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511920001001-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992463658-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982737214-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562985555076-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562996513077-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519996159630-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982151788-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511981456065-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981127561-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982480090-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981805141-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981171630-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5564984140737-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562995576117-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984161324-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982220042-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5551998221045-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984196461-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5516992885323-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511991285626-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982208080-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511985141800-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511975302995-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991702605-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5584999814323-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562985763276-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992615460-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984717121-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5516991790168-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984750954-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5527992713230-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5535984062221-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562993745453-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561981372334-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981336313-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562994630484-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511991489398-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5551994281881-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511985581283-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511991921583-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5584991114900-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5513981335845-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562999723312-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562985108155-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562985289294-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5571993106000-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982084227-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511977121638-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5591991243023-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562982497894-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5564984248692-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5519991874922-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992818060-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562992136129-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511996423146-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562998564656-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511945364812-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562986300458-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981402704-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981012372-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562998321642-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562996671522-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511992632030-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5541991037521-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5517997098296-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5514996561999-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562991267829-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562984676631-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981314419-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981404863-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5518997832112-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5599992187915-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5511995415679-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981030487-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5562981624826-e0aa2e5c7ed462647540be7a58465a26-1",
    "+556299602-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562981336313-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562991000908-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562992561196-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562992463658-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562994951233-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562983170392-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562981935215-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562983119782-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562991635375-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5551984444466-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562984340200-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562998564656-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562993890479-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5562998696193-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5551984239862-e0aa2e5c7ed462647540be7a58465a26-2",
    "+5524998842773-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5521997861104-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561998537710-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561992017632-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561991466044-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561981337039-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561996191617-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561985310004-e0aa2e5c7ed462647540be7a58465a26-1",
    "+5561996552292-e0aa2e5c7ed462647540be7a58465a26-1"
]

CURRENT_MIGRATION = "version-mudar-fase-contexto-alunos"


def migrate():
    try:
        add_fase_atual_to_users()
        message = "realizada com sucesso."
    except Exception as e:
        message = f"falhou. Erro: {str(e)}"
    finally:
        print(f"Migração {CURRENT_MIGRATION} {message}")


def add_fase_atual_to_users():
    for key in KEYS:
        user = current_app.redis_client.get(key)
        if user:
            user = json.loads(user)
            fase = user['fase_atual']
            match fase:
                case "0":
                    user['fase_atual'] = 'VINTE_QUATRO_HORAS'
                case "1":
                    user['fase_atual'] = 'POS_VENDA'
                case "2":
                    user['fase_atual'] = 'CONHECER_USUARIO'
                case "4":
                    user['fase_atual'] = 'POS_VENDA'
                case "27":
                    user['fase_atual'] = 'LEADS_HOJE'
            user = json.dumps(user)
            current_app.redis_client.set(key, user)
