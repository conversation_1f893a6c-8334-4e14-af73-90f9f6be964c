from flask import current_app
import logging
import json
logger = logging.getLogger("conversas_logger")

def migrate():
    
    try:
        page_size = 1000
        start = 0
        it = 0
        while True:
            queue_size = current_app.redis_client.llen("task_queue_bd_contexts")
            end = min(int(queue_size) - 1, start + page_size - 1)
            elementos = current_app.redis_client.lrange("task_queue_bd_contexts", start, end)

            logger.info(f"[MUDA_FILA] (iteracao {it}) tamanho da fila task_queue_bd_contexts: {queue_size}")
            logger.info(f"[MUDA_FILA] migrando elemento do {start} ao {end} no task_queue_bd_contexts")
            
            count_del = 0
            for item in elementos:
                item = item.decode("utf-8")
                task = json.loads(item)

                if task.get("type") == "gymbot_status":
                    current_app.redis_client.lrem("task_queue_bd_contexts", 1, item)
                    task["provedor"] = "gymbot"
                    current_app.redis_client.lpush("save_status", json.dumps(task))
                    count_del += 1

                elif task.get("type") == "status_mensagem":
                    current_app.redis_client.lrem("task_queue_bd_contexts", 1, item)
                    task["provedor"] = "z_api"
                    current_app.redis_client.lpush("save_status", json.dumps(task))
                    count_del += 1
            
            logger.info(f"[MUDA_FILA] quantidade de elementos excluidos nessa pagina: {count_del} no task_queue_bd_contexts")

            if end == int(queue_size) - 1: break
            start += (page_size - count_del)
            it +=1

    except Exception as e:
        logger.info(f"[MUDA_FILA] (ERROR) tamanho da fila task_queue_bd_contexts depois do migration: {queue_size}")
        print(f"Error: {e}", "red")