from flask import current_app

def migrate():
    try:
        redis_client = current_app.redis_client
        keys = redis_client.keys()
        queue_name = "task_queue_bd_contexts"
        queue = redis_client.lrange(queue_name, 0, -1)
        for item in queue:
            print(item)
            redis_client.lrem(queue_name, 0, item)
        print(f"Queue {queue_name} reseted")
    except Exception as e:
        print(f"Error: {e}", "red")
