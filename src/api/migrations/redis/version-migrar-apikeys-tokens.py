from flask import current_app
import json
import logging
from src.data.bigquery_data import BigQueryData as bq
from src.api.app.auth.tokens_manager import JWT

bq_ = bq(id_empresa=None)
logger = logging.getLogger("conversas_logger")

def migrate():
    try:
        redis_client = current_app.redis_client
        
        migrate_auth_tokens(redis_client)
        
        migrate_api_keys(redis_client)
        
        logger.info("Migração finalizada.")
    except Exception as e:
        logger.error(f"Erro na migração: {e}", exc_info=True)

def migrate_auth_tokens(redis_client):
    try:
        keys = redis_client.keys("user_auth_tokens:*")
        
        for key in keys:
            auth_token = redis_client.get(key)
            if auth_token:
                auth_token = auth_token.decode("utf-8")
                payload = JWT.decode_auth_token(auth_token)
                api_key = payload.get("sub")
                
                if api_key:
                    salvar_auth_session(api_key, auth_token, expiration_time=current_app.config['JWT_EXPIRATION_TIME'])
                    logger.info(f"Token {auth_token} migrado com sucesso!")
    except Exception as e:
        logger.error(f"Erro ao migrar tokens de autenticação: {e}", exc_info=True)

def salvar_auth_session(api_key, auth_token, expiration_time):
    try:
        logger.info(f"Salvando sessão de autenticação no BigQuery: {api_key} - {auth_token}")
        bq_.save_auth_session(api_key=api_key, token=auth_token, expiration_time=expiration_time)
        logger.info(f"Sessão de autenticação {auth_token} salva no BigQuery com sucesso!")
    except Exception as e:
        logger.error(f"Erro ao salvar sessão {auth_token} no BigQuery: {e}", exc_info=True)

def migrate_api_keys(redis_client):
    try:
        keys = redis_client.keys("user_api_keys:*")
        
        for key in keys:
            value = redis_client.get(key)
            if value:
                data = json.loads(value)
                api_key = data.get("api_key")
                integration = data.get("integration")
                
                if api_key and integration:
                    salvar_api_key_no_bigquery(api_key, integration)
                    logger.info(f"Chave {api_key} migrada com sucesso!")
    except Exception as e:
        logger.error(f"Erro ao migrar chaves de API: {e}", exc_info=True)


def salvar_api_key_no_bigquery(api_key, integration):
    try:
        logger.info(f"Salvando API Key no BigQuery: {api_key} - {integration}")
        bq_.save_auth_api_key(api_key=api_key, user=integration, active=True)
        logger.info(f"API Key {api_key} salva no BigQuery com sucesso!")
    except Exception as e:
        logger.error(f"Erro ao salvar {api_key} no BigQuery: {e}", exc_info=True)
