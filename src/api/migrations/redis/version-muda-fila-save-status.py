from flask import current_app
import logging
import json
logger = logging.getLogger("conversas_logger")

def migrate():
    
    try:
        elementos = current_app.redis_client.lrange("task_queue_bd_contexts", 0, -1)

        for item in elementos:
            item = item.decode("utf-8")
            data = json.loads(item)
            if data.get("type") == "gymbot_status":
                current_app.redis_client.lrem("task_queue_bd_contexts", 1, item)
                current_app.redis_client.lpush("save_status_gymbot", item)

    except Exception as e:
        print(f"Error: {e}", "red")