from flask import Blueprint, request, jsonify, current_app
import hashlib
import uuid
import logging
import json
import os

from src.api.app.exception.exception import Unauthorized, BadRequest
from src.api.app.auth.utils.auth_wrappers import master_token_required
from src.api.app.auth.tokens_manager import JWT
from src.extras.config import Config
from src.data.bigquery_data import BigQueryData as bq

logger = logging.getLogger("conversas_logger")

authorization_bp = Blueprint('authorization', __name__)
registration_bp = Blueprint('registration', __name__)
unregistration_bp = Blueprint('unregistration', __name__)
get_api_keys_bp = Blueprint('apikeys', __name__)

DEV_ENV = Config.ENV == 'development'
SEPARATE_API_KEY = os.getenv('SEPARATE_API_KEY', 'false').lower() == 'true'

@authorization_bp.route('', methods=['POST'])
def authorization():
    """
    Rota para autenticar uma chave de API
    ---
    tags:
        - Segurança
    security:
        - Bearer: []
    parameters:
        - name: body
          in: body
          type: string
          required: true
          schema:
            id: authorization
            required:
                - api_key
            properties:
                api_key:
                    type: string
                    description: "Chave de API a ser autenticada"
                    example: "b4c2c4f9f6b9e1b8f6b9e1b8f6b9e1b8"
                empresa:
                    type: string
                    description: "ID da empresa"
                    example: "empresa-1"
    responses:
        200:
            description: Retorna o token de autenticação
            schema:
                type: object
                properties:
                    auth_token:
                        type: string
                    expiration:
                        type: integer
        400:
            description: Dados não informados
        401:
            description: Credenciais inválidas
    """
    api_key = request.json.get('api_key', None)
    id_empresa = request.json.get('empresa', None)

    if not SEPARATE_API_KEY:
        id_empresa = None

    bq_ = bq(id_empresa=id_empresa)

    if not id_empresa:
        logger.info("A request was made without company ID")
        if SEPARATE_API_KEY:
            raise BadRequest("Company ID is required")
    if not api_key or not bq_.get_api_key_integration(api_key)[0]:
        logger.info("A request was made with invalid credentials %s", request.json)
        raise Unauthorized("Invalid credentials")

    auth_token = JWT.encode_auth_token(api_key, id_empresa)
    ex = current_app.config['JWT_EXPIRATION_TIME']

    task_data = {
        'api_key': api_key,
        'auth_token': auth_token,
        'expiration': ex,
        'id_empresa': id_empresa,
        'task_type': 'authorization'
    }

    current_app.redis_client.rpush('task_queue_api_keys', json.dumps(task_data))
    if SEPARATE_API_KEY:
        token_key = f"auth_tokens:{id_empresa}:{auth_token}"
    else:
        token_key = f"auth_tokens:{auth_token}"
    current_app.redis_client.set(token_key, api_key, ex)

    return jsonify({'auth_token': auth_token, 'expiration': ex})

@registration_bp.route('', methods=['POST'])
@master_token_required
def register_api_key():
    """
    Rota para cadastrar uma nova chave de API
    ---
    tags:
        - Segurança
    headers:
        Authorization:
            type: string
            required: true
            description: Chave mestra de API para criação de novas chaves
    security: 
        - Bearer: []
    parameters:
        - name: body
          in: body
          type: string
          required: true
          schema:
            id: register_api_key
            required:
                - user
            properties:
                user:
                    type: string
                    description: "Nome da integração, da lista de integrações permitidas"
                    example: "pacto"
                empresa:
                    type: string
                    description: "ID da empresa"
                    example: "empresa-1"
    responses:
        200:
            description: Retorna a chave de API
            schema:
                type: object
                properties:
                    api_key:
                        type: string
        400:
            description: Dados não informados
    """
    logger.info("Registering API key")

    integration = request.json.get('user', None)
    id_empresa = request.json.get('empresa', None)

    if not integration:
        logger.info("A request was made without integration")
        raise BadRequest("Integration is required")

    if not id_empresa:
        logger.info("A request was made without company ID")
        if SEPARATE_API_KEY:
            raise BadRequest("Company ID is required")

    api_key = hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()

    task_data = {
        'api_key': api_key,
        'integration': integration,
        'id_empresa': id_empresa,
        'task_type': 'registration'  # Para identificar o tipo de tarefa
    }

    current_app.redis_client.rpush('task_queue_api_keys', json.dumps(task_data))

    return jsonify({'api_key': api_key})


@unregistration_bp.route('', methods=['POST'])
@master_token_required
def unregister_api_key():
    """
    Rota para deletar uma chave de API
    ---
    tags:
        - Segurança
    headers:
        Authorization:
            type: string
            required: true
            description: Chave mestra de API para deletar chaves
    security: 
        - Bearer: []
    parameters:
        - name: body
          in: body
          type: string
          required: true
          schema:
            id: delete_api_key
            required:
                - api_key
            properties:
                api_key:
                    type: string
                    description: "Chave de API a ser deletada"
                    example: "b4c2c4f9f6b9e1b8f6b9e1b8f6b9e1b8"
                empresa:
                    type: string
                    description: "ID da empresa"
                    example: "empresa-1"
    responses:
        200:
            description: Retorna mensagem de sucesso
            schema:
                type: object
                properties:
                    message:
                        type: string
                        example: "API key deleted"
        400:
            description: Dados não informados
    """
    api_key = request.json.get('api_key', None)

    if not api_key:
        raise BadRequest("API key is required")
    id_empresa = request.json.get('empresa', None)

    if not id_empresa:
        logger.info("A request was made without company ID")
        if SEPARATE_API_KEY:
            raise BadRequest("Company ID is required")

    task_data = {
        'api_key': api_key,
        'id_empresa': id_empresa,
        'task_type': 'deletion'
    }

    current_app.redis_client.rpush('task_queue_api_keys', json.dumps(task_data))

    return jsonify({'message': 'API key deleted'})

@get_api_keys_bp.route('', methods=['GET'])
@master_token_required
def get_api_keys():
    """
    Rota para consultar chaves de API com paginação
    ---
    tags:
        - Segurança
    headers:
        Authorization:
            type: string
            required: true
            description: Chave mestra de API para consultar chaves
    security: 
        - Bearer: []
    parameters:
        - name: empresa
          in: query
          type: string
          description: "ID da empresa (opcional)"
        - name: name
          in: query
          type: string
          description: "Nome da integração (opcional)"
        - name: page
          in: query
          type: integer
          description: "Número da página (default: 1)"
          default: 1
        - name: limit
          in: query
          type: integer
          description: "Limite de elementos por página (default: 10)"
          default: 10
    responses:
        200:
            description: "Retorna uma lista de chaves de API"
            schema:
                type: object
                properties:
                    success:
                        type: boolean
                        example: true
                    total_keys:
                        type: integer
                        example: 2
                    api_keys:
                        type: array
                        items:
                            type: object
                            properties:
                                api_key:
                                    type: string
                                    example: "abcd1234efgh5678"
                                user:
                                    type: string
                                    example: "usuario_exemplo"

        401:
            description: "Não autorizado. Chave de API inválida ou ausente."
            schema:
                type: object
                properties:
                    success:
                        type: boolean
                        example: false
                    message:
                        type: string
                        example: "Authorization required"
        400:
            description: "Erro na requisição. Os parâmetros fornecidos podem estar incorretos ou ausentes."
            schema:
                type: object
                properties:
                    success:
                        type: boolean
                        example: false
                    message:
                        type: string
                        example: "Invalid request parameters"
        404:
            description: "Nenhuma chave de API encontrada para os filtros fornecidos."
            schema:
                type: object
                properties:
                    success:
                        type: boolean
                        example: false
                    message:
                        type: string
                        example: "No API keys found"
    """
    id_empresa = request.args.get('empresa', None)
    name = request.args.get('name', None)
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))

    bq_ = bq(id_empresa=id_empresa)

    api_keys = bq_.get_api_keys(name=name, page=page, limit=limit)

    if not api_keys:
        return jsonify({
            "success": False,
            "message": "No API keys found"
        }), 404

    return jsonify({
        "success": True,
        "total_keys": len(api_keys),
        "api_keys": api_keys
    })
