import jwt
from datetime import datetime, timedelta
from flask import current_app

from src.api.app.exception.exception import Unauthorized
from src.extras.config import Config
from src.data.bigquery_data import BigQueryData as bq

AUTH_ON = Config.AUTH_ON == 'true'
require_auth = AUTH_ON


class JWT:
    @staticmethod
    def encode_auth_token(api_key: str, id_empresa: str):
        bq_ = bq(id_empresa=id_empresa)

        api_key, integration = bq_.get_api_key_integration(api_key)

        payload = {
            'exp': datetime.now() + timedelta(days=1),
            'iat': datetime.now(),
            'integration': integration,
            'company': id_empresa,
            'sub': api_key
        }
        return jwt.encode(payload, current_app.config['SECRET_KEY'], algorithm='HS256')

    @staticmethod
    def decode_auth_token(auth_token):
        try:
            payload = jwt.decode(auth_token, current_app.config['SECRET_KEY'], algorithms=['HS256'], options={"verify_signature": False})
            return payload
        except jwt.ExpiredSignatureError:
            if require_auth:
                raise Unauthorized('Signature expired')
            else:
                return {}
        except jwt.InvalidTokenError:
            if require_auth:
                raise Unauthorized('Invalid token')
            else:
                return {}
