# Python standard libraries
import json
import os

# Third-party libraries
from flask import current_app, redirect, request, Blueprint, session, url_for
from flask_login import (
    login_user
)
from oauthlib.oauth2 import WebApplicationClient
import requests
import logging

from src.api.app.models.user import User


logger = logging.getLogger("conversas_logger")

os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_OAUTH_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_OAUTH_CLIENT_SECRET")
GOOGLE_DISCOVERY_URL = (
    "https://accounts.google.com/.well-known/openid-configuration"
)

# Flask app setup
app = current_app

# OAuth 2 client setup
client = WebApplicationClient(GOOGLE_CLIENT_ID)

def get_google_provider_cfg():
    return requests.get(GOOGLE_DISCOVERY_URL).json()
        

google_login_bp = Blueprint("google_login", __name__)
callback_bp = Blueprint("google_auth", __name__)


@google_login_bp.route("")
def google_login():
    google_provider_cfg = get_google_provider_cfg()
    authorization_endpoint = google_provider_cfg["authorization_endpoint"]

    next_url = request.args.get("next", "/")

    session["next"] = next_url

    logger.info(f"next_url url: {next_url}")
    logger.info(f"Request base url: {request.base_url}")

    logger.info(f"Request base url (HTTPS): {url_for('google_auth.callback', _external=True, _scheme='https')}")

    request_uri = client.prepare_request_uri(
        authorization_endpoint,
        redirect_uri=url_for('google_auth.callback', _external=True, _scheme='https'),
        scope=["openid", "email"],
        next=request.args.get("next")
    )

    return redirect(request_uri)

@callback_bp.route("")
def callback():
    code = request.args.get("code")
    google_provider_cfg = get_google_provider_cfg()
    token_endpoint = google_provider_cfg["token_endpoint"]

    logger.info(f"Request base url: {request.base_url}")
    logger.info(f"Request base url (HTTPS): {url_for('google_auth.callback', _external=True, _scheme='https')}")
    logger.info(f"Request url: {request.url}")
    logger.info(f"Code: {code}")

    token_url, headers, body = client.prepare_token_request(
        token_endpoint,
        authorization_response=request.url,
        redirect_url=url_for('google_auth.callback', _external=True, _scheme='https'),
        code=code
    )

    token_response = requests.post(
        token_url,
        headers=headers,
        data=body,
        auth=(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET)
    )

    client.parse_request_body_response(json.dumps(token_response.json()))

    userinfo_endpoint = google_provider_cfg["userinfo_endpoint"]
    uri, headers, body = client.add_token(userinfo_endpoint)
    userinfo_response = requests.get(uri, headers=headers, data=body)

    if userinfo_response.json().get("email_verified"):
        unique_id = userinfo_response.json()["sub"]
        users_email = userinfo_response.json()["email"]
        picture = userinfo_response.json()["picture"]
    else:
        return "User email not available or not verified by Google.", 400
    
    if not userinfo_response.json()["hd"] == "pactosolucoes.com.br":
        return "User email not available or not verified", 400

    if not User.get(unique_id):
        User.create(unique_id, users_email, picture)

    user = User.get(unique_id)

    login_user(user)

    target = session.pop("next", "/")

    return redirect(target)
