import json
import os
import firebase_admin
from firebase_admin import credentials, auth
import logging

from src.extras.config import Config
from src.api.app.exception.exception import Unauthorized

logger = logging.getLogger("conversas_logger")

AUTH_ON = Config.AUTH_ON == 'true'
require_auth = AUTH_ON
gbq_credentials_file_path = os.path.join('src/connections/conversas-ai.json')

with open(gbq_credentials_file_path, "r") as file:
    CREDENTIALS = json.load(file)

cred = credentials.Certificate(CREDENTIALS)
firebase_admin.initialize_app(cred)

# Configure o cliente Pyrebase
config = {
    "apiKey": "AIzaSyCSnHdp3Ot6tKcs8y95nfz5vAircQQHjAs",
    "authDomain": "conversas-ai.firebaseapp.com",
    "storageBucket": "conversas-ai.firebasestorage.app",
    "serviceAccount": "src/connections/conversas-ai.json",
    "databaseURL": Config.REDIS_URL
}

def handle_firebase_auth(token):
    """
    Function to handle firebase authentication, used inside request context
    """
    logger.info(CREDENTIALS)
    try:
        value = auth.verify_id_token(token)
        return value

    except Exception as e:
        logger.error(f"Error authenticating user: {e}")
        if require_auth:
            raise Unauthorized("Authentication failed")
        return None
