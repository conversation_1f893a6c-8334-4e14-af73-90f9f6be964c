from flask import request, current_app, g
from functools import wraps
import logging
import os

from src.api.app.exception.exception import Unauthorized, Forbidden, BadRequest
from src.extras.config import Config
from src.api.app.auth.tokens_manager import JWT
from src.api.app.auth.firebase_auth import handle_firebase_auth
from src.data.bigquery_data import BigQueryData as bq

logger = logging.getLogger("conversas_logger")

AUTH_ON = Config.AUTH_ON == 'true'
SEPARATE_API_KEY = os.getenv('SEPARATE_API_KEY', 'false') == 'true'
require_auth = AUTH_ON

map_origin = {
    "18.215.79.89": "gym_bot", 
    "https://api.z-api.io": "z_api"
}

def authentication_required(f):
    """
    Decorator to require a valid JWT token in the request headers
    """
    @wraps(f)
    def decorator(*args, **kwargs):
        # Autenticação normal
        token = request.headers.get('Authorization', None)
        id_empresa = request.args.get('empresa', None)

        if not token:
            logger.info("A request was made without a token")
            if require_auth:
                raise Unauthorized("Authorization required")
        else:
            token = token.removeprefix('Bearer ')

        payload = JWT.decode_auth_token(token)

        company = None
        # Autenticação normal via chave de API
        if (sub := payload.get('sub', None)):
            company = payload.get('company', None)
            if not company:
                logger.info("Token was generated without related company")
                if require_auth and SEPARATE_API_KEY:
                    raise BadRequest("Token was generated without related company")

            if SEPARATE_API_KEY:
                token_key = f"auth_tokens:{company}:{token}"
            else:
                token_key = f"auth_tokens:{token}"
            if not current_app.redis_client.get(token_key):
                logger.info("A request was made with an unexpected token")
                if require_auth:
                    raise Unauthorized("Invalid token")

            bq_ = bq(id_empresa=company)
            if not bq_.get_api_key_integration(sub):
                logger.info("A request was made with invalid credentials")
                if require_auth:
                    raise Unauthorized("Invalid credentials")
            if id_empresa and company != id_empresa:
                logger.info("A request was made with invalid company")
                if require_auth and SEPARATE_API_KEY:
                    raise Unauthorized("Invalid company")

        # Autenticação via Firebase
        elif payload.get('firebase', None):
            company = payload.get('user_id', None)
            is_valid = handle_firebase_auth(token)
            if not is_valid:
                logger.info("A request was made with invalid firebase credentials")
                if require_auth:
                    raise Unauthorized("Invalid credentials")
            g.is_firebase = True
            g.user_id = company
        else:
            logger.info("A request was made with invalid user")
            if require_auth:
                raise Unauthorized("Invalid user")

        logger.info("Authentication successful")

        # Para manter a compatibilidade com as rotas antigas
        if not require_auth and not company:
            company = id_empresa

        if not SEPARATE_API_KEY and not company:
            company = id_empresa

        try:
            return f(company, *args, **kwargs)
        except TypeError:
            return f(*args, **kwargs)

    return decorator

def master_token_required(f):
    """
    Decorator to require a valid superuser token in the request headers
    """
    @wraps(f)
    def decorator(*args, **kwargs):
        master_key = request.headers.get('Authorization')

        if not master_key:
            logger.info("A request was made without master key")
            if require_auth:
                raise Forbidden("Authorization required")
        else:
            master_key = master_key.removeprefix('Bearer ')

        if master_key != current_app.config['API_MASTER_KEY']:
            logger.info("A request was made with invalid master key")
            if require_auth:
                raise Forbidden("Invalid master key")

        return f(*args, **kwargs)
    return decorator

def verify_origin(origins: list = Config.ALLOWED_ORIGINS):
    """
    Decorator to verify if the request origin is allowed

    Args:
        origins (list): List of allowed origins
    """
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            logger.info(f"\n\nHeaders:\n{request.headers}\n\n")
            origin = request.origin or request.headers.get("X-Real-Ip", None) or request.headers.get("X-Forwarded-For", None)   
            logger.info(f"Origin: {origin}")  
            if origin not in origins:
                logger.info("A request was made from an invalid origin")
                if require_auth:
                    raise Forbidden("Origin not allowed")
            kwargs["origin"] = map_origin[origin]
            return f(*args, **kwargs)
        return wrapper
    return decorator
