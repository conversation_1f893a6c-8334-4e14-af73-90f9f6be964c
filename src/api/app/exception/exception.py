class Error(Exception):
    def __init__(self, message, status_code):
        self.message = message
        self.code = status_code
        super().__init__(self.message)

    def to_dict(self):
        return {
            "error": f"{self.__class__.__name__}: {self.message}",
            "status_code": self.code,
        }
    
class BadRequest(Error):
    def __init__(self, message):
        super().__init__(message, 400)

class Unauthorized(Error):
    def __init__(self, message):
        super().__init__(message, 401)

class Forbidden(Error):
    def __init__(self, message):
        super().__init__(message, 403)
