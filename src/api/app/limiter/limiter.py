from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

from src.extras.config import Config

def route_scope(endpoint_name):
    """Define o escopo de rate limit por endpoint."""
    return endpoint_name

base_limiter = Limiter(
    get_remote_address,
    storage_uri=Config.REDIS_URL,
    storage_options={"socket_connect_timeout": 30},
    strategy="fixed-window", # or "moving-window",
    enabled=Config.RATE_LIMIT_ENABLED,
)

limiter = base_limiter.shared_limit(Config.GLOBAL_RATE_LIMIT, scope=route_scope)
