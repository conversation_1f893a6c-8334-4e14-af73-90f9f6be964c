import logging
import json
import re
from datetime import datetime
from flask import request, Request, abort, make_response, jsonify

from src.connections.connections import Connections
from src.extras.util import generate_curl_command
from src.api.app.auth.tokens_manager import JWT
from src.extras.util import RoutesTracing

CONNECTIONS = Connections.get_instance()
SQL_KEYWORDS = [
    r'\bselect\b', r'\binsert\b', r'\bupdate\b', r'\bdelete\b',
    r'\bdrop\b', r'\bunion\b', r'\bfrom\b', r'\bwhere\b',
    r'\bexec\b', r'\bcreate\b', r'\balter\b', r'\btable\b',
    r'--', r'@@', r'\bcast\b', r'\bwaitfor\b'
]
logger = logging.getLogger("conversas_logger")


# Função para serializar os dados da requisição
@RoutesTracing(
    span_name_prefix="safe_serialize_request",
    span_description="Serializando os dados da requisição"
)
def safe_serialize_request(request: Request):
    token_auth = request.headers.get("Authorization", None)

    if any(prefix in request.path for prefix in [
        '/register',
        '/unregister',
        '/api/keys',
        '/auth',
        '/health',
    ]):
        if token_auth:
            token_auth = token_auth.removeprefix("Bearer ")
            if not token_auth:
                token_auth = token_auth
        api_key = token_auth  
    elif token_auth:
        token_auth = token_auth.removeprefix("Bearer ")
        api_key = JWT.decode_auth_token(token_auth).get("sub")
    else:
        api_key = None

    try:
        return {
            "origin": request.origin,
            "remote_addr": request.remote_addr,
            "method": request.method,
            "path": request.path,
            "url": request.url,
            "headers": json.dumps(dict(request.headers)),
            "body": json.dumps(request.get_json(silent=True) or {}),  # Tenta pegar JSON, senão retorna {}
            "args": json.dumps(request.args.to_dict()),
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "curl_command": generate_curl_command(
                url=request.url,
                req_body=request.get_json(silent=True) or {},
                headers=dict(request.headers),
                method=request.method,
                params=request.args.to_dict(),
                ),
            "ip": request.headers.get("X-Real-Ip", None),
            'api_key': api_key
        }
    except Exception as e:
        return {
            "error": f"Failed to serialize request: {str(e)}",
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

def log_request():
    if request.path == '/health/':
        return
    request_data = safe_serialize_request(request)
    if request_data.get("error"):
        logger.error(f"Failed to serialize request: {request_data['error']}")
        return
    task = {
        "table": "requests_conversas_ai",
        "data": request_data
        }
    CONNECTIONS.redis_client.rpush('logs', json.dumps(task))

@RoutesTracing(
    span_name_prefix="is_suspicious_input",
    span_description="Verificando se a entrada é suspeita",
    capture_body_fields=["value"]
)
def is_suspicious_input(value):
    if not value:
        return False
    value = str(value).lower()
    for keyword in SQL_KEYWORDS:
        if re.search(keyword, value, re.IGNORECASE):
            return True
    return False

@RoutesTracing(
    span_name_prefix="sql_injection_middleware",
    span_description="Middleware para detecção de SQL Injection",
)
def sql_injection_middleware():
    abort_request = False
    for key, value in request.args.items():
        if "'" in value:
            logger.warning(
                f"Possível tentativa de SQL Injection detectada em query string: {key}={value}")
            abort_request = True
            continue
        if is_suspicious_input(value):
            logger.warning(
                f"Possível tentativa de SQL Injection detectada em query string: {key}={value}")
            abort_request = True 

    for key, value in request.form.items():
        if is_suspicious_input(value):
            logger.warning(
                f"Possível tentativa de SQL Injection detectada em formulário: {key}={value}")
            abort_request = True

    if request.is_json:
        json_data = request.get_json(silent=True)

        def check_json(data):
            nonlocal abort_request
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        check_json(value)
                    elif is_suspicious_input(value):
                        logger.warning(
                            f"Possível tentativa de SQL Injection detectada em JSON: {key}={value}")
                        abort_request = True
            elif isinstance(data, list):
                for item in data:
                    check_json(item)

        check_json(json_data)

    if abort_request:
        request_log = safe_serialize_request(request)
        CONNECTIONS.redis_client.rpush('logs', json.dumps(request_log))
        abort(400, description="Entrada inválida detectada. Verifique os dados enviados.")


def before_request_routine():
    try:
        # sql_injection_middleware()
        log_request()
    except Exception as e:
        logger.error(f"Error in before_request_routine: {str(e)}")
        return make_response(jsonify({"error": f"{e}"}), 200)
