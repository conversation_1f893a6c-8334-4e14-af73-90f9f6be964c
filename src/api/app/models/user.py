import json

from src.connections.connections import Connections

redis_client = Connections.get_instance().redis_client


class User:
    def __init__(self, user_id: int, email: str, name: str = None, profile_pic: str = None):
        self.user_id = user_id
        self.email = email
        self.name = name
        self.profile_pic = profile_pic

    @staticmethod
    def get(user_id: int) -> "User":
        user_data = redis_client.get(f"google-oauth:{user_id}")
        if user_data:
            user_dict = json.loads(user_data)
            return User(
                user_id=user_dict["user_id"],
                email=user_dict["email"],
                name=user_dict["name"],
                profile_pic=user_dict["profile_pic"],
            )
        return None
    
    @staticmethod
    def create(user_id: int, email: str, name: str = None, profile_pic: str = None) -> "User":
        user = User(user_id, email, name, profile_pic)
        redis_client.set(
            f"google-oauth:{user_id}",
            json.dumps(user.__dict__),
            ex=8*60*60
        )
        return user

    @property
    def is_active(self):
        return True

    @property
    def is_authenticated(self):
        return True

    def get_id(self):
        return str(self.user_id)
