import json
import logging
import os

from flask import Blueprint, request, jsonify, current_app
from datetime import datetime

from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import RoutesTracing
from src.api.app.exception.exception import BadRequest

logger = logging.getLogger("conversas_logger")
scheduler_queue = os.environ.get("RQ_QUEUE_NAME", "scheduler_queue")

schedule_contact_bp = Blueprint('schedule_contact', __name__)
notification_scheme_bp = Blueprint('notification_scheme', __name__)
available_events_bp = Blueprint('available_events', __name__)


scheduler_config = {
    "book_class": {
        "has_category": False,
    },
    "phase_message_send": {
        "has_category": True,
    },
}


@schedule_contact_bp.route('/', methods=['POST'])
@RoutesTracing(
    span_name_prefix="schedule_contact",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def schedule_contact():
    """
    Esta rota serve para enviar uma mensagem manualmente.
    ---
    deprecated: true
    tags:
        - Scheduler
    security:
        - Bearer: []
    parameters:
      - name: body
        in: body
        required: true
        schema:
            id: say_hello
            required:
                - seconds
                - kwargs
            properties:
                seconds:
                    type: integer
                    description: Tempo em segundos antes da execução da tarefa
                    example: 10
                kwargs:
                    type: object
                    description: Parâmetros da tarefa a ser agendada
                    properties:
                        msg:
                            type: string
                            description: Mensagem a ser enviada
                            example: "diga olá para o aluno"
                        id_empresa:
                            type: string
                            description: ID da empresa
                            example: "5297f17485f010616fcfd3a15099d7a5-1"
                        instanceId:
                            type: string
                            description: ID da instância conectada
                            example: "3DB4A8AA8CA74006DC791E1C7791D352"
                        connectedPhone:
                            type: string
                            description: Telefone conectado
                            example: "+5562981305490"
                        phone:
                            type: string
                            description: Telefone para o qual a mensagem será enviada
                            example: "5500912341234"
                        isGroup:
                            type: boolean
                            description: Indica se a mensagem será enviada para um grupo
                            example: false
                        messageId:
                            type: string
                            description: ID da mensagem
                            example: "2c784912-ad74-4472-9e13-7bd7e9461b80"
                        sessionId:
                            type: string
                            description: ID da sessão
                            example: null
                        origin:
                            type: string
                            description: Origem da mensagem
                            example: "z_api"

    responses:
        200:
            description: Retorna sucesso indicando que a mensagem foi adicionada à fila do scheduler.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            message:
                                type: string
                                example: Scheduler route is working!
        400:
            description: Erro indicando que os dados fornecidos são inválidos ou incompletos.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            error:
                                type: string
                                example: No data received
    """
    task = request.json
    if not task:
        return jsonify({"error": "No data received"}), 400
    task["kwargs"]["momment"] = int(datetime.now().timestamp()*1000)
    current_app.redis_client.lpush(scheduler_queue, json.dumps(task))

    return jsonify({"message": "Contact scheduled"}), 201


@notification_scheme_bp.route('/', methods=['POST'])
@RoutesTracing(
    span_name_prefix="convert_notification_scheme",
    capture_body_fields=["id_empresa", "notification_type"],
)
def convert_notification_scheme():
    """
    Esta rota serve para criar um esquema de notificações apartir de um texto.
    ---
    deprecated: true
    tags:
        - Scheduler
    security:
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: notification_type
        in: query
        type: string
        enum: [phase_message_send, book_class]
        default: book_class
        required: true
        description: tipo de schema de notificação
      - name: body
        in: body
        required: true
        schema:
            id: notification_scheme
            required:
                - scheduler_text
            properties:
                scheduler_text:
                    type: str
                    description: Texto com estratégia de agendamento de notificações
                    example: Quero que seja enviado um messagem 1 hora depois do agendamento. Depois disso, quero que cada dias intermediário, tenha uma notificação as 8 horas da manhã. No dia da aula expremental, quero uma mensagem 1 hora antes lembrando o usuário e uma hora depois perguntando se ele gostou da aula.
                category:
                    type: str
                    description: "Categoria especifica do esquema de notificação (Ex.: Fase do CRM: POS_VENDA)"
                    example: POS_VENDA

    responses:
        200:
            description: Retorna sucesso indicando que a mensagem foi adicionada à fila do scheduler.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            message:
                                type: string
                                example: Scheduler route is working!
        400:
            description: Erro indicando que os dados fornecidos são inválidos ou incompletos.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            error:
                                type: string
                                example: No data received
    """
    logger.info("Scheduler route called")
    data = request.json
    logger.info(f"Data received: {data}")
    if not data:
        return jsonify({"error": "No data received"}), 400

    empresa = request.args.get("empresa")
    if not empresa:
        raise BadRequest("empresa is required")

    notification_type = request.args.get("notification_type")
    if not notification_type:
        raise BadRequest("notification_type is required")

    notification_key = f"notification_scheme:status:{empresa}:{notification_type}"

    if "category" in data:
        if not scheduler_config.get(notification_type, {}).get("has_category"):
            raise BadRequest(f"{notification_type} does not accept category")
        notification_key += f":{data['category']}"

    current_app.redis_client.set(
        notification_key,
        "pending",
        ex=8*60*60
    )

    current_app.redis_client.lpush("notification_scheme",  json.dumps({
        "id_empresa": request.args["empresa"],
        "notification_type": request.args["notification_type"],
        **data
    }))

    return jsonify({"message": "Send text to process."}), 201


@notification_scheme_bp.route('/', methods=['GET'])
@RoutesTracing(
    span_name_prefix="get_notification_scheme",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def get_notification_scheme():
    """
    Esta rota serve para recuperar do banco de dados um esquema de notificações.
    ---
    deprecated: true
    tags:
        - Scheduler
    security:
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: notification_type
        in: query
        type: string
        enum: [phase_message_send, book_class]
        default: book_class
        required: true
        description: tipo de schema de notificação
      - name: category
        in: query
        type: string
        default: POS_VENDA
        required: false
        description: "Categoria especifica do esquema de notificação (Ex.: Fase do CRM: POS_VENDA)"

    responses:
        200:
            description: Retorna o esquema de notificação para o evento.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            message:
                                type: string
                                example: Scheduler route is working!
        400:
            description: Erro indicando que o esquema de notificação do evento não foi encontrado.
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            error:
                                type: string
                                example: No data received
    """
    id_empresa = request.args.get("empresa", None)
    if not id_empresa:
        raise BadRequest("id_empresa is required")

    notification_type = request.args.get("notification_type", None)
    if not notification_type:
        raise BadRequest("notification_type is required")

    category = request.args.get("category", None)

    status_key = f"notification_scheme:status:{id_empresa}:{notification_type}"
    if category:
        if not scheduler_config.get(notification_type, {}).get("has_category"):
            raise BadRequest(f"{notification_type} does not accept category")
        status_key += f":{category}"

    status = current_app.redis_client.get(status_key)

    if status:
        status = status.decode("utf-8")

    if status == "pending":
        return jsonify({"status": status, "id": 2}), 202
    if status == "processing":
        return jsonify({"status": status, "id": 3}), 202
    elif status == "error":
        return jsonify(
            {"status": status, "id": 4, "message": "Error processing notification scheme"}
        ), 500

    bq_ = bq(id_empresa=id_empresa)
    schema_notification = bq_.get_notification_schema(notification_type, category=category)

    if not schema_notification:
        return jsonify({}), 204

    return jsonify({
        **schema_notification,
        "id": 1
    }), 200


@available_events_bp.route('/', methods=['GET'])
@RoutesTracing(
    span_name_prefix="available_events"
)
def available_events():
    """
    Esta rota retorna uma lista de eventos com notificação disponíveis.
    ---
    deprecated: true
    tags:
        - Scheduler
    responses:
        200:
            description: Retorna uma dos eventos com notificação disponíveis.
            content:
                application/json:
                    schema:
                        type: array
                        items:
                            type: string
                            example: "book_class"
    """
    functions_with_notification_schema = [x for x in os.getenv("NOTIFICATION_EVENTS", "book_class, ").split(",") if x.strip()]
    return jsonify(functions_with_notification_schema), 200
