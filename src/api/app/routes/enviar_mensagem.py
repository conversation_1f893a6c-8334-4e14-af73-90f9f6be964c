import logging
import os
import json
from flask import Blueprint, request, jsonify, current_app

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.connections.connections import Connections
from src.extras.util import RoutesTracing

connections = Connections.get_instance()

logger = logging.getLogger("conversas_logger")

enviar_mensagem_bp = Blueprint('enviar_mensagem', __name__)

@enviar_mensagem_bp.route('', methods=['POST'])
@authentication_required
@RoutesTracing(
    span_name_prefix="enviar_mensagem",
    capture_body_fields=["id_empresa"],
    capture_query_params=["id_empresa"],
)
def enviar_mensagem(id_empresa: str):
    """
    Esta rota serve para enviar uma mensagem para o usuário.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da empresa
      - name: body
        in: body
        required: true
        schema:
          id: enviar_mensagem
          required:
            - fase_crm
            - aluno
          properties:
            fase_crm:
              type: string
              description: A fase de contato em que o usuário se encontra
            aluno:
              type: object
              properties:
                codigo:
                  type: integer
                  description: Código do aluno
                pessoa:
                  type: object
                  properties:
                    nome:
                      type: string
                      description: Nome do usuário para tratamento pessoal
                    telefonesconsulta:
                      type: string
                      description: Telefone do usuário para envio de mensagens
    responses:
      200:
        description: Retorna sucesso
        schema:
          id: success
      400:
        description: Dados não informados
      500:
        description: Erro interno
    """
    try:
        data = request.json

        task = {
            "id_empresa" : id_empresa,
            "data" : data
        }

        if not data:
            return jsonify({"error": "No data received"}), 400

        telefone_consulta = data.get('aluno', {}).get('pessoa', {}).get('telefonesconsulta', '')
        telefone = str(telefone_consulta).split(',', maxsplit=1)[0]
        id_conversa = current_app.redis_client.get(f"current_conversation:{telefone}-{id_empresa}")
        logger.info(f"[ENVIAR_MENSAGEM] Database do redis: {os.getenv('REDIS_URL')}")
        if id_conversa is None:
            logger.info("[ENVIAR_MENSAGEM] Enviando para messages_to_send")
        else:
            logger.info("[ENVIAR_MENSAGEM] Enviando para messages_queued")

        queue = 'messages_to_send' if id_conversa is None else 'messages_queued'
        current_app.redis_client.lpush(queue, json.dumps(task))

        return jsonify({"success": "success"}), 200

    except Exception as e:
        return jsonify({"error": str(e)}), 500
