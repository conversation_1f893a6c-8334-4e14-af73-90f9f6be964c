from flask import Blueprint, request, jsonify, g
import logging
import os
import requests
from functools import wraps
import base64
from datetime import datetime
import re

from src.data.google_storage import Bucket
from src.extras.util import register_log, token_guard, RoutesTracing
from src.data.bigquery_data import get_from_instance, get_from_empresa
from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.integrations.z_api.tools.integration_tools import ZApiIntegrationTools

logger = logging.getLogger("conversas_logger")

get_qr_code_bp = Blueprint('get_qr_code', __name__)
disconnect_instance_bp = Blueprint('disconnect_instance', __name__)
check_instance_status_bp = Blueprint('check_instance_status', __name__)
create_instance_bp = Blueprint('create_instance', __name__)
subscribe_instance_bp = Blueprint('subscribe_instance', __name__)
cancel_instance_bp = Blueprint('cancel_instance', __name__)
get_instance_bp = Blueprint('get_instance', __name__)
profile_bp = Blueprint('profile', __name__)
device_bp = Blueprint('device', __name__)
get_token_from_instance_bp = Blueprint('get_token_from_instance', __name__)
get_tag_colors_bp = Blueprint('get_tag_colors', __name__)

CLIENT_TOKEN = os.getenv('Z_API_CLIENT_TOKEN')
INTEGRATOR_TOKEN = os.getenv('Z_API_INTEGRATOR_TOKEN')

def instance_data_required(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        instance_id = request.args.get('instance_id')   
        token = request.args.get('token')
        is_firebase = getattr(g, 'is_firebase', False)

        if is_firebase:
            id_empresa = getattr(g, 'user_id', None)
            if request.path == '/instance':
                return func(id_empresa, *args, **kwargs)

            instance_id, token = get_from_empresa(id_empresa)

        if request.path == '/instance' or request.path == '/get_token_from_instance':
            return func(*args, **kwargs)

        if not instance_id:
            return jsonify({'error': 'instance_id is required'}), 400
        if not token:
            return jsonify({'error': 'token is required'}), 400
        
        id_empresa, _ = get_from_instance(instance_id)
        base_url = f'https://api.z-api.io/instances/{instance_id}/token/{token}'
        return func(base_url, id_empresa)
    return wrapper

@get_qr_code_bp.route('', methods=['GET'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="get_qr_code",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def get_qr_code(base_url, id_empresa):
    """
    Esta rota serve para buscar o QR Code da instância.
    ---
    tags:
        - Whatsapp
    security:
        - Bearer: []
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna o QR Code em base64
            schema:
                id: qr_code
                type: object
                properties:
                    value:
                        type: string
                        description: QR Code
                        example: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAARQAAAEUCAYAAADqcMl5AAAAAklEQVR4AewaftIAABJTSURBVO3BQW4AR5LAQLKh/3+Z62OeCmh0SfYOMsL+wVprXfCw1lqXPKy11iUPa611ycNaa13ysNZalzyst
        400:
            description: Dados não informados
            schema:
                id: error
    """
    url = f'{base_url}/qr-code/image'

    response = requests.get(url, headers={'client-token': CLIENT_TOKEN})

    register_log(url, None, {'client-token': CLIENT_TOKEN}, 'GET', response, 'get_qr_code', id_empresa, None, table="requests_zapi")
    
    return response.content, response.status_code

@disconnect_instance_bp.route('', methods=['POST'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="disconnect_instance",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def disconnect_instance(base_url, id_empresa):
    """
    Esta rota serve para desconectar a instância.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna sucesso
            schema:
                type: object
                properties:
                    value:
                        type: Boolean
                        description: Desconectado
                        example: true
        400:
            description: Dados não informados
            schema:
                id: error
    """
    url = f'{base_url}/disconnect'

    response = requests.get(url, headers={'client-token': CLIENT_TOKEN})

    register_log(url, None, {'client-token': CLIENT_TOKEN}, 'GET', response, 'disconnect_instance', id_empresa, None, "requests_zapi")

    return response.content, response.status_code

@check_instance_status_bp.route('', methods=['GET'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="check_instance_status",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def check_instance_status(base_url, id_empresa):
    """
    Esta rota serve para checar o status da instância.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna o status da instância
            schema:
              type: object
              properties:
                status:
                  type: string
                  description: "Indica se a instância está conectada ou desconectada"
                  enum:
                    - connected
                    - disconnected
                  example: "connected"
                data:
                  type: object
                  description: "Detalhes da instância"
                  properties:
                    phone:
                        type: string
                        description: Número do telefone
                        example: 556281305490
                    imgUrl:
                        type: string
                        description: URL da imagem
                        example: https://pps.whatsapp.net/v/t61.24694-24/425272972_532416369464106_5989681207002826431_n.jpg
                    name:
                        type: string
                        description: Nome
                        example: Jennifer
                    device:
                        type: object
                        description: Dispositivo
                        properties:
                            sessionName:
                                type: string
                                description: Nome da sessão
                                example: Z-API
                            device_model:
                                type: string
                                description: Modelo do dispositivo
                                example: Z-API
                    originalDevice:
                        type: string
                        description: Dispositivo original
                        example: smba
                    sessionId:
                        type: integer
                        description: ID da sessão
                        example: 178
                    isBusiness:
                        type: boolean
                        description: É um Whatsapp Business?
                        example: true
        400:
            description: Dados não informados
            schema:
                type: object
                properties:
                    error:
                        type: string
                        description: Mensagem de erro
                        example: Instance not found
    """
    url = f'{base_url}/status'

    response = requests.get(url, headers={'client-token': CLIENT_TOKEN})

    register_log(url, None, {'client-token': CLIENT_TOKEN}, 'GET', response, 'check_instance_status', id_empresa, None, "requests_zapi")

    return response.content, response.status_code

@create_instance_bp.route('', methods=['POST'])
@authentication_required
@RoutesTracing(
    span_name_prefix="create_instance",
    capture_body_fields=[
        "name",
        "_id_empresa",
        "receivedCallbackUrl",
        "messageStatusCallbackUrl",
        "isDevice",
        "businessDevice",
    ],
)
def create_instance(_id_empresa: str):
    """
    Esta rota serve para criar uma instância.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: body
        in: body
        required: false
        schema:
          id: create_instance
          required:
            - name
          properties:
            name:
              type: string
              description: Nome da instância
            receivedCallbackUrl:
              type: string
              description: Webhook para receber mensagens
              default: "https://orion.pactosolucoes.com.br/receber_mensagem/"
            isDevice:
              type: boolean
              description: É um dispositivo móvel? (Alternativa seria um chip virtual)
            businessDevice:
              type: boolean
              description: É um Whatsapp Business?
    responses:
        200:
            description: Retorna o status da instância
        400:
            description: Dados não informados
            schema:
                id: error
    """

    if not INTEGRATOR_TOKEN:
        logger.error('Cannot proceed because the server is not INTEGRATOR_TOKEN environment variable')
        return jsonify({'error': 'Cannot proceed because the server is not communicating with z-api'}), 400

    data = request.get_json()
    name = data.get('name', None)
    if not name:
        return jsonify({'error': 'name is required'}), 400
    
    url = 'https://api.z-api.io/instances/integrator/on-demand'
    
    body = {
        "name": f"Pacto Conversas - {name}",
        "receivedCallbackUrl": f"{os.getenv('DOMAIN', 'https://orion.pactosolucoes.com.br/')}/receber_mensagem/",
        "messageStatusCallbackUrl": f"{os.getenv('DOMAIN', 'https://orion.pactosolucoes.com.br/')}/status_mensagem/",
        "isDevice": data.get('isDevice', False),
        "businessDevice": data.get('businessDevice', True)
    }

    response = requests.post(url, headers={'Authorization': "Bearer " + INTEGRATOR_TOKEN, 'Content-Type': 'application/json'}, json=body)

    register_log(url, body, {'Authorization': "Bearer " + token_guard(INTEGRATOR_TOKEN), 'Content-Type': 'application/json'}, 'POST', response, 'create_instance', 'conversas-ai-core', None, "requests_zapi")

    return response.content, response.status_code

@subscribe_instance_bp.route('', methods=['POST'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="subscribe_instance",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def subscribe_instance(base_url, id_empresa):
    """
    Esta rota serve para inscrever a instância, incluindo ela nas cobranças.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: Dados não informados
            schema:
                id: error
    """
    url = f"{base_url}/integrator/on-demand/subscription"

    response = requests.post(url, headers={'Authorization': "Bearer " + INTEGRATOR_TOKEN})

    register_log(url, None, {'Authorization': "Bearer " + token_guard(INTEGRATOR_TOKEN)}, 'POST', response, 'subscribe_instance', id_empresa, None, "requests_zapi")

    return response.content, response.status_code

@cancel_instance_bp.route('', methods=['POST'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="cancel_instance",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def cancel_instance(base_url, id_empresa):
    """
    Esta rota serve para cancelar a instância.
    ---
    tags:
        - Whatsapp
    security: 
        - Bearer: []
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: success
        400:
            description: Dados não informados
            schema:
                id: error
    """
    url = f"{base_url}/integrator/on-demand/cancel"

    response = requests.post(url, headers={'Authorization': "Bearer " + INTEGRATOR_TOKEN})

    register_log(url, None, {'Authorization': "Bearer " + token_guard(INTEGRATOR_TOKEN)}, 'POST', response, 'cancel_instance', id_empresa, None, "requests_zapi")

    return response.content, response.status_code

@get_instance_bp.route('', methods=['GET'])
@authentication_required
@instance_data_required
@RoutesTracing(
    span_name_prefix="get_instance",
    capture_body_fields=["id_empresa", "instance_id"],
)
def get_instance(id_empresa=None):
    """
    Esta rota serve para obter informações de uma instância específica.
    ---
    tags:
        - Whatsapp
    parameters:
      - name: id_empresa
        in: query
        type: string
        default: empresa-1
        required: false
        description: ID da empresa relacionada à instância
    responses:
        200:
            description: Retorna informações da instância
            schema:
                id: dados_z_api
        400:
            description: Dados não informados
            schema:
                id: error
    """
    if not id_empresa:
        id_empresa = request.args.get('id_empresa')
    
    instance_id, token = get_from_empresa(id_empresa)

    if not instance_id or not token:
        return jsonify({'error': 'Instance not found'}), 400
    
    response = {
        "instance_id": instance_id,
        "token": token
    }

    return jsonify(response), 200

@profile_bp.route('', methods=['POST'])
@instance_data_required
@RoutesTracing(
    span_name_prefix="profile",
    capture_body_fields=["base_url", "id_empresa"],
)
def profile(base_url, id_empresa):
    """
    Esta rota serve para atualizar dados do perfil da instância: foto, nome e/ou descrição.
    ---
    tags:
        - Whatsapp
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
      - name: body
        in: body
        required: false
        schema:
          id: profile
          properties:
            img:
              type: string
              description: URL da imagem
              example: data:image/jpeg;base64,/iVBORw0KGgoAAAANSUhEUgAAARQAAAEUCAYAAADqcMl5AAAAAklEQVR4AewaftIAABJTSURBVO3BQW4AR5LAQLKh/3+Z62OeCmh0SfYOMsL+wVprXfCw1lqXPKy11iUPa611ycNaa13ysNZalzyst
            name:
              type: string
              description: Nome
              example: Jennifer
            description:
              type: string
              description: Descrição
              example: "Oi, eu sou a Jennifer!"
    responses:
        200:
            description: Retorna sucesso
            schema:
                id: profile_updated
                properties:
                    picture:
                        type: object
                        properties:
                            value:
                                type: boolean
                                description: Atualizado
                                example: true
                    name:
                        type: object
                        properties:
                            value:
                                type: boolean
                                description: Atualizado
                                example: true
                    description:
                        type: object
                        properties:
                            value:
                                type: boolean
                                description: Atualizado
                                example: true
        400:
            description: Dados não informados
            schema:
                id: error
    """
    response_body = {
        "img": None,
        "name": None,
        "description": None
    }

    endpoints = {
        "name": f"{base_url}/profile-name",
        "description": f"{base_url}/profile-description",
        "img": f"{base_url}/profile-picture"
    }

    data = request.get_json()

    if not data or not any(value for value in data.values()):
        return jsonify({'error': 'No data to update'}), 400

    for key, url in endpoints.items():
        if key in data:
            if key == 'img':
                bucket = Bucket('z-api-profile-pictures')
                file_name = f"profile_pic_{datetime.now().strftime('%Y%m%d%H%M%S')}.jpeg"
                image = data[key]
                regex = r"(?<=data:)(.*)(?=;)"
                split = image.split('base64')
                format_image = re.findall(regex, split[0])[0]
                base64_image = base64.b64decode(split[1])
                value = bucket.upload(base64_image, file_name, True, True, format_image)
                response = requests.put(url, headers={'client-token': CLIENT_TOKEN}, json={"value": value})
                response_body[key] = response.json()
                bucket.delete(file_name)
                continue
            else: 
                value = data[key]
            response = requests.put(url, headers={'client-token': CLIENT_TOKEN}, json={"value": value})
            response_body[key] = response.json()
    
    register_log(endpoints, data, {'client-token': CLIENT_TOKEN}, 'PUT', response_body, 'profile', id_empresa, None, "requests_zapi")

    return jsonify(response_body), 200


@device_bp.route('', methods=['GET'])
@instance_data_required
@RoutesTracing(
    span_name_prefix="get_info_device",
    capture_body_fields=["base_url", "id_empresa"],
)
def device(base_url, id_empresa):
    """
    Esta rota serve para obter informações do dispositivo da instância.
    ---
    tags:
        - Whatsapp
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância
      - name: token
        in: query
        type: string
        default: 1234567890
        required: false
        description: Token da instância
    responses:
        200:
            description: Retorna informações do dispositivo
            schema:
                type: object
                properties:
                    phone:
                        type: string
                        description: Número do telefone
                        example: 556281305490
                    imgUrl:
                        type: string
                        description: URL da imagem
                        example: https://pps.whatsapp.net/v/t61.24694-24/425272972_532416369464106_5989681207002826431_n.jpg
                    name:
                        type: string
                        description: Nome
                        example: Jennifer
                    device:
                        type: object
                        description: Dispositivo
                        properties:
                            sessionName:
                                type: string
                                description: Nome da sessão
                                example: Z-API
                            device_model:
                                type: string
                                description: Modelo do dispositivo
                                example: Z-API
                    originalDevice:
                        type: string
                        description: Dispositivo original
                        example: smba
                    sessionId:
                        type: integer
                        description: ID da sessão
                        example: 178
                    isBusiness:
                        type: boolean
                        description: É um Whatsapp Business?
                        example: true
        400:
            description: Dados não informados
            schema:
                type: object
                properties:
                    error:
                        type: string
                        description: Mensagem de erro
                        example: Instance not found
    """
    url = f'{base_url}/device'

    response = requests.get(url, headers={'client-token': CLIENT_TOKEN})

    register_log(url, None, {'client-token': CLIENT_TOKEN}, 'GET', response, 'device', id_empresa, None, "requests_zapi")

    return response.content, response.status_code


@get_token_from_instance_bp.route('', methods=['GET'])
@instance_data_required
@RoutesTracing(
    span_name_prefix="get_token_from_instance",
    capture_body_fields=["instance_id", "base_url", "id_empresa"],
)
def get_token_from_instance(*args, **kwargs):
    """
    Esta rota serve pegar o token a partir da instância.
    ---
    tags:
      - Whatsapp
    parameters:
      - name: instance_id
        in: query
        type: string
        default: 1234567890
        required: false
        description: ID da instância

    responses:
        200:
            description: Retorna o token da instância
            schema:
                type: object
                properties:
                    token:
                        type: string
                        description: Token da instancia
        400:
            description: Dados não informados
            schema:
                id: error
    """

    if not INTEGRATOR_TOKEN:
        logger.error('Cannot proceed because the server is not INTEGRATOR_TOKEN environment variable')
        return jsonify({'error': 'Cannot proceed because the server is not communicating with z-api'}), 400

    instance_id = request.args.get('instance_id', None)
    if not instance_id:
        return jsonify({'error': 'instance_id is required'}), 400
    
    url = 'https://api.z-api.io/instances/'
    
    params = {
        "pageSize": "9999", 
        "page": "1", 
        "query": instance_id, 
        "middleware": "web"
    }
    headers = {
         'Authorization': "Bearer " + INTEGRATOR_TOKEN, 
         'Content-Type': 'application/json'
    }

    response = requests.get(url, headers=headers, params=params)
    register_log(url, None, {'Authorization': "Bearer " + token_guard(INTEGRATOR_TOKEN), 'Content-Type': 'application/json'}, 'GET', response, 'get_token_from_instance', 'conversas-ai-core', params, "requests_zapi")
    
    response_json = response.json()
    if response.status_code == 200 and len(response_json["content"]) > 0 and "token" in response_json["content"][0]:
        return jsonify({'token': response_json["content"][0]["token"]}), response.status_code

    return jsonify({'error': 'Instance not found'}), 400


@get_tag_colors_bp.route('', methods=['GET'])
@authentication_required
@RoutesTracing(
    span_name_prefix="get_tag_colors",
    capture_body_fields=["id_empresa"],
)
def get_tag_colors():
    """
    Esta rota serve para obter as cores disponíveis para tags na instância da empresa.
    ---
    tags:
        - Whatsapp
    security:
        - Bearer: []
    parameters:
      - name: id_empresa
        in: query
        type: string
        required: true
        description: ID da empresa
        example: empresa-1
    responses:
        200:
            description: Retorna as cores disponíveis para tags
            schema:
                type: object
                properties:
                    colors:
                        type: array
                        description: Lista de cores disponíveis
                        items:
                            type: object
                            properties:
                                id:
                                    type: integer
                                    description: ID da cor
                                    example: 1
                                name:
                                    type: string
                                    description: Nome da cor
                                    example: "Azul"
                                hex:
                                    type: string
                                    description: Código hexadecimal da cor
                                    example: "#0084FF"
        400:
            description: Dados não informados
            schema:
                type: object
                properties:
                    error:
                        type: string
                        description: Mensagem de erro
                        example: "id_empresa is required"
        500:
            description: Erro interno do servidor
            schema:
                type: object
                properties:
                    error:
                        type: string
                        description: Mensagem de erro
                        example: "Internal server error"
    """
    id_empresa = request.args.get('id_empresa')

    if not id_empresa:
        return jsonify({'error': 'id_empresa is required'}), 400

    try:
        z_api_tools = ZApiIntegrationTools(id_empresa=id_empresa)
        tag_colors = z_api_tools.get_tag_colors()

        return jsonify(tag_colors), 200
    except requests.RequestException as e:
        logger.error("Error getting tag colors for empresa %s: %s", id_empresa, str(e))
        return jsonify({'error': 'Error communicating with Z-API'}), 500
    except ValueError as e:
        logger.error("Invalid data for empresa %s: %s", id_empresa, str(e))
        return jsonify({'error': 'Invalid empresa data'}), 400
