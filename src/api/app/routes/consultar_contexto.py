from flask import Blueprint, request, jsonify

from src.data.bigquery_data import BigQueryData as bq
from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.api.app.exception.exception import BadRequest
from src.extras.util import RoutesTracing

consultar_contexto_academia_bp = Blueprint('consultar_contexto_academia', __name__)
consultar_contexto_planos_bp = Blueprint('consultar_contexto_planos', __name__)
consultar_contexto_fases_bp = Blueprint('consultar_contexto_fases', __name__)
consultar_contexto_turmas_bp = Blueprint('consultar_contexto_turmas', __name__)
consultar_contexto_produtos_bp = Blueprint('consultar_contexto_produtos', __name__)
consultar_contexto_personalidade_bp = Blueprint('consultar_contexto_personalidade', __name__)
consultar_contexto_aluno_bp = Blueprint('consultar_contexto_aluno', __name__)
consultar_contexto_campanhas_bp = Blueprint('consultar_contexto_campanhas', __name__)


#TODO: Add try/except to all json searches
@consultar_contexto_academia_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_academia",
    capture_body_fields=["empresa"],
    capture_query_params=["empresa"],
)
def consultar_contexto_academia(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto da academia, esses dados serão usados 
    para guiar a IA sobre informações específicas da empresa.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna o contexto da academia
            schema:
                id: contexto_academia
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    bq_ = bq(id_empresa=id_empresa)
    contexto = bq_.get_gym_context()

    return jsonify(contexto), 200

@consultar_contexto_planos_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_planos",
    capture_body_fields=["empresa"],
    capture_query_params=["empresa"],
)
def consultar_contexto_planos(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto dos planos da academia, esses dados servem 
    para que a IA saiba quais são os planos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna o contexto dos planos da academia
            schema:
                id: contexto_planos
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_plans_context()

    return jsonify(contexto), 200


@consultar_contexto_fases_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_fases",
    capture_body_fields=["empresa", "fase"],
    capture_query_params=["empresa"],
)
def consultar_contexto_fases(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto das fases da academia, esses dados servem 
    para que a IA saiba como falar com o usuário em cada fase.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: fase
        in: query
        type: string
        default: INICIAL
        required: false
        description: Nome da fase
    responses:
        200:
            description: Retorna o contexto das fases da academia
            schema:
                id: contexto_fases
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    phase = request.args.get('fase', None)
    if phase is None:
        get_all = True
    else:
        get_all = False

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_phase_context(phase_name=phase, get_all=get_all)

    return jsonify(contexto), 200


@consultar_contexto_turmas_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_turmas",
    capture_body_fields=["empresa"],
    capture_query_params=["empresa"],
)
def consultar_contexto_turmas(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto das turmas da academia, esses dados servem 
    para que a IA saiba quais são as turmas disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna o contexto das turmas da academia
            schema:
                id: contexto_turmas
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_classes_context()

    return jsonify(contexto), 200

@consultar_contexto_produtos_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_produtos",
    capture_body_fields=["empresa"],
    capture_query_params=["empresa"],
)
def consultar_contexto_produtos(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto dos produtos da academia, esses dados servem 
    para que a IA saiba quais são os produtos disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna o contexto dos produtos da academia
            schema:
                id: contexto_produtos
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_products_context()

    return jsonify(contexto), 200


@consultar_contexto_personalidade_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_personalidade",
    capture_body_fields=["empresa"],
    capture_query_params=["empresa"],
)
def consultar_contexto_personalidade(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto da personalidade da academia, esse dado serve 
    para que a IA saiba como falar com o usuário.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
        200:
            description: Retorna o contexto da personalidade da academia
            schema:
                id: contexto_personalidade
        400:
            description: ID da academia não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_personality_context()

    return jsonify(contexto), 200

@consultar_contexto_aluno_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_aluno",
    capture_body_fields=["empresa", "telefone"],
    capture_query_params=["empresa", "telefone"],
)
def consultar_contexto_aluno(id_empresa: str = None):
    """
    Esta rota serve para buscar o contexto dos alunos da academia, esse dado serve 
    para que a IA saiba quem é o usuário e tenha seu número de telefone.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:    
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: telefone
        in: query
        type: string
        default: '+5562988887777'
        required: true
        description: Telefone do aluno
    responses:
        200:
            description: Retorna o contexto dos alunos da academia
            schema:
                id: enviar_mensagem
        400:
            description: ID da academia ou telefone não informado
            schema:
                id: error
    """
    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")

    telefone = request.args.get('telefone', None)

    if not telefone:
        raise BadRequest("Telefone não informado")

    bq_ = bq(id_empresa=id_empresa)

    contexto = bq_.get_user_context(telefone=telefone)

    return jsonify(contexto), 200

@consultar_contexto_campanhas_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="consultar_contexto_campanhas",
    capture_body_fields=["empresa", "id_campanha"],
    capture_query_params=["empresa", "id_campanha"],
)
def consultar_contexto_campanhas(id_empresa: str = None, nome_campanha: str = None):
    """
    Esta rota serve para buscar o contexto das campanhas da academia, esses dados servem 
    para que a IA saiba quais são as campanhas disponíveis.
    ---
    tags:
        - Contexto
    security: 
        - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: id_campanha
        in: query
        type: string
        default:
        example: dd9ef247-8500-4c56-9be7-c38f24c7a853a
        required: false
        description: id da campanha, se não informado, retorna todas as campanhas da empresa
    responses:
        200:
            description: Retorna o contexto da academia
            schema:
                type: array
                items:
                    type: object
                    properties:
                        nome:
                            type: string
                            description: Nome da campanha
                            example: dia das mães
                        instrucao:
                            type: string
                            description: instrução da campanha
                            example: Mande uma linda mensagem de dia das mães para o aluno.
                        keyword:
                            type: string
                            description: gatilho para campanha (hashtag)
                            example: diadasmaes
                        data_inicio:
                            type: string
                            description: data de início da campanha no formato dd/mm/yyyy hh:mm:ss
                            example: 01/01/2025 00:00:00
                        data_fim:
                            type: string
                            description: data de fim da campanha no formato dd/mm/yyyy hh:mm:ss
                            example: 31/01/2025 00:00:00
                        imagem:
                            type: string
                            description: url da imagem da campanha
                            example: https://storage.googleapis.com/conversas-ai/empresa-1/ca74ec27-6280-4900-bebd-789f46cead98
                        is_template:
                            type: bool
                            description: Diz se o campo instrução é um template fixo ou é usado pela IA
                            example: false
                        id_campanha:
                            type: string
                            description: id da campanha
                            example: dd9ef247-8500-4c56-9be7-c38f24c7a853a
                        id_empresa:
                            type: string
                            description: id da empresa
                            example: empresa-1
                        whatsapp_link:
                            type: string
                            description: link do whatsapp da campanha
                            example: https://wa.me/5562982374212?text=%23diadasmaes
        400:
            description: ID da academia não informado
            schema:
                id: error
    """

    id_empresa = request.args.get('empresa')

    if not id_empresa:
        raise BadRequest("ID da academia não informado")
    
    id_campanha = request.args.get('id_campanha')

    bq_ = bq(id_empresa=id_empresa)
    contexto = bq_.get_campaigns_context(id_campanha)

    if not contexto:
        return jsonify([]), 200

    return jsonify(contexto), 200
