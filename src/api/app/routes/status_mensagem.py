import json
import logging
from flask import Blueprint, request, jsonify, current_app

from src.api.app.auth.utils.auth_wrappers import verify_origin
from src.data.bigquery_data import get_from_instance
from src.extras.util import RoutesTracing

logger = logging.getLogger("conversas_logger")

status_mensagem_bp = Blueprint('status_mensagem', __name__)

@status_mensagem_bp.route('/', methods=['POST'])
@verify_origin()
@RoutesTracing(
    span_name_prefix="status_mensagem",
    capture_body_fields=["id_empresa" ,"data"],
)
def status_mensagem(origin="Z-API"):
    try:
        data = request.json
        id_empresa = data.get('chave_empresa', None)

        if not data:
            return jsonify({"error": "No data received"}), 400
        
        if data.get("type") != 'MessageStatusCallback':
            return jsonify({"success": "success"}), 200

        id_empresa, _ = get_from_instance(data["instanceId"])

        if not data.get('id', None):
            if data.get('ids', None):
                for id in data['ids']:
                    data_to_send = data.copy()
                    data_to_send['id'] = id
                    task = {
                        "provedor": "z_api",
                        "id_empresa" : id_empresa,
                        "data" : data_to_send
                    }
                    current_app.redis_client.lpush('save_status', json.dumps(task))
            return jsonify({
                "success": "success",
            }), 200

        task = {
            "provedor": "z_api",
            "id_empresa" : id_empresa,
            "data" : data
        }

        current_app.redis_client.lpush('save_status', json.dumps(task))
        
        return jsonify({
            "success": "success",
        }), 200

    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 500
