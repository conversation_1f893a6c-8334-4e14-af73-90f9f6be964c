"""Módulo para gerenciamento de departamentos via API v2."""
import json
import re
from flask import Blueprint, request, current_app

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import RoutesTracing
from src.integrations.z_api.tools.integration_tools import ZApiIntegrationTools

departments_bp = Blueprint('departments', __name__)

@departments_bp.route('/sync', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="sync_departments",
    span_description="Sincronizar departamentos com o Whatsapp Business",
    capture_body_fields=["empresa"]
)
def sync_departments(id_empresa: str):
    """
    Sincronizar departamentos, buscando no Whatsapp Web e registrando no banco de dados.
    ---
    tags:
      - Departments
    parameters:
      - name: empresa
        in: query
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - name: integration
        in: query
        type: string
        required: false
        description: Tipo de integração (z_api)
        example: "z_api"
    responses:
      200:
        description: Lista de departamentos sincronizados
        schema:
          type: array
          items:
            $ref: '#/definitions/Department'
      404:
        description: Nenhum departamento encontrado
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Nenhum departamento encontrado"
    """
    integration = request.args.get('integration')

    if not id_empresa:
        return {"error": "Erro ao criar departamento"}, 400

    # Se integration=z_api, criar tag no Z-API primeiro
    if integration == "z_api":
        try:
            z_api_tools = ZApiIntegrationTools(id_empresa=id_empresa)

            tags = z_api_tools.get_tags()

            data = []
            for tag in tags:
                data.append({
                    "id": tag.get("id"),
                    "name": tag.get("name"),
                    "description": None,
                    "color": tag.get("colorHex"),
                })
            task = {
                "type": "api-v2",
                "id_empresa": id_empresa,
                "data": data,
                "action": "sync",
                "resource": "department"
            }

            current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

        except (ValueError, KeyError, ConnectionError) as e:
            return {"error": f"Erro na integração Z-API: {str(e)}"}, 500

    return {
        "success": "success",
        "id_empresa": id_empresa
    }, 201


@departments_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_departments",
    span_description="Obter departamentos da empresa",
    capture_body_fields=["empresa"]
)
def get_departments(id_empresa: str):
    """
    Obter departamentos da empresa
    ---
    definitions:
      Department:
        type: object
        required:
          - id
          - name
          - color
        properties:
          id:
            type: string
            description: ID único do departamento (UUID).
            example: "729e9f85-3f3e-472b-b92d-d720f4babc37"
          name:
            type: string
            description: Nome do departamento.
            example: "Financeiro"
          color:
            type: string
            description: Cor do departamento em formato hexadecimal.
            example: "#FF5733"
          description:
            type: string
            description: Descrição do departamento.
            example: "Departamento responsável pelas finanças"
          data_criacao:
            type: string
            format: date-time
            description: Data de criação do departamento.
            example: "2024-01-15T10:30:00Z"
          data_ultima_atualizacao:
            type: string
            format: date-time
            description: Data da última atualização do departamento.
            example: "2024-01-15T10:30:00Z"
    tags:
      - Departments
    parameters:
      - name: empresa
        in: query
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - name: integration
        in: query
        type: string
        required: false
        description: Tipo de integração (z_api)
        example: "z_api"
    responses:
      200:
        description: Lista de departamentos encontrados
        schema:
          type: array
          items:
            $ref: '#/definitions/Department'
      404:
        description: Nenhum departamento encontrado
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Nenhum departamento encontrado"
    """
    bq_ = bq(id_empresa=id_empresa)
    departments = bq_.get_departments()

    if not departments:
        return {"error": "Nenhum departamento encontrado"}, 404

    return departments, 200

@departments_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="create_department",
    span_description="Criar um novo departamento",
    capture_body_fields=["empresa"]
)
def create_department(id_empresa: str):
    """
    Criar um novo departamento
    ---
    tags:
      - Departments
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - in: query
        name: integration
        type: string
        required: false
        description: Tipo de integração (z_api)
        example: "z_api"
      - in: body
        name: body
        required: true
        description: Dados do departamento a ser criado.
        schema:
          type: object
          required:
            - name
            - color
          properties:
            name:
              type: string
              description: Nome do departamento
              example: "Financeiro"
            color:
              description: ID da cor do departamento
              example: 1
            description:
              type: string
              description: Descrição do departamento (opcional)
              example: "Departamento responsável pelas finanças"
    responses:
      201:
        description: Departamento criado com sucesso
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
      400:
        description: Dados inválidos
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Dados inválidos: name"
    """
    required = ["name", "color"]
    data = request.json
    integration = request.args.get('integration')

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    if not id_empresa:
        return {"error": "Erro ao criar departamento"}, 400

    # Se integration=z_api, criar tag no Z-API primeiro
    if integration == "z_api":
        try:
            z_api_tools = ZApiIntegrationTools(id_empresa=id_empresa)

            # Criar tag no Z-API
            tag_response = z_api_tools.create_tag(
                tag_name=data.get("name"),
                tag_color=data.get("color")
            )

            if not tag_response or "id" not in tag_response:
                return {"error": "Falha ao criar tag no Z-API"}, 500

            # Usar o ID da tag como ID do departamento
            data["id"] = tag_response["id"]

        except (ValueError, KeyError, ConnectionError) as e:
            return {"error": f"Erro na integração Z-API: {str(e)}"}, 500

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "create",
        "resource": "department"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
        "success": "success",
        "id_empresa": id_empresa
    }, 201

@departments_bp.route('/<department_id>', methods=['PUT'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="update_department",
    span_description="Atualizar um departamento existente",
    capture_body_fields=["empresa", "department_id"]
)
def update_department(id_empresa: str, department_id: str):
    """
    Atualizar um departamento existente (atualização completa)
    ---
    tags:
      - Departments
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: id_empresa
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - in: path
        name: department_id
        type: string
        required: true
        description: ID do departamento
        example: "729e9f85-3f3e-472b-b92d-d720f4babc37"
      - in: query
        name: integration
        type: string
        required: false
        description: Tipo de integração (z_api)
        example: "z_api"
      - in: body
        name: body
        required: true
        description: Dados completos do departamento a serem atualizados.
        schema:
          type: object
          required:
            - name
            - color
          properties:
            name:
              type: string
              description: Nome do departamento
              example: "Financeiro Atualizado"
            color:
              type: number
              description: ID da cor do departamento
              example: 1
    responses:
      200:
        description: Departamento atualizado com sucesso
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
      400:
        description: Dados inválidos
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Dados inválidos: name"
    """
    required = ["name", "color"]
    data = request.json
    integration = request.args.get('integration', 'z_api')

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400


    # Se integration=z_api, criar tag no Z-API primeiro
    if integration == "z_api":
        try:
            z_api_tools = ZApiIntegrationTools(id_empresa=id_empresa)

            # Criar tag no Z-API
            tag_response = z_api_tools.edit_tag(
                tag_id=department_id,
                color=data.get("color"),
                name=data.get("name"),
            )

            if not tag_response or not tag_response.get("success"):
                return {"error": "Falha ao editar tag no Z-API"}, 500

        except (ValueError, KeyError, ConnectionError) as e:
            return {"error": f"Erro na integração Z-API: {str(e)}"}, 500

    data["id"] = department_id

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "update",
        "resource": "department"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
        "success": "success",
        "id_empresa": id_empresa
    }, 200

@departments_bp.route('/<department_id>', methods=['PATCH'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="patch_department",
    span_description="Atualizar parcialmente um departamento",
    capture_body_fields=["empresa", "department_id"]
)
def patch_department(id_empresa: str, department_id: str):
    """
    Atualizar parcialmente um departamento existente
    ---
    tags:
      - Departments
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - in: path
        name: department_id
        type: string
        required: true
        description: ID do departamento
        example: "729e9f85-3f3e-472b-b92d-d720f4babc37"
      - in: body
        name: body
        required: true
        description: Dados parciais do departamento a serem atualizados.
        schema:
          type: object
          properties:
            name:
              type: string
              description: Nome do departamento
              example: "Financeiro Patch"
            color:
              type: number
              description: ID da cor do departamento
              example: 1
    responses:
      200:
        description: Departamento atualizado com sucesso
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
      400:
        description: Dados inválidos
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Cor inválida"
    """
    data = request.json

    if not data:
        return {"error": "Nenhum dado fornecido para atualização"}, 400

    data["id"] = department_id

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "patch",
        "resource": "department"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
        "success": "success",
        "id_empresa": id_empresa
    }, 200

@departments_bp.route('/<department_id>', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="delete_department",
    span_description="Deletar um departamento existente",
    capture_body_fields=["empresa", "department_id"]
)
def delete_department(id_empresa: str, department_id: str):
    """
    Deletar um departamento existente
    ---
    tags:
      - Departments
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: empresa
        type: string
        required: true
        description: ID da empresa
        example: "empresa-1"
      - in: path
        name: department_id
        type: string
        required: true
        description: ID do departamento
        example: "729e9f85-3f3e-472b-b92d-d720f4babc37"
    responses:
      200:
        description: Departamento deletado com sucesso
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
    """
    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": {"id": department_id},
        "action": "delete",
        "resource": "department"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
        "success": "success",
        "id_empresa": id_empresa
    }, 200
