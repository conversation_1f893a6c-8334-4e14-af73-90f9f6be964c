import json
from flask import Blueprint, request, current_app
from werkzeug.exceptions import NotFound

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.data.bigquery_data import BigQueryData as bq
from src.api.app.exception.exception import BadRequest
from src.extras.util import RoutesTracing

empresa_bp = Blueprint('empresa', __name__)

@empresa_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_empresa",
    span_description="Obter uma empresa pelo ID",
    capture_body_fields=["id_empresa"],
)
def get_empresa(id_empresa: str):
    """
    Obter uma empresa pelo ID
    ---
    definitions:
      Empresa:
        type: object
        required:
          - nomeFantasia
          - endereco
          - cidade
          - estado
          - cep
          - cnpj
          - site
          - email
          - telefone
          - horarioFuncionamento
        properties:
          nomeFantasia:
            type: string
            description: Nome fantasia da empresa.
          endereco:
            type: string
            description: Endereço da empresa.
          cidade:
            type: string
            description: Cidade onde a empresa está localizada.
          estado:
            type: string
            description: Estado onde a empresa está localizada.
          cep:
            type: string
            description: CEP do endereço da empresa.
          cnpj:
            type: string
            description: CNPJ da empresa.
          site:
            type: string
            description: Site oficial da empresa.
          email:
            type: string
            format: email
            description: Email de contato da empresa.
          telefone:
            type: string
            description: Telefone comercial principal.
          horarioFuncionamento:
            type: string
            description: Horário de funcionamento da empresa.

      EmpresaPartial:
        type: object
        properties:
          nomeFantasia:
            type: string
          endereco:
            type: string
          cidade:
            type: string
          estado:
            type: string
          cep:
            type: string
          cnpj:
            type: string
          site:
            type: string
          email:
            type: string
            format: email
          telefone:
            type: string
          horarioFuncionamento:
            type: string

    tags:
      - Empresa
    responses:
      200:
        description: Empresa encontrada com sucesso.
        schema:
          $ref: '#/definitions/Empresa'
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """
    bq_ = bq(id_empresa=id_empresa)

    empresa = bq_.get_empresa()

    if not empresa:
        raise NotFound("Empresa não encontrada")

    return empresa, 200

@empresa_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="create_empresa",
    span_description="Criar uma nova empresa",
    capture_body_fields=["id_empresa"],
)
def create_empresa(id_empresa: str):
    """
    Criar uma nova empresa
    ---
    tags:
      - Empresa
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: Dados da nova empresa.
        schema:
          $ref: '#/definitions/Empresa'
    responses:
      201:
        description: Empresa criada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              format: uuid
      400:
        description: Dados inválidos.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Dados inválidos"
    """
    REQUIRED = [
        "nomeFantasia",
        "endereco",
        "cidade",
        "estado",
        "cep",
        "cnpj",
        "site",
        "email",
        "telefone",
        "horarioFuncionamento"
    ]

    data = request.json

    if not data:
        raise BadRequest("Dados inválidos")

    for key in REQUIRED:
        if key not in data.keys():
            raise BadRequest(f"Dados inválidos")
        if not data.get(key, None):
            raise BadRequest(f"Dados inválidos")

    if not id_empresa:
        raise BadRequest("Erro ao criar empresa")

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "create",
        "resource": "empresa"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 201

@empresa_bp.route('', methods=['PUT'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="update_empresa",
    span_description="Atualizar uma empresa existente",
    capture_body_fields=["id_empresa"],
)
def update_empresa(id_empresa: str):
    """
    Atualizar uma empresa existente
    ---
    tags:
      - Empresa
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: Dados completos da empresa a serem atualizados.
        schema:
          $ref: '#/definitions/Empresa'
    responses:
      200:
        description: Empresa atualizada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              format: uuid
      400:
        description: Dados inválidos.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Dados inválidos"
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """
    REQUIRED = [
        "nomeFantasia",
        "endereco",
        "cidade",
        "estado",
        "cep",
        "cnpj",
        "site",
        "email",
        "telefone",
        "horarioFuncionamento"
    ]

    data = request.json

    if not data:
        raise BadRequest("Dados inválidos")

    for key in REQUIRED:
        if key not in data.keys():
            print(key)
            raise BadRequest("Dados inválidos")
        if not data.get(key, None):
            print(key)
            raise BadRequest("Dados inválidos")

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "update",
        "resource": "empresa"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200

@empresa_bp.route('', methods=['PATCH'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="patch_empresa",
    span_description="Atualizar parcialmente uma empresa existente",
    capture_body_fields=["id_empresa"],
)
def patch_empresa(id_empresa: str):
    """
    Atualizar parcialmente uma empresa existente
    ---
    tags:
      - Empresa
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: body
        name: body
        required: true
        description: Dados parciais da empresa a serem atualizados.
        schema:
          $ref: '#/definitions/EmpresaPartial'
    responses:
      200:
        description: Empresa atualizada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              format: uuid
      400:
        description: Dados inválidos.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Campo {campo} não é válido"
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """

    data = request.json

    if not data:
        raise BadRequest("Dados inválidos")

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "patch",
        "resource": "empresa"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200

@empresa_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="delete_empresa",
    span_description="Deletar uma empresa existente",
    capture_body_fields=["id_empresa"],
)
def delete_empresa(id_empresa: str):
    """
    Deletar uma empresa existente
    ---
    tags:
      - Empresa
    responses:
      200:
        description: Empresa deletada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              format: uuid
      404:
        description: Empresa não encontrada.
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Empresa não encontrada"
    """

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": "",
        "action": "delete",
        "resource": "empresa"
    }

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200
