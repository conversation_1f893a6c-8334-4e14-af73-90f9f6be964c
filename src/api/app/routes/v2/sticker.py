from flask import Blueprint, request, current_app
import json

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import RoutesTracing

sticker_bp = Blueprint('sticker', __name__)

@sticker_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_sticker",
    span_description="Obter um sticker pelo ID da empresa",
    capture_body_fields=["id_empresa"]
)
def get_sticker(id_empresa: str):
    """
    Obter um sticker pelo ID da empresa
    ---
    definitions:
      Sticker:
        type: object
        required:
          - sticker
        properties:
          sticker:
            type: string
            description: Sticker em base64.
            example: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABjElEQVRIS+2Vv0oDQRDG
    tags:
      - Sticker
    parameters:
      - name: id_empresa
        in: query
        type: string
        required: true
        description: ID da empresa
    responses:
      200:
        description: Sticker encontrado
        schema:
          $ref: '#/definitions/Sticker'
      404:
        description: Sticker não encontrado
    """
    bq_ = bq(id_empresa=id_empresa)
    sticker = bq_.get_sticker()
    if not sticker:
        return {"error": "Sticker não encontrado"}, 404

    return sticker, 200

@sticker_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="create_sticker",
    span_description="Adicionar um sticker para a empresa",
    capture_body_fields=["id_empresa"]
)
def create_sticker(id_empresa: str):
    """
    Adicionar um sticker para a empresa. O sticker deve ser enviado em base64, iniciando com "data:image/png;base64,".
    ---
    tags:
      - Sticker
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: id_empresa
        type: string
        required: true
        description: ID da empresa
      - in: body
        name: body
        required: true
        description: Dados do sticker.
        schema:
          $ref: '#/definitions/Sticker'
    responses:
      201:
        description: Sticker criado com sucesso
      400:
        description: Dados inválidos
    """
    required = ["sticker"]
    data = request.json

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    if not id_empresa:
        return {"error": "Erro ao criar sticker"}, 400
    
    if not str(data.get("sticker")).startswith("data:image/png;base64,"):
        return {"error": "Sticker inválido"}, 400

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "create",
        "resource": "sticker"
    }


    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 201

@sticker_bp.route('', methods=['PUT'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="update_sticker",
    span_description="Atualizar um sticker existente",
    capture_body_fields=["id_empresa"]
)
def update_sticker(id_empresa: str):
    """
    Atualizar um sticker existente
    ---
    tags:
      - Sticker
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: id_empresa
        type: string
        required: true
        description: ID da empresa
      - in: body
        name: body
        required: true
        description: Dados completos do sticker a serem atualizados.
        schema:
          $ref: '#/definitions/Sticker'
    responses:
      200:
        description: Sticker atualizado com sucesso
      400:
        description: Dados inválidos
    """
    required = ["sticker"]
    data = request.json

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "data": data,
        "action": "create",
        "resource": "sticker"
    }


    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200

@sticker_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="delete_sticker",
    span_description="Deletar um sticker existente",
    capture_body_fields=["id_empresa"]
)
def delete_sticker(id_empresa: str):
    """
    Deletar um sticker existente
    ---
    tags:
      - Sticker
    consumes:
      - application/json
    produces:
      - application/json
    parameters:
      - in: query
        name: id_empresa
        type: string
        required: true
        description: ID da empresa
    responses:
      200:
        description: Sticker deletado com sucesso
    """
    task = {
        "type": "api-v2",
        "id_empresa": id_empresa,
        "action": "delete",
        "data": {},
        "resource": "sticker"
    }


    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200
