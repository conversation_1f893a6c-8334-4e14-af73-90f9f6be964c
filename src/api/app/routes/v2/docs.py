"""Modulo de rotas para os documentos do RAG"""
import os
import json
from tempfile import NamedTemporaryFile
from flask import Blueprint, request, current_app, send_file, after_this_request, url_for
from werkzeug.exceptions import NotFound

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.exception.exception import BadRequest
from src.api.app.limiter.limiter import limiter
from src.data.rag import Rag
from src.data.google_storage import Bucket
from src.extras.util import RoutesTracing

doc_bp = Blueprint('doc', __name__)


@doc_bp.route('/text', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_doc_text",
    capture_body_fields=["id_empresa"],
)
def get_doc_text(id_empresa: str):
    """
    Rota para obter o texto relacionado ao documento da empresa.
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id

    responses:
      200:
        description: Operação realizada com sucesso.
        schema:
          type: object
          properties:
            data:
              type: string
              example: "Texto do documento"
            success:
              type: string
              example: "success"
      202:
        description: Documento ainda não processado.
        schema:
          type: object
          properties:
            success:
              type: string
              example: success
            data:
              type: object
              properties:
                status:
                  type: string
                  example: "pending"
                action:
                  type: string
                  example: create
                message:
                  type: string
                  example: "Documento ainda não processado. Aguardando processamento."
      400:
        description: Erro de validação.
        schema:
          type: object
          properties:
            error:
              type: string
          example: "Dados inválidos"
      404:
        description: Documento não encontrado.
        schema:
          type: object
          properties:
            error:
              type: string
          example: "Documento não encontrado"
    """

    doc = Rag(id_empresa).get_full_doc(tag="custom_pdf")
    if not doc:
        if not (status := current_app.redis_client.get(f"docs:pending:{id_empresa}")):
            raise NotFound("Documento não encontrado")
        if status.decode("utf-8") == "error":
            return {
                "success": "error",
                "data": {
                    "status": "error",
                    "action": status.decode("utf-8"),
                    "message": "Erro ao processar o documento. Verifique o arquivo enviado."
                }
            }, 400
        return {
            "success": "success",
            "data": {
                "status": "pending",
                "action": status.decode("utf-8"),
                "message": "Documento ainda não processado. Aguardando processamento."
            }
        }, 202

    return {
        "success": "success",
        "data": doc
    }, 200


@doc_bp.route('', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_doc",
    capture_body_fields=["id_empresa"],
)
def get_doc(id_empresa: str):
    """
    Rota para obter o status do pdf.
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id

    responses:
      200:
        description: Operação realizada com sucesso.
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                status:
                  type: string
                  example: "completed"
                url_download:
                  type: string
                  example: https://api.conversas.ai/doc/pdf/download?empresa=empresa-1
                action:
                  type: string
                  example: create
                message:
                  type: string
                  example: "Documento ainda não processado. Aguardando processamento."
    """

    doc = Rag(id_empresa).get_full_doc(tag="custom_pdf")
    if not doc:
        if not (status := current_app.redis_client.get(f"docs:pending:{id_empresa}")):
            return {
                "data": {
                    "status": "not_found",
                    "action": "",
                    "url_download": None,
                    "message": "Documento não encontado."
                }
            }, 200
        print(status)
        if status.decode("utf-8") == "error":
            return {
                "data": {
                    "status": "error",
                    "action": status.decode("utf-8"),
                    "url_download": None,
                    "message": "Erro ao processar o documento. Verifique o arquivo enviado."
                }
            }, 200
        return {
            "data": {
                "status": "pending",
                "action": status.decode("utf-8"),
                "url_download": None,
                "message": "Documento ainda não processado. Aguardando processamento."
            }
        }, 200
    elif (status := current_app.redis_client.get(f"docs:pending:{id_empresa}")) == b'delete':
        return {
            "data": {
                "status": "pending",
                "action": status.decode("utf-8"),
                "url_download": None,
                "message": "Apagando documento..."
            }
        }, 200


    url_download = url_for("doc.get_pdf", empresa=id_empresa, _external=True)
    return {
        "data": {
            "status": "completed",
            "action": "",
            "url_download": url_download,
            "message": "O documento está disponível!"
        }
    }, 200


@doc_bp.route('/pdf/download', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_pdf",
    capture_body_fields=["id_empresa"],
)
def get_pdf(id_empresa: str):
    """
    Rota para obter o pdf de informações da empresa.
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id

    responses:
      200:
        description: Operação realizada com sucesso.
        content:
          application/pdf:
            schema:
              type: string
              format: binary
      202:
        description: Documento ainda não processado.
        schema:
          type: object
          properties:
            status:
              type: string
              example: "pending"
            message:
              type: string
              example: "Documento ainda não processado. Aguardando processamento."
      400:
        description: Erro de validação.
        schema:
          type: object
          properties:
            error:
              type: string
          example: "Dados inválidos"
      404:
        description: Documento não encontrado.
        schema:
          type: object
          properties:
            error:
              type: string
          example: "Documento não encontrado"
    """
    bucket = Bucket("custom-pdf")
    file_name = f"empresa-{id_empresa}.pdf"
    temp = NamedTemporaryFile(delete=False)

    try:
        bucket.download(file_origin=file_name, file_dest=temp.name)
    except FileNotFoundError:
        if not (status := current_app.redis_client.get(f"docs:pending:{id_empresa}")):
            raise NotFound("Documento não encontrado")
        if status.decode("utf-8") == "error":
            return {
                "success": "error",
                "data": {
                    "status": "error",
                    "action": status.decode("utf-8"),
                    "message": "Erro ao processar o documento. Verifique o arquivo enviado."
                }
            }, 400
        return {
            "success": "success",
            "data": {
                "status": "pending",
                "action": status.decode("utf-8"),
                "message": "Documento ainda não processado. Aguardando processamento."
            }
        }, 202

    @after_this_request
    def remove_file(response):
        try:
            os.remove(temp.name)
        except Exception:
            pass
        return response

    return send_file(
        temp.name,
        mimetype="application/pdf",
        as_attachment=True,
        download_name=file_name
    )


@doc_bp.route('', methods=['POST'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="create_doc",
    capture_body_fields=["id_empresa"],
)
def create_doc(id_empresa: str):
    """
    Rota para adicionar um novo pdf.
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
      - name: body
        in: body
        type: object
        required: true
        properties:
          file:
            type: string
            format: binary
            description: Arquivo PDF a ser enviado
            required: true
            example: "base64-encoded-pdf-data"
    responses:
      201:
        description: Operação realizada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
      400:
        description: Erro de validação.
        schema:
          type: object
          properties:
            error:
              type: string
        example: "Dados inválidos"
    """
    data = request.json or {}

    data = data.get("file")

    if not data:
        raise BadRequest("Dados inválidos")


    # try:
    #     data = data.decode("utf-8")
    # except UnicodeDecodeError:
    #     data = data.decode("latin-1")
    # except Exception as e:
    #     raise BadRequest(f"Erro ao decodificar os dados: {str(e)}")

    task = {
        "id_empresa": id_empresa,
        "data": data,
        "action": "create",
    }

    current_app.redis_client.rpush("task_queue_document", json.dumps(task))
    current_app.redis_client.set(
        f"docs:pending:{id_empresa}",
        "create",
        ex=8*60*60
    )

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 201


@doc_bp.route('', methods=['PUT'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="update_doc",
    capture_body_fields=["id_empresa"],
)
def update_doc(id_empresa: str):
    """
    Rota para atualizar um documento existente.
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
      200:
        description: Operação realizada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
      400:
        description: Erro de validação.
        schema:
          type: object
          properties:
            error:
              type: string
          example: "Dados inválidos"
    """
    data = request.data

    if not data:
        raise BadRequest("Dados inválidos")

    try:
        data = data.decode("utf-8")
    except UnicodeDecodeError:
        data = data.decode("latin-1")
    except Exception as e:
        raise BadRequest(f"Erro ao decodificar os dados: {str(e)}")

    task = {
        "id_empresa": id_empresa,
        "data": data,
        "action": "update",
    }

    current_app.redis_client.rpush("task_queue_document", json.dumps(task))
    current_app.redis_client.set(
        f"docs:pending:{id_empresa}",
        "update",
        ex=8*60*60
    )

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200


@doc_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="delete_doc",
    capture_body_fields=["id_empresa"],
)
def delete_doc(id_empresa: str):
    """
    Rota para deletar um documento existente
    ---
    tags:
      - Empresa
    security:
      - Bearer: []
    parameters:
      - name: empresa
        in: query
        type: string
        default: empresa-1
        required: true
        description: ID da academia, no formato chave-id
    responses:
      200:
        description: Operação realizada com sucesso.
        schema:
          type: object
          properties:
            success:
              type: string
              example: "success"
            id_empresa:
              type: string
              example: "empresa-1"
    """
    task = {
        "id_empresa": id_empresa,
        "action": "delete",
    }

    current_app.redis_client.rpush("task_queue_document", json.dumps(task))
    current_app.redis_client.set(
        f"docs:pending:{id_empresa}", "delete", ex=60*5
    )

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200
