import logging
from flask import Blueprint, request
from werkzeug.exceptions import NotFound

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter

from src.data.bigquery_data import BigQueryData as bq
from src.api.app.exception.exception import BadRequest

from src.extras.util import parse_phone, RoutesTracing

logger = logging.getLogger()

users_bp = Blueprint('users', __name__)

@users_bp.route('/memories', methods=['GET'])
@limiter
@authentication_required
@RoutesTracing(
    span_name_prefix="get_user_memories",
    span_description="Consulta a memória de contexto do usuário",
    capture_body_fields=["id_empresa", "phone_number"]
)
def get_user_memories(id_empresa : str = None):
    """
    Consulta a memória de contexto do usuário
    ---
    definitions:
      Usuarios: 
        type: object
        required:
          - id_empresa
          - phone_number
        properties:
          id_empresa:
            type: string
            description: ID da empresa.
          phone_number:
            type: string
            description: Número do telefone.
    tags:
      - Usuários
    parameters:
      - name: id_empresa
        in: query
        type: string
        required: true
        description: ID da empresa
      - name: phone_number
        in: query
        type: string
        required: true
        description: Telefone do usuário
    responses:
      200:
        description: Memórias do usuário encontradas com sucesso.
      404:
        description: Memórias não encontradas.
    """

    logger.info("Request do id_empresa e phone_number.")

    if not id_empresa:
        id_empresa = request.args.get("id_empresa")

    logger.info(request.args)

    phone_number = request.args.get("phone_number")

    if not phone_number:
        raise BadRequest("Numero de telefone nao informado.")

    bq_ = bq(id_empresa=id_empresa)

    phone_number = parse_phone(phone_number)

    logger.info("Buscando no BigQuery.")

    memoria = bq_.get_memories(phone_number)

    if not memoria:
        raise NotFound("Memória do usuário não encontrada.")

    return memoria, 200


