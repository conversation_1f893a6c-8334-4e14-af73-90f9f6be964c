import os
import importlib
from flask import current_app
import logging

logger = logging.getLogger("conversas_logger")

def run_redis_migrations():
    migrate_path = os.path.join(os.path.dirname(__file__), 'migrations/redis')
    for filename in os.listdir(migrate_path):
        if filename.startswith('version') and filename.endswith('.py'):
            module_name = filename[:-3]
            migration_key = f'migration:{module_name}'

            # Check if the migration has already been executed
            if current_app.redis_client.exists(migration_key):
                logger.info(f'Migration {module_name} already executed.')
                continue

            module = importlib.import_module(f'migrations.redis.{module_name}')
            if hasattr(module, 'migrate') and callable(getattr(module, 'migrate')):
                try:
                    current_app.redis_client.set(migration_key, 'success')
                    getattr(module, 'migrate')()
                    # Store the filename in Redis if migration is successful
                    logger.info(f"Migration {migration_key} executed succesfully")
                except Exception as e:
                    # Optionally log the exception or handle it
                    current_app.redis_client.delete(migration_key)
                    current_app.logger.error(f'Failed to run migration for {module_name}: {e}')
