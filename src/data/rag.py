"""Módulo com gerenciador de Utilidades para RAG (Retrieval-Augmented Generation)"""
import os
import logging
import uuid
from unidecode import unidecode

from qdrant_client.models import VectorParams, PointStruct, ScoredPoint, FilterSelector
from qdrant_client.http.exceptions import UnexpectedResponse

from src.connections.qdrant import QdrantConnection

if "worker" in os.getenv("MODE", ""):
    from src.worker.llm_modules.openai.openai_embeddings_module import OpenAIEmbeddingsModule
else:
    from unittest.mock import MagicMock as OpenAIEmbeddingsModule

QDRANT_CONN = QdrantConnection()
logger = logging.getLogger("conversas_logger")


class Rag:
    """Classe responsável por realizar a comunicação com o Qdrant."""

    def __init__(self, id_empresa: str, telefone: str = None) -> None:
        """Inicializa a classe com o id da empresa."""
        self.qdrant = QDRANT_CONN.client
        self.model = OpenAIEmbeddingsModule()
        self.collection = f"rag_{id_empresa}"
        if not self.qdrant.collection_exists(self.collection):
            try:
                self.qdrant.create_collection(
                    collection_name=self.collection,
                    vectors_config=VectorParams(
                        size=1536,
                        distance="Cosine"
                    )
                )
                self.new_collection = True
                self._create_payload_index()
            except UnexpectedResponse:
                self.new_collection = False
        else:
            self.new_collection = False

    def _create_payload_index(self) -> None:
        self.qdrant.create_payload_index(
            collection_name=self.collection,
            field_name="doc_index",
            field_schema="integer"
        )
        self.qdrant.create_payload_index(
            collection_name=self.collection,
            field_name="tag",
            field_schema="keyword"
        )

    def search(self, query: str, top_k: int = 5) -> str:
        """Realiza a busca no Qdrant."""
        if not query:
            return ""

        query = unidecode(query)
        query_vector = self.model.get_embeddings([query])[0]['embedding']

        response = self.qdrant.search(
            self.collection, query_vector, limit=top_k)
        logger.info(response)

        return self.format_search_results(response)

    def insert(
        self, text: str | list, tag: str
    ) -> None:
        """Insere um novo documento no Qdrant."""
        embeddings = self.model.get_embeddings(text)
        points = []
        for i, embedding in enumerate(embeddings):
            point = PointStruct(
                id=str(uuid.uuid4()),
                vector=embedding['embedding'],
                payload={
                    "text": embedding['text_chunk'],
                    "tag": tag,
                    "doc_index": i,
                }
            )
            points.append(point)
        try:
            self.qdrant.upsert(
                self.collection,
                points=points
            )
        except Exception as e:
            logger.error(f" [*] Erro ao inserir documento no Qdrant: {e}")

    def delete(self, tag: str) -> None:
        """Deleta um documento do Qdrant."""
        self.qdrant.delete(
            self.collection,
            points_selector=FilterSelector(
                filter={
                    "must": [{
                        "key": "tag",
                        "match": {
                            "value": tag
                        }
                    }]
                }
            )
        )

    def update(self, text: str | list, tag: str) -> None:
        """Atualiza um documento no Qdrant."""
        self.delete(tag)
        self.insert(text=text, tag=tag)

    def populate(self, texts: dict) -> None:
        """Popula o Qdrant com os dados."""
        for tag, text in texts.items():
            self.insert(text, tag)

    def format_search_results(self, search_results: list[ScoredPoint]) -> str:
        """Formata os resultados da busca."""
        if not search_results:
            return ""
        responses = [hit.payload.get('text', '') for hit in search_results]
        return '\n\n'.join(responses)

    def get_full_doc(
        self, tag: str, limit: int = 1000
    ) -> str | None:
        """
        Retorna os documentos do Qdrant com o tag especificado.
        """
        try:
            response = self.qdrant.scroll(
                collection_name=self.collection,
                limit=limit,
                scroll_filter={
                    "must": [{
                        "key": "tag",
                        "match": {
                            "value": tag
                        }
                    }]
                },
                order_by="doc_index",
            )
        except:
            response = None

        if not response:
            return None

        response = [
            record.payload.get('text', '') for record in response[0]
        ]

        return ' '.join(response)
