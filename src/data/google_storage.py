from src.connections.connections import Connections
from src.extras.util import retry
import logging

connections = Connections.get_instance()
logger = logging.getLogger("conversas_logger")

class Bucket():

    def __init__(self, bucket_name):
        self.bucket_name = bucket_name
        self.bucket = connections.storage_client.bucket(self.bucket_name)

        if self.bucket.exists():
            logger.info(f" [*] Conexão com o bucket {bucket_name} estabelecida com sucesso.")
        else:
            logger.error(f" [*] Falha ao conectar com o bucket {bucket_name}.")
    
    @retry(retries=3, delay=2, backoff=2, status_codes=[500, 502, 503, 504])
    def download(self, file_origin, file_dest):
        """Faz download de um arquivo para o bucket."""
        try:
            blob = self.bucket.blob(file_origin)
            if not blob.exists():
                logger.error(f" [*] Arquivo {file_origin} não encontrado no bucket {self.bucket_name}.")
                raise FileNotFoundError(f"Arquivo {file_origin} não encontrado no bucket {self.bucket_name}.")
            blob.download_to_filename(file_dest)
            logger.info(f" [*] Download do arquivo: {file_origin} para {file_dest}")
        except Exception as e:
            logger.error(f" [*] Erro ao fazer download do arquivo: {file_origin} para {file_dest}")
            raise e

    @retry(retries=3, delay=2, backoff=2, status_codes=[500, 502, 503, 504])
    def upload(self, file_origin, file_dest, return_url=False, from_string=False, content_type=None):
        """
        Faz upload de um arquivo para o bucket.

        Args:
            file_origin (str): Caminho do arquivo de origem.
            file_dest (str): Caminho do arquivo de destino no bucket.
            return_url (bool): Se True, retorna a URL pública do arquivo.
            from_string (bool): Se True, faz upload a partir de uma string.
            content_type (str): Tipo de conteúdo do arquivo.
        """
        try:
            blob = self.bucket.blob(file_dest)
            if from_string:
                blob.upload_from_string(file_origin, content_type=content_type)
            else:
                blob.upload_from_filename(file_origin)
                logger.info(f" [*] Upload do arquivo: {file_origin} para {file_dest}")
            if return_url:
                blob.make_public()
                return blob.public_url
        except Exception as e:
            logger.error(f" [*] Erro ao fazer upload do arquivo: {file_origin} para {file_dest}")
            raise e

    @retry(retries=3, delay=2, backoff=2, status_codes=[500, 502, 503, 504])
    def delete(self, file):
        """Faz upload de um arquivo para o bucket."""
        try:
            blob = self.bucket.blob(file)
            if not blob.exists():
                logger.error(f" [*] Arquivo {file} não encontrado no bucket {self.bucket_name}.")
                return
            blob.delete()
            logger.info(f" [*] Arquivo deletado: {file}")
        except Exception as e:
            logger.error(f" [*] Erro ao deletar o arquivo: {e}")
            raise e

    @retry(retries=3, delay=2, backoff=2, status_codes=[500, 502, 503, 504])
    def list(self, dir):
        blobs = self.bucket.list_blobs(prefix=dir)

        filenames = []
        for blob in blobs:
            filename = blob.name
            # if not filename.endswith('.json'): continue
            filenames.append(filename)

        return filenames
