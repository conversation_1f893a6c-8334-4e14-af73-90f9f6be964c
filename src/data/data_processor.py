import json
import re
import os
import datetime
from typing import Literal, Dict, List, Any
from datetime import datetime as dt, timedelta

from pandas import DataFrame, Timedelta

from src.extras.util import format_seconds_to_readable
if os.getenv("MODE") == "worker":
    from src.worker.stt_modules.openai_stt.openai_stt_module import proccess_audio
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools as PIT

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

# TODO: Trocar todos esses processamentos de dados para o módulo de integrações

LINK_APP_TREINO = os.getenv("LINK_APP_TREINO", "https://apptreino.com.br/#baixar")

class DataProcessor:
    def __init__(self) -> None:
        pass

    @staticmethod
    def process_plans_data(
        data: object,
        return_chunks: bool = False,
    ) -> tuple[str, list] | list:
        """
        Essa função processa o json com os planos e retorna uma string com as informações dos planos e uma lista com os nomes dos planos
        """
        final_string = ""
        chunks = []
        current_string = ""
        nomes_planos = []
        if type(data) == str:
            data = json.loads(data)
        for tipo in data:
            if tipo == "planosComVendaOnline":
                current_string += "Esses são os planos com link para venda online: \n"
                for plano in data[tipo]:
                    current_string = ""
                    current_string += "["
                    current_string += plano.get("descricao") + "\n"
                    nomes_planos.append(plano.get("descricao"))
                    if plano.get("urlVendaOnline") != None:
                        current_string += "Esse é o link para comprar o plano: " + plano.get("urlVendaOnline") + "\n"
                    else:
                        current_string += "Esse plano não possui link para compra online \n"
                    if plano.get("condicaoPagamento"):
                        current_string += "Essa é a condição de pagamento: " + plano.get("condicaoPagamento") + "\n"
                    else:
                        current_string += "Esse plano não possui informação da condição de pagamento \n"
                    if plano.get("descricaoEncantamento") != None:
                        current_string += "Descrição do plano: " + plano.get("descricaoEncantamento") + "\n"
                    if plano.get("modalidades") and len(plano.get("modalidades")) > 0:
                        current_string += f"Essas são as modalidades do plano {nomes_planos[-1]}: \n"
                        for modalidade in plano.get("modalidades"):
                            current_string += modalidade.get("modalidade") + "\n"
                    if plano.get("valorMensal") != None:
                        current_string += "Esse é o valor mensal do plano: " + str(plano.get("valorMensal")) + "\n"
                    if plano.get("taxaAdesao") not in [0.0, 0, None]:
                        current_string += "Essa é a taxa de adesão do plano: " + str(plano.get("taxaAdesao")) + "\n"
                    else:
                        current_string += "Taxa de adesão grátis \n"
                    if plano.get("valorAnuidade") not in [0.0, 0, None]:
                        current_string += "Esse é o valor da anuidade do plano: " + str(plano.get("valorAnuidade")) + "\n"
                        current_string += "Essa é a data do pagamento da anuidade: \n"
                        if plano.get("mesAnuidade") != None:
                            current_string += "Mês: " + str(plano.get("mesAnuidade")) + "\n"
                        if plano.get("diaAnuidade") != None:
                            current_string += "Dia: " + str(plano.get("diaAnuidade")) + "\n"
                        if plano.get("anuidadeNaParcela"):
                            current_string += "Você pagará a anuidade junto de uma parcela do plano \n"
                        else:
                            current_string += "Você pagará a anuidade separadamente \n"
                    if plano.get("valorTotalDoPlano") not in [0.0, 0, None]:
                        current_string += "Esse é o valor total do plano: " + str(plano.get("valorTotalDoPlano")) + "\n"
                    if plano.get("duracaoPlano") != None:
                        current_string += "Essa é a duração do plano: " + str(plano.get("duracaoPlano")) + "\n"
                    if plano.get("quantidadeDiasExtra") != None:
                        current_string += "Essa é a quantidade de dias extras do plano: " + str(plano.get("quantidadeDiasExtra")) + "\n"
                    if plano.get("planoPersonal") not in [None, "", False]:
                        current_string += "Esse plano é disponível para personal trainers \n"
                    if plano.get("regimeRecorrencia") not in [None, "", False]:
                        current_string += "Esse plano é recorrente \n"
                    if plano.get("renovavelAutomaticamente") not in [None, "", False]:
                        current_string += "Esse plano é renovável automaticamente \n"
                    if plano.get("maximoVezesParcelar") not in [0, None]:
                        current_string += "Esse plano pode ser parcelado até " + str(plano.get("maximoVezesParcelar")) + " vezes \n"
                    if plano.get("categorias") != None and len(plano.get("categorias")) > 0:
                        current_string += "Essas são as categorias do plano: \n"
                        for categoria in plano.get("categorias"):
                            current_string += str(categoria) + "\n"
                    current_string = current_string[:-1]
                    current_string += "]\n"
                    final_string += current_string
                    chunks.append(current_string)
                
            elif tipo == "planosComVendaConsultor":
                for plano in data[tipo]:
                    current_string = ""
                    current_string += "["
                    if plano.get("descricao"):
                        current_string += plano.get("descricao") + "\n" 
                        nomes_planos.append(plano.get("descricao"))
                    if plano.get("percentualmultacancelamento"):
                        current_string += "Esse é o percentual de multa por cancelamento: " + str(plano.get("percentualmultacancelamento")) + "\n"
                    if plano.get("recorrencia") not in [None, "", False]:
                        current_string += "Esse plano é recorrente \n"
                    if plano.get("permitiracessosomentenaempresavendeucontrato") == False:
                        current_string += "Esse plano permite acesso em todas as unidades relacionadas a essa academia \n"
                    if plano.get("planopersonal") not in [None, "", False]:
                        current_string += "Esse plano é disponível para personal trainers \n"
                    if plano.get("permitepagarcomboleto") not in [None, "", False]:
                        current_string += "Esse plano permite pagamento com boleto \n"
                    if plano.get("convidadospormes") not in [0.0, 0, None]:
                        current_string += "Esse plano permite " + str(plano.get("convidadospormes")) + " convidados por mês \n"
                    if plano.get("valordescontoboletopagantecipado") not in [0.0, 0, None]:
                        current_string += "Esse é o valor do desconto para pagamento antecipado no boleto: " + str(plano.get("valordescontoboletopagantecipado")) + "\n"
                    if plano.get("porcentagemdescontoboletopagantecipado") not in [0.0, 0, None]:
                        current_string += "Esse é o percentual do desconto para pagamento antecipado no boleto: " + str(plano.get("porcentagemdescontoboletopagantecipado")) + "\n"

                    current_string = current_string[:-1]
                    current_string += "\nCASO O CLIENTE PERGUNTE ALGO DIFERENTE SOBRE O PLANO, RECOMENDE FALAR COM UM CONSULTOR, ESSE PLANO NÃO TEM LINK DE COMPRA]\n"
                    final_string += current_string
                    chunks.append(current_string)
        if return_chunks:
            return chunks

        return final_string, nomes_planos

    @staticmethod
    def process_gym_data(data: object) -> str:
        """
        Essa função processa o json com as informações da academia e retorna uma string com as informações
        """
        string = ""
        if data.get("nomeFantasia") != None:
            string += "Nome da empresa: " + str(data.get("nomeFantasia")) + "\n"
        elif data.get("razaoSocial") != None:
            string += "Nome da empresa: " + str(data.get("razaoSocial")) + "\n"
        if data.get("endereco") != None:
            string += "Endereço: " + str(data.get("endereco")) + "\n"
        if data.get("cidade") != None:
            string += "Cidade: " + str(data.get("cidade")) + "\n"
        if data.get("estado") != None:
            string += "Estado: " + str(data.get("estado")) + "\n"
        if data.get("cep") != None:
            string += "CEP: " + str(data.get("cep")) + "\n"
        if data.get("complemento") != None:
            string += "Complemento: " + str(data.get("complemento")) + "\n"
        if data.get("numero") != None:
            string += "Número: " + str(data.get("numero")) + "\n"
        if data.get("setor") != None:
            string += "Setor: " + str(data.get("setor")) + "\n"
        if data.get("cnpj") != None:
            string += "CNPJ: " + str(data.get("cnpj")) + "\n"
        if data.get("site") not in [None, "", False]:
            string += "Site da empresa: " + str(data.get("site")) + "\n"
        if data.get("email") not in [None, "", False]:
            string += "Email: " + str(data.get("email")) + "\n"
        if data.get("telComercial1") != None:
            string += "Telefone comercial 1: " + str(data.get("telComercial1")) + "\n"
        if data.get("telComercial2") not in [None, "", False]:
            string += "Telefone comercial 2: " + str(data.get("telComercial2")) + "\n"
        if data.get("telComercial3") not in [None, "", False]:
            string += "Telefone comercial 3: " + str(data.get("telComercial3")) + "\n"
        if data.get("permiteContratosConcomitantes") not in [None, "", False]:
            string += "Essa empresa permite contratos concomitantes \n"
        if data.get("urlLojaVendaOnline") not in [None, "", False]:
            string += f"Esse é o link para a loja de venda online: {data.get('urlLojaVendaOnline')} \n"
        if data.get("urlPlanosLojaVendaOnline") not in [None, "", False]:
            string += f"Esse é o link para os planos na loja de venda online: {data.get('urlPlanosLojaVendaOnline')} \n"
        if data.get("urlProdutosLojaVendaOnline") not in [None, "", False]:
            string += f"Esse é o link para os produtos na loja de venda online: {data.get('urlProdutosLojaVendaOnline')} \n"
        if data.get("urlAgendaAulasLojaVendaOnline") not in [None, "", False]:
            string += f"Esse é o link para o aluno ver a agenda das aulas online: {data.get('urlAgendaAulasLojaVendaOnline')} \n"
        if data.get("horario_funcionamento") not in [None, "", False]:
            string += "Esse é o horário de funcionamento da academia: " + str(data.get("horario_funcionamento")) + "\n"
        if data.get("proposito") not in [None, ""]:
            string += "Propósito da empresa: " + str(data.get("proposito")) + "\n"
        if data.get("site_texts") not in [None, ""]:
            string += "Informações adicionais do site: " + str(data.get("site_texts")) + "\n"

        return string

    @staticmethod
    def process_user_data(
        data: object, id_empresa=None, telefone=None
    ) -> str:
        """
        Essa função processa o json com as informações do usuário e retorna uma string com as informações
        """
        if id_empresa:
            pit = PIT(id_empresa=id_empresa)
        else:
            pit = None

        string = ""
        try:

            aluno = data.get("aluno", {})
            pessoa = aluno.get("pessoa", {})
            situacao = aluno.get("situacao", {})
            parcelas = data.get("parcelas", [])
            contratos = data.get("contratos", [])
            historico_contatos = data.get("historicoContatos", [])
            programas_treino = data.get("programasTreino", {})
            avaliacao_fisica = data.get("avaliacaoFisica", {})
            acessos = data.get("acessos", [])
            boletim_de_visita = data.get("boletimDeVisita", {})

            nome = pessoa.get("nome")

            if nome != None:
                string += "Nome: " + str(pessoa.get("nome")) + "\n"
            if pessoa.get("dataNasc") != None:
                string += "Data de nascimento: " + str(pessoa.get("dataNasc")) + "\n"
            if pessoa.get("profissao") != None:
                string += "Profissão: " + str(pessoa.get("profissao")) + "\n"
            if pessoa.get("telefonesconsulta") != None:
                string += "Telefone: " + str(pessoa.get("telefonesconsulta")) + "\n"
            if pessoa.get("emails") != None:
                emails = pessoa.get("emails")
                string += "Emails: " + "\n"
                for email in emails:
                    string += "- " + str(email.get("email")) + "\n"
            if aluno.get("matricula") != None:
                string += "Matrícula: " + str(aluno.get("matricula")) + "\n"
            if aluno.get("codigo") != None:
                cod_cliente= aluno.get("codigo")
            else:
                cod_cliente = None
            if isinstance(situacao, dict):
                if situacao.get("descricao") != None:
                    string += "Situação: " + str(situacao.get("descricao")) + "\n"
                if situacao.get("grupo", {}).get("descricao") != None:
                    string += "Grupo: " + str(situacao.get("grupo", {}).get("descricao")) + "\n"
            elif isinstance(situacao, str):
                string += "Situação: " + str(situacao) + "\n"
            if aluno.get("linkAppTreino", None) not in (None, ""):
                string += "Link para o App Treino: " + str(aluno.get("linkAppTreino")) + "\n" 
            else:
                string += "Link para o App Treino: " + LINK_APP_TREINO + "\n"

            if cod_cliente and pit:
                needs_link = False
                next_expiration = None
                
                if isinstance(parcelas, list):
                    now = datetime.datetime.now()

                    parcelas = sorted(
                        parcelas,
                        key=lambda x: (
                            datetime.datetime.strptime(
                                x.get("datavencimento"), "%d/%m/%Y %H:%M:%S"
                            ).timestamp()
                            if x.get("datavencimento") is not None else 0
                        )
                    )
                    
                    for parcela in parcelas:
                        try:
                            if expiration_str := parcela.get("datavencimento"):
                                expiration = datetime.datetime.strptime(
                                    expiration_str, "%d/%m/%Y %H:%M:%S"
                                )
                                delta = expiration - now
                                delta_seconds = delta.total_seconds()
                                
                                if delta_seconds < 0:
                                    needs_link = True
                                else:
                                    # Track upcoming payment if closer than current closest
                                    next_expiration = delta_seconds
                                    break
                        except TypeError:
                            continue

                link_data = pit.get_payment_link(
                    cod_cliente, next_expiration, telefone, nome
                ) if needs_link else {}

                if link_data.get("link") is not None:
                    string += ("ATENÇÃO: O aluno tem parcelas em atraso. Envie o seguinte link para o pagamento:" 
                            f"{link_data.get('link')}")

                    if len(parcelas) > 0:
                        string += "Parcelas: \n"
                        for parcela in parcelas:
                            try:
                                if parcela is None:
                                    continue
                                if (parcela.get("situacao") or {}).get("descricao") in [None, "PG", "RN"]:
                                    continue
                                if parcela.get("descricao") is not None:
                                    string += "- Descrição: " + str(parcela.get("descricao")) + "\n"
                                if parcela.get("valorParcela") is not None:
                                    string += "- Valor da parcela: " + str(parcela.get("valorParcela")) + "\n"
                                if parcela.get("situacao", {}).get("descricao") is not None:
                                    string += "- Situação: " + str(parcela.get("situacao", {}).get("descricao")) + "\n"
                                if parcela.get("datavencimento") is not None:
                                    datavencimento_str = parcela.get("datavencimento")
                                    try:
                                        datavencimento = datetime.datetime.strptime(datavencimento_str, "%d/%m/%Y %H:%M:%S")
                                        string += f"- Data de vencimento: {datavencimento.strftime('%Y-%m-%d')}\n"
                                    except ValueError:
                                        string += f"- Data de vencimento: {datavencimento_str}\n"
                            except AttributeError:
                                continue
                else:
                    string += "O aluno não possui parcelas em atraso para serem pagas.\n"

            if len(contratos) > 0:
                string += "Contratos: " + "\n"
                for contrato in contratos:
                    if contrato is None:
                        continue
                    if contrato.get("datamatricula") != None:
                        string += "- Data da matrícula: " + str(contrato.get("datamatricula")) + "\n"
                    if contrato.get("vigenciaate") != None:
                        string += "- Data de vigência até: " + str(contrato.get("vigenciaate")) + "\n"
                    if contrato.get("vigenciade") != None:
                        string += "- Data de vigência de: " + str(contrato.get("vigenciade")) + "\n"
                    if contrato.get("valorfinal") != None:
                        string += "- Valor final: " + str(contrato.get("valorfinal")) + "\n"
                    if contrato.get("situacaocontrato", {}) != None:
                        if contrato.get("situacaocontrato", {}).get("descricao") != None:
                            string += "- Situação do contrato: " + str(contrato.get("situacaocontrato", {}).get("descricao")) + "\n"
                    if contrato.get("situacao", {}) != None:
                        if contrato.get("situacao", {}).get("descricao") != None:
                            string += "- Situação: " + str(contrato.get("situacao", {}).get("descricao")) + "\n"
                    if contrato.get("plano", {}) != None:
                        if contrato.get("plano", {}).get("descricao") != None:
                            string += "- Plano: " + str(contrato.get("plano", {}).get("descricao")) + "\n"
                
            else:
                string += "Não há contratos disponíveis, *o aluno não tem plano*" + "\n"

            if len(historico_contatos) > 0:
                string += "Histórico de contatos: " + "\n"
                for contato in historico_contatos:
                    if contato is None:
                        continue
                    if contato.get("dia") != None:
                        string += "- Dia: " + str(contato.get("dia")) + "\n"
                    if contato.get("observacao") != None:
                        string += "- Observação: " + str(contato.get("observacao")) + "\n"
                    if contato.get("fase", {}) != None and contato.get("fase", {}).get("descricao") != None:
                        string += "- Fase: " + str(contato.get("fase", {}).get("descricao")) + "\n"

            if programas_treino.get("nomeProgramaAtual") != None:
                string += "Programa de treino atual: " + str(programas_treino.get("nomeProgramaAtual")) + "\n"
            if programas_treino.get("totalAulasPrevistas") != None:
                string += "Total de aulas previstas: " + str(programas_treino.get("totalAulasPrevistas")) + "\n"
            if programas_treino.get("nrTreinosRealizados") != None:
                string += "Número de treinos realizados: " + str(programas_treino.get("nrTreinosRealizados")) + "\n"
            if len(programas_treino.get("programas", [])) > 0:
                string += "Programas de treino: " + "\n"
                for programa in programas_treino.get("programas"):
                    if programa is None:
                        continue
                    else:
                        string += "- " + json.dumps(programa) + "\n"
            if avaliacao_fisica.get("totalAvaliacoes") != None:
                string += "Avaliação física: " + "\n"
                string += "- Total de avaliações: " + str(avaliacao_fisica.get("totalAvaliacoes")) + "\n"
                string += "- Período de dias: " + str(avaliacao_fisica.get("periodoDias")) + "\n"
                string += "- Dias de avaliação: " + str(avaliacao_fisica.get("diasProximaAvaliacao")) + "\n"
                string += "- Data da próxima avaliação: " + str(avaliacao_fisica.get("dataProxima")) + "\n"
                string += "- Percentual de massa gorda: " + str(avaliacao_fisica.get("percentualMassaGorda")) + "\n"
                string += "- Percentual de massa magra: " + str(avaliacao_fisica.get("percentualMassaMagra")) + "\n"
                string += "- Massa gorda inicial: " + str(avaliacao_fisica.get("massaGordaInicial")) + "\n"
                string += "- Massa gorda atual: " + str(avaliacao_fisica.get("massaGordaAtual")) + "\n"
                string += "- Evolução geral: " + str(avaliacao_fisica.get("evolucaoGeral")) + "\n"
                string += "- Massa magra inicial: " + str(avaliacao_fisica.get("massaMagraInicial")) + "\n"
                string += "- Massa magra atual: " + str(avaliacao_fisica.get("massaMagraAtual"))
                string += "- Nível de gordura corporal: " + str(avaliacao_fisica.get("nivelGorduraCorporal")) + "\n"
                string += "- Nível de gordura corporal inicial: " + str(avaliacao_fisica.get("nivelGorduraCorporalInicial")) + "\n"
                string += "- Nível de gordura corporal faltando: " + str(avaliacao_fisica.get("nivelGorduraCorporalFaltando")) + "\n"
                string += "- Grupos: " + "\n"
                for grupo in avaliacao_fisica.get("grupos"):
                    string += "- " + json.dumps(grupo) + "\n"
            
            if len(acessos) > 0:
                string += "Total de acessos: " + str(len(acessos)) + "\n"
                string += "Último acesso: " + str(acessos[0]) + "\n"
            
            if boletim_de_visita.get("questionarioCliente") != None:
                string += "Boletim de visita: " + "\n" + json.dumps(boletim_de_visita.get("questionarioCliente")) + "\n"
                string += "Perguntas: " + "\n" + json.dumps(boletim_de_visita.get("questionarioPergunta")) + "\n"
            return string
        except Exception as e:
            print(e)
            import traceback
            traceback.print_exc()
            return string + "Não foi possível processar algumas informações sobre o usuário"

    @staticmethod
    def process_message(data: object) -> tuple[dict, str]:
        """
        Essa função identifica o tipo de mídia enviado na mensagem
        """
        if data.get('text', None):
            return {'type': 'text'}, data.get('text')
        elif data.get('audio', None):
            content, n_seconds = proccess_audio(data.get('audio'))
            return {'type': 'audio', 'n_seconds': n_seconds}, content
        elif data.get('sticker', None):
            return {'type': 'sticker'}, data.get('sticker')
        elif data.get('image', None):
            return {'type': 'image'}, data.get('image')
        elif data.get('contact', None):
            return {'type': 'contact'}, data.get('contact')
        elif data.get('document', None):
            return {'type': 'contact'}, data.get('document')
        elif data.get('location', None):
            return {'type': 'location'}, data.get('location')
        elif data.get('video', None):
            return {'type': 'video'}, data.get('video')
        elif data.get('poll', None):
            return {'type': 'poll'}, data.get('poll')
        elif data.get('reaction', None):
            return {'type': 'reaction'} , data.get('reaction').get('value')
        elif data.get('notification', None):
            return {'type': 'notification'} , data.get('notification')
        else:
            return None, None

    @staticmethod
    def process_classes_data(data: object, periodo: str = "") -> str:
        """Processa os dados de aulas e retorna uma string formatada"""
        content = data.get('content', {})
        dia = content.get('dias', [])
        if len(dia) > 0:
            dia = dia[0]
        else:
            return ""
        itens = content.get('itens')
        string = f"# Aulas para o dia {dia} (Informe esse dia para o usuário):\n"
        if periodo:
            string += f"## Estas aulas são referentes a este período: {periodo}, " + \
                    "informe isto ao usuário, e não diga que não tem alguma aula sem antes consultar pra ele em outros períodos."
        if len(itens) == 0:
            return string + "Não há aulas disponíveis nesse dia, é preciso consultar outro dia ou outro período! **Não sugira outros horários antes de consultar novamente!**"
        string += (
            "## O código da aula é o código que o ASSISTENTE deverá usar para o agendamento da aula.\n"
            f"## (*ATENÇÃO, o código só irá funcionar para o dia {dia}, "
            "verifique se bate com o dia que você irá tentar agendar a aula.*)\n"
        )
        class_found = False
        for horario in itens:
            aulas = horario.get('dias', {}).get(dia, [])
            if not aulas:
                continue
            string += f"### Essas são as aulas disponíveis ás **{horario.get('horario')}**" + ":\n"
            for aula in aulas:
                if isinstance(aula, dict) and aula.get('codigo', None):
                    string += "#### Código da aula: " + str(aula.get('codigo')) + "\n"
                    string += "- Turma: " + str(aula.get('turma', None)) + "\n"
                    capacidade = aula.get('capacidade') or 0
                    ocupacao = aula.get('ocupacao') or 0
                    string += "- Vagas disponíveis: " + str(capacidade - ocupacao) + "\n\n"
                elif isinstance(aula, str):
                    string += "\n\n\n #### Código da aula: " + str(aula) + "\n"

                class_found = True
            string += "_____________________________________________________\n"
        if not class_found:
            return ""

        return string

    @staticmethod
    def process_available_classes_data(data: object, age: int) -> str:
        """
        Retorna quais turmas estão disponíveis para a idade informada.
        """
        string = ""
        has_warning = False
        age = age or 0
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                # Posteriormente, consultar no sistema de novo
                data = []
        for aula in data:
            has_code = aula.get('codigo', None) is not None
            minimum_age = aula.get('idademinima', 0) <= age
            maximum_age = aula.get('idademaxima', 120) >= age
            if aula.get('idademaxima', 120) == 0:
                maximum_age = True
            is_age_0 = age == 0
            if not (has_code and minimum_age and maximum_age) and not is_age_0:
                string += f"Turma: {aula.get('descricao')}\n"
                has_warning = True
        if has_warning:
            string = "ATENÇÃO: as seguintes turmas **não estão disponíveis** devido à idade do aluno.\n" + string
        else:
            string += "Todas as turmas estão disponíveis.\n"
        string += "_____________________________________________________\n"
        return string

    @staticmethod
    def process_class_data(data: object, pretty: bool = False) -> str:
        """Processa os detalhes de uma aula"""
        class_details = data.get("content", {})

        class_schedule_id = class_details.get('horarioTurmaId', 'N/A')

        day_str = class_details.get('dia')
        if day_str:
            try:
                formatted_day = datetime.datetime.strptime(day_str, '%Y%m%d').strftime('%d/%m/%Y')
            except (ValueError, TypeError):
                formatted_day = "Data inválida"
        else:
            formatted_day = "N/A"

        capacity = class_details.get('capacidade', 0)
        enrolled_students = class_details.get('numeroAlunos', 0)
        available_slots = capacity - enrolled_students

        id_note = "" if pretty else " (Use essa informação apenas para agendar a aula. Não mostre ela ao usuário)."
        slots_note = "" if pretty else " (Não trave o agendamento se isso for 0)."

        formatted_details = (
            f"*ID do horário da aula:* {class_schedule_id}{id_note}.\n"
            f"*Nome da aula:* {class_details.get('nome', 'N/A')}\n"
            f"*Dia da aula:* {formatted_day}\n"
            f"*Horário da aula:* {class_details.get('periodo', 'N/A')}\n"
            f"*Quantidade de vagas:* {available_slots}{slots_note}.\n"
            f"*Modalidade da aula:* {class_details.get('modalidade', {}).get('nome', 'N/A')}\n"
            f"*Professor:* {class_details.get('professor', {}).get('nome', 'N/A')}\n"
            f"*Ambiente da aula:* {class_details.get('ambiente', {}).get('nome', 'N/A')}\n"
            f"*Nível da aula:* {class_details.get('nivel', {}).get('nome', 'N/A')}"
        )
        
        return formatted_details

    @staticmethod
    def process_static_classes_data(data: object, return_chunks: bool = False) -> str | list:
        """Processa os dados de aulas estáticas e retorna uma string formatada"""
        final_string = ""
        chunks = []
        if isinstance(data, str):
            data = json.loads(data)
        for aula in data:
            current_string = ""
            if aula.get('descricao'):
                current_string += f"Aula: {aula.get('descricao')}\n"
            if aula.get('mensagem') is not None:
                current_string += f"Mensagem: {aula.get('mensagem')}\n"
            if aula.get('modalidade'):
                current_string += f"Modalidade: {aula.get('modalidade')}\n"
            if aula.get('horarios'):
                current_string += f"Horários: {aula.get('horarios')}\n"
            if aula.get('dias'):
                current_string += f"Dias: {aula.get('dias')}\n"
            if aula.get('idademinima') is not None:
                current_string += f"Idade mínima: {aula.get('idademinima')}\n"
            if aula.get('idademaxima') is not None:
                current_string += f"Idade máxima: {aula.get('idademaxima')}\n"
            if aula.get('aulacoletiva') is not None:
                current_string += f"Aula coletiva: {aula.get('aulacoletiva')}\n"
            if aula.get('permitiraulaexperimental') is not None:
                current_string += (
                    f"Permite aula experimental: {aula.get('permitiraulaexperimental')}\n"
                )
            if aula.get('permitirdesmarcarreposicoes') is not None:
                current_string += (
                    f"Permite desmarcar reposições: {aula.get('permitirdesmarcarreposicoes')}\n"
                )
            current_string += "_____________________________________________________\n"
            final_string += current_string
            chunks.append(current_string)

        if return_chunks:
            return chunks
        return final_string

    @staticmethod
    def process_train_data(data: object) -> str:
        string = ""
        fichas = []
        if type(data) == str:
            data = json.loads(data)
        if data.get("name_training_plan") != None:
            string += "*Nome do plano de treino: " + str(data.get("name_training_plan")) + "*\n"
        if data.get("training_plan") != None:
            training_plan = data.get("training_plan")
            if training_plan.get("days_per_week") != None:
                string += "Dias por semana: " + str(training_plan.get("days_per_week")) + "\n"
            if training_plan.get("workouts") != None:
                workouts = training_plan.get("workouts")
                for workout in workouts:
                    ficha = ""
                    if workout.get("workout_name") != None:
                        ficha += f"*{str(workout.get('workout_name'))}*" + "\n"
                        string += f"*{str(workout.get('workout_name'))}*" + "\n"
                    if workout.get("type_of_training") != None:
                        ficha += "- Tipo de treino: " + str(workout.get("type_of_training")) + "\n"
                        string += "- Tipo de treino: " + str(workout.get("type_of_training")) + "\n"
                    if workout.get("duration") != None:
                        ficha += "- Duração: " + str(workout.get("duration")) + "\n"
                        string += "- Duração: " + str(workout.get("duration")) + "\n"
                    if workout.get("activities") != None:
                        activities = workout.get("activities")
                        for activity in activities:
                            if activity.get("exercise_name") != None:
                                ficha += "- Nome do exercício: *" + str(activity.get("exercise_name")).title() + "*\n"
                                string += "- Nome do exercício: *" + str(activity.get("exercise_name")).title() + "*\n"
                            if activity.get("sets") != None:
                                ficha += "- Número de séries: " + str(activity.get("sets")) + "\n"
                                string += "- Número de séries: " + str(activity.get("sets")) + "\n"
                            if activity.get("repetitions") != None:
                                ficha += f"- Repetições:" + "\n"
                                string += f"- Repetições:" + "\n"
                                ficha += f"\t- Mínimo: {activity.get('repetitions').get('min')} repetições" + "\n"
                                string += f"\t- Mínimo: {activity.get('repetitions').get('min')} repetições" + "\n"
                                ficha += f"\t- Máximo: {activity.get('repetitions').get('max')} repetições" + "\n"
                                string += f"\t- Máximo: {activity.get('repetitions').get('max')} repetições" + "\n"
                            if activity.get("rest_interval") != None:
                                ficha += "\t- Intervalo de descanso: " + str(activity.get("rest_interval")) + " segundos\n"
                                string += "\t- Intervalo de descanso: " + str(activity.get("rest_interval")) + " segundos\n"
                            if activity.get("bodyPart") != None:
                                ficha += "- Parte do corpo: *" + str(activity.get("bodyPart")) + "*\n"
                                string += "- Parte do corpo: *" + str(activity.get("bodyPart")) + "*\n"
                            if activity.get("target") != None:
                                ficha += "- Alvo: " + str(activity.get("target")) + "\n"
                                string += "- Alvo: " + str(activity.get("target")) + "\n"
                            if activity.get("instructions") != None:
                                ficha += "- Instruções: \n"
                                string += "- Instruções: \n"
                                for instruction in activity.get("instructions"):
                                    ficha += "\t- " + instruction + "\n"
                                    string += "\t- " + instruction + "\n"
                    fichas.append(ficha)
                    ficha = ""
        return string, fichas

    @staticmethod
    def process_products_data(data: object, return_chunks: bool = False) -> str:
        string = ""
        final_string = ""
        chunks = []
        if isinstance(data, str):
            data = json.loads(data)
        if not data:
            if return_chunks:
                return chunks
            return "Não há produtos disponíveis."
        for product in data:
            string = ""
            string += f"Produto: {product.get('descricao')}\n"
            string += f"Preço: {product.get('valorfinal')}\n"
            if product.get('urlVendaOnline') is not None:
                string += f"Link para compra: R${product.get('urlVendaOnline')}\n"
            string += f"_____________________________________________________\n"
            final_string += string
            chunks.append(string)
        if return_chunks:
            return chunks
        return final_string

    @staticmethod
    def preprocess_text(text: str) -> str:
        # Substitui links no formato [Texto](URL) pelo URL seguido do restante do texto
        text = re.sub(r'\[.*?\]\((http[s]?://[^\s]+)\)', r'\1', text)

        # Remove colchetes remanescentes
        text = text.replace("[", "").replace("]", "")

        # Substitui negrito em Markdown por um asterisco
        text = text.replace("**", "*")

        # Remove hashtags (substitui por espaço)
        text = text.replace("##", " ")

        return text.strip()

    @staticmethod
    def format_dados_bi(
        data: DataFrame,
        type_: str = Literal[
            "total_atendimentos",
            "frequencia_atendimento",
            "tempo_medio_atendimento",
        ]
    ) -> dict:
        """
        Formata os dados de BI para o formato desejado.
        """
        try:
            match type_:
                case "total_atendimentos":
                    # Processando dados de total de atendimentos
                    total_atendimentos = int(data['total_atendimentos'].sum(
                    )) if 'total_atendimentos' in data else 0

                    # Calculando contatos novos e frequentes
                    if 'data_primeira_mensagem' in data and len(data) > 0:
                        data_min = data['data_primeira_mensagem'].min()
                        data_max = data['data_primeira_mensagem'].max()
                        delta = (data_max - data_min).days

                        # Considerando contatos novos os que têm primeira mensagem nos últimos 7 dias
                        limite_novos = data_max - Timedelta(days=min(delta, 7))
                        contatos_novos = data[data['data_primeira_mensagem']
                                              >= limite_novos]
                        porcentagem_contatos_novos = (
                            len(contatos_novos) / len(data)) * 100
                        contatos_frequentes = len(data) - len(contatos_novos)
                    else:
                        porcentagem_contatos_novos = 0
                        contatos_frequentes = 0

                    return {
                        "total_atendimentos": total_atendimentos,
                        "total_contatos": len(data),
                        "porcentagem_contatos_novos": porcentagem_contatos_novos,
                        "contatos_frequentes": contatos_frequentes,
                    }

                case "frequencia_atendimento":
                    # Processando dados de frequência de atendimento
                    result = {"frequencia_atendimento": {}}

                    # Se disponível no dataframe, adicionar dados de dia da semana
                    if 'dia_semana_nome' in data and len(data) > 0:
                        freq_por_dia = data.groupby('dia_semana_nome')[
                            'frequencia_atendimento'].sum()
                        result["frequencia_atendimento"]["dia_maior_frequencia"] = freq_por_dia.idxmax()
                        result["frequencia_atendimento"]["dia_menor_frequencia"] = freq_por_dia.idxmin()

                    # Processando dados por hora
                    if 'hora_envio' in data and len(data) > 0:
                        horarios = {}
                        for hora in range(7, 23):  # De 7h até 22h
                            count = data[data['hora_envio'] == hora]['mensagens_por_hora'].sum()
                            horarios[f"{hora}h"] = int(count)
                        result["frequencia_atendimento"]["horarios"] = horarios

                    return result

                case "tempo_medio_atendimento":
                    # Processando dados de tempo médio de atendimento
                    result = {"tempo_medio_atendimento": {}}

                    if 'duracao_conversa' in data and len(data) > 0:
                        tempo_medio_atendimento = data['duracao_conversa'].mean()
                        result["tempo_medio_atendimento"]["tempo_medio_atendimento"] = format_seconds_to_readable(
                            tempo_medio_atendimento)

                    if 'mensagens_trocadas' in data and len(data) > 0:
                        mensagens_trocadas = data['mensagens_trocadas'].sum()
                        if mensagens_trocadas == 0:
                            tempo_medio_atendimento = 0
                        else:
                            tempo_medio_atendimento = data['duracao_conversa'].sum() / (mensagens_trocadas / 2)
                        result["tempo_medio_atendimento"]["tempo_medio_resposta"] = format_seconds_to_readable(
                            tempo_medio_atendimento)

                    return result

        except Exception as e:
            import logging
            logging.error(f"Erro ao formatar dados de BI: {e}")
            return {}

    @staticmethod
    def filter_available_classes(raw_classes_data: dict, user_age: int, user_situation: str) -> dict:
        """
        Filtra as aulas disponíveis baseado na idade do usuário, situação e outros critérios.

        Args:
            raw_classes_data: Dados brutos retornados pela API de aulas
            user_age: Idade do usuário em anos
            user_situation: Situação do usuário (VI para visitante, AT para ativo, etc.)

        Returns:
            dict: Dados filtrados contendo apenas aulas/turmas que atendem aos critérios
        """
        try:
            content = raw_classes_data.get('content', {})
            itens = content.get('itens', [])
            dias = content.get("dias", [])

            if len(dias) <= 0 or not itens:
                return raw_classes_data

            dia = dias[0]

            # Obter data/hora atual
            now = dt.now()

            filtered_itens = []

            for item in itens:
                filtered_classes = []
                for aula in item.get("dias", {}).get(dia, []):
                    # Verificar se a aula já iniciou ou iniciará nos próximos 30 minutos
                    if not DataProcessor._is_class_available_by_time(aula, now):
                        continue

                    # Verificar filtros de idade
                    if not DataProcessor._is_age_valid(aula, user_age):
                        continue

                    # Verificar filtros específicos para visitantes
                    if user_situation != "AT" and not DataProcessor._is_visitor_allowed(aula):
                        continue

                    # Verificar limite de vagas
                    if not DataProcessor._has_available_spots(aula):
                        continue

                    filtered_classes.append(aula)

                item = item.copy()
                item["dias"][dia] = filtered_classes

                filtered_itens.append(item)

            # Atualizar o conteúdo com os itens filtrados
            filtered_data = raw_classes_data.copy()
            filtered_data['content']['itens'] = filtered_itens

            return filtered_data

        except Exception as e:
            logger.error(f"Erro ao filtrar aulas disponíveis: {e}")
            return raw_classes_data

    @staticmethod
    def _is_class_available_by_time(item: dict, current_time: dt) -> bool:
        """Verifica se a aula não iniciou e não iniciará nos próximos 30 minutos"""
        try:
            dia_mes = item.get('diaMes', '')
            inicio = item.get('inicio', '')

            if not dia_mes or not inicio:
                return True

            # Converter diaMes (formato YYYYMMDD) e inicio (formato HH:MM) para datetime
            year = int(dia_mes[:4])
            month = int(dia_mes[4:6])
            day = int(dia_mes[6:8])

            hour, minute = map(int, inicio.split(':'))

            class_datetime = dt(year, month, day, hour, minute)

            # Verificar se a aula já passou ou iniciará nos próximos 30 minutos
            time_diff = class_datetime - current_time

            return time_diff > timedelta(minutes=30)

        except Exception as e:
            logger.error(f"Erro ao verificar horário da aula: {e}")
            return False

    @staticmethod
    def _is_age_valid(item: dict, user_age: int) -> bool:
        """Verifica se a idade do usuário está dentro dos limites da aula/turma"""
        try:
            idade_minima = item.get('idadeMinima', 0)
            idade_maxima = item.get('idadeMaxima', 999)

            # Se idade máxima for 0, considera como sem limite
            if not idade_maxima:
                idade_maxima = 999

            return idade_minima <= user_age <= idade_maxima

        except Exception as e:
            logger.error(f"Erro ao verificar idade: {e}")
            return False

    @staticmethod
    def _is_visitor_allowed(item: dict) -> bool:
        """Verifica se visitantes são permitidos na aula/turma"""
        try:
            permite_aula_experimental = item.get('permiteAulaExperimental', True)
            return permite_aula_experimental

        except Exception as e:
            logger.error(f"Erro ao verificar permissão para visitante: {e}")
            return False

    @staticmethod
    def _has_available_spots(item: dict) -> bool:
        """Verifica se há vagas disponíveis na aula/turma"""
        try:
            ocupacao = item.get('ocupacao', 0)
            capacidade = item.get('capacidade', 0)

            # Se capacidade for 0, considera como sem limite
            if capacidade == 0:
                return True

            return ocupacao < capacidade

        except Exception as e:
            logger.error(f"Erro ao verificar vagas disponíveis: {e}")
            return False
