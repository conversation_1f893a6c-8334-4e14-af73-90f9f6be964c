-- SQL para frequencia_atendimento
-- Tabela: {dataset}.conversas
-- Frequência de atendimento por dia da semana com base nos id_conversa
select
    extract(DAYOFWEEK from data_envio) as dia_semana,
    case
        when extract(DAYOFWEEK from data_envio) = 1 then 'Domingo'
        when extract(DAYOFWEEK from data_envio) = 2 then 'Segunda-feira'
        when extract(DAYOFWEEK from data_envio) = 3 then 'Terça-feira'
        when extract(DAYOFWEEK from data_envio) = 4 then 'Quarta-feira'
        when extract(DAYOFWEEK from data_envio) = 5 then 'Quinta-feira'
        when extract(DAYOFWEEK from data_envio) = 6 then 'Sexta-feira'
        when extract(DAYOFWEEK from data_envio) = 7 then 'Sábado'
    end as dia_semana_nome,
    count(distinct id_conversa) as frequencia_atendimento,
    extract(HOUR from data_envio) as hora_envio,
    count(*) as mensagens_por_hora
from
    {dataset}.conversas
where id_empresa = '{id_empresa}'
    and data_envio >= TIMESTAMP '{data_inicio} 00:00:00 UTC'
    and data_envio <= TIMESTAMP '{data_fim} 23:59:59 UTC'
group by
    dia_semana, dia_semana_nome, hora_envio
order by
    dia_semana, hora_envio
