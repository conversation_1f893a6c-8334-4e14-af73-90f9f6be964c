-- SQL para total_atendimentos
-- Tabela: {dataset}.conversas
-- Total de atendimentos por pessoa
SELECT 
    id_empresa,
    telefone,
    count(distinct id_conversa) as total_atendimentos,
    min(data_envio) as data_primeira_mensagem
from `{dataset}.conversas`
where id_empresa = '{id_empresa}'
    and data_envio >= TIMESTAMP '{data_inicio} 00:00:00 UTC'
    and data_envio <= TIMESTAMP '{data_fim} 23:59:59 UTC'
    and telefone not like '%utility%'
group by id_empresa, telefone
