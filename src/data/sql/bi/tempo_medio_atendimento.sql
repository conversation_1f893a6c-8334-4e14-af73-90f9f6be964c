-- SQL para tempo_medio_atendimento
-- Tabela: {dataset}.conversas
-- Tempo médio de atendimento por data de inicio de conversa
WITH
  duracao AS (
  SELECT
    id_conversa,
    id_empresa,
    MIN(data_envio) AS data_inicio_conversa,
    MAX(data_envio) AS data_fim_conversa,
    LEAST(TIMESTAMP_DIFF(MAX(data_envio), MIN(data_envio), SECOND), 4*60*60) AS duracao_conversa,
    COUNT(DISTINCT id_mensagem) AS mensagens_trocadas
  FROM
    `{dataset}.conversas`
  WHERE
    telefone NOT LIKE '%utility%'
  GROUP BY
    1,
    2)

SELECT
  id_empresa,
  data_fim_conversa,
  data_inicio_conversa,
  COALESCE(duracao_conversa, 0) AS duracao_conversa,
  COALESCE(mensagens_trocadas, 0) AS mensagens_trocadas
FROM
  duracao
where id_empresa = '{id_empresa}'
    and data_fim_conversa >= TIMESTAMP '{data_inicio} 00:00:00 UTC'
    and data_fim_conversa <= TIMESTAMP '{data_fim} 23:59:59 UTC'
ORDER BY
  data_fim_conversa DESC
