import requests
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin

class Scraper:
    def __init__(self, url, recursive):
        self.url = url
        self.recursive = recursive
        self.response = None
        self.texts = []
        self.text = ""
        self.links = []

    def extract_page_texts(self, response):
        """
        Extrai os textos do conteúdo principal de uma página da web.

        Args:
            url (str): URL da página da qual se deseja extrair o conteúdo.

        Returns:
            list: Uma lista de tuplas contendo o nome da tag HTML e o texto extraído.
        """

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')

            for script_or_style in soup(["script", "style", "noscript"]):
                script_or_style.extract()

            main_content = soup.find('main') or soup.find('body') or soup.find('div')

            if main_content:
                extracted_texts = []
                for element in main_content.descendants:
                    if element.name is not None and element.string is not None:
                        text = element.string.strip()
                        if text and text not in extracted_texts and text not in self.text:
                            extracted_texts.append(text)
                return extracted_texts
            else:
                #print("Conteúdo principal não encontrado.")
                return []
        else:
            #print(f"Erro ao acessar o site: {response.status_code}")
            return []


    def extract_internal_links(self, response, url):
        """
        Extrai os links internos de uma página da web.

        Args:
            url (str): URL da página da qual se deseja extrair os links.

        Returns:
            list: Uma lista de tuplas contendo o href completo do link e o texto do link.
        """

        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            extracted_links = []

            links = soup.find_all('a', href=True)
            for link in links:
                href = link['href']
                full_url = urljoin(url, href)  # Constrói a URL completa
                if full_url.startswith(url):
                    link_text = link.get_text(strip=True)
                    if not full_url == url:
                      extracted_links.append((full_url, link_text))

            return extracted_links
        else:
            #print(f"Erro ao acessar o site: {response.status_code}")
            return []


    def scrape(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.response = requests.get(self.url, headers=headers)
        self.texts = self.extract_page_texts(self.response)
        self.links = self.extract_internal_links(self.response, self.url)

        if self.recursive:
            for link, text in self.links:
                scraper = Scraper(link, False)
                scraper.scrape()
                for text in scraper.texts:
                  if text not in self.texts:
                    self.texts.append(text)
                del scraper
                time.sleep(0.0035)

        self.text = " ".join(self.texts)
