"""Módulo para agendar notificações ao Google Chat."""
import os
import logging
from apscheduler.schedulers.background import BackgroundScheduler

from src.monitoring.chats_integration.send_notification import send_notification

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

def run():
    """Worker para notificar queries problemáticas."""
    if os.getenv("BIGQUERY_NOTIFICATION") != "true":
        logger.info("Notificação de queries problemáticas desabilitada...")
        return

    scheduler = BackgroundScheduler()
    notification_time = os.getenv("BIGQUERY_NOTIFICATION_TIME", "7:30")

    scheduler.add_job(
        func=send_notification,
        trigger="cron",
        hour=notification_time.split(":")[0],
        minute=notification_time.split(":")[1],
        id="bigquery_notification",
        name="Notificação de queries problemáticas",
        replace_existing=True,
    )

    scheduler.start()

    try:
        scheduler._thread.join() # pylint: disable=protected-access

    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
