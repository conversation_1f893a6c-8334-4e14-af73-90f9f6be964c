import sys
from src.worker.entrypoint import connections
from pydub import AudioSegment
import io
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

def convert_to_audio(text, voice, mimetype='mp3'):
    """
    Get the transcription of the audio file.
    """
    
    try:
        response = connections.openai_client.audio.speech.create(
            model="tts-1",
            voice=voice,
            input=text,
            response_format=mimetype
        )

        content = list(response.iter_bytes())[0]
        
        try:
            audio = AudioSegment.from_file(io.BytesIO(content), format=mimetype)
            duration = audio.duration_seconds
        except Exception as error:
            logger.error(f'convert_to_audio Error: {error}')
            duration = None
            
        logger.info(f'CONVERT TO AUDIO:\n\tduração: {duration}')
        return content, duration # retorno está em binário
    except Exception as error:
        print(error)
        sys.exit(-1) 
