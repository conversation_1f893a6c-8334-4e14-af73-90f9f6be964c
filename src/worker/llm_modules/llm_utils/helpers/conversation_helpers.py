"""Módulo para auxiliar na manipulação de conversas."""
from src.extras.util import analyze_conversation_success
from src.extras.util import WorkersTracer

@WorkersTracer(
    span_name_prefix=f"{__name__}.end_conversation",
    span_description="<PERSON>lisando o fim da conversa",
    span_attributes={
        "telefone": "str",
        "objetivo": "str",
        "id_conversa": "str",
        "description": "description"
    }
)
def end_conversation(
        telefone: str,
        objetivo: str,
        bq,
        id_conversa: str
        ) -> None:
    """
    Utilitário para analisar o fim de uma conversa.
    """

    success, description = analyze_conversation_success(
        telefone,
        objetivo,
        bq,
        id_conversa
        )

    return success, description
