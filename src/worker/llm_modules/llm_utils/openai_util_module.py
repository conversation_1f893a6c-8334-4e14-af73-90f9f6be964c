# Esse módulo é responsável por utilizar LLM para utilidades específicas
# Como executar funções específicas, como salvar informações no banco de dados, agendar ligações, etc.

import os
from multiprocessing import Process
import logging
from unittest.mock import MagicMock
from typing import Type, TypeVar
from pydantic import BaseModel
import instructor

from src.extras.config import Config
from src.worker.entrypoint import connections
from src.extras.util import WorkersTracer

OPENAI_MODEL = os.environ.get("OPENAI_MODEL", "gpt-4o-mini")
logger = logging.getLogger("conversas_logger")
T = TypeVar('T', bound=BaseModel)

class OpenAIUtilModule:
   """
   Classe responsável por realizar a comunicação com a API do OpenAI e permitir a execução de ações diversas.

   Args:
      bq (BigQueryData): Objeto BigQueryData para salvar informações no banco de dados.
   """
   def __init__(self, bq) -> None:
      # Conexão com o BigQuery e OpenAI
      self.bq = bq
      self.client = instructor.from_openai(connections.openai_client)
      self.raw_client = connections.openai_client

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_messages",
      span_description="Salvando mensagem no banco de dados",
   )
   def save_messages(self, prompt, result, prompt_tokens=0, completion_tokens=0):
      logger.info("Prompt: %s", prompt)
      logger.info("Resultado: %s", result)
      for key, value in result.items():
         if hasattr(value, "model_dump"):
            result[key] = value.model_dump()
         elif hasattr(value, "value"):
            result[key] = value.value
         elif not isinstance(value, (str, int, float, bool)):
            result[key] = f"{value}"
      self.bq.save_message("assistant", result, "utility", prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)
      self.bq.save_message("user", prompt, "utility", prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)

   @WorkersTracer(
      span_name_prefix=f"{__name__}.save_in_parallel",
      span_description="Salvando em paralelo mensagem no banco de dados",
   )
   def save_in_parallel(self, prompt, result, prompt_tokens=0, completion_tokens=0):
      save_message_process = Process(target=self.save_messages, args=(prompt, result, prompt_tokens, completion_tokens))
      save_message_process.start()

   @WorkersTracer(
      span_name_prefix=f"{__name__}.get_response",
      span_description="Obtendo resposta do OpenAI",
   )
   def get_response(self, system: str, prompt: str, schema: Type[T] = None, dump: bool = True) -> T:
      """
      Função responsável por obter a resposta do OpenAI.

      :param system: Mensagem do sistema.
      :param prompt: Prompt de entrada.
      :param schema: Esquema de validação de entrada.
      """
      if Config().isLoadTesting():
            mock = MagicMock()
            mock.model_dump.return_value = {"message": "Teste de carga"}
            return mock

      logger.info("Obtendo resposta do OpenAI...")

      messages = [{"role": "system", "content": system}, {"role": "user", "content": prompt}]

      response, completion = self.client.chat.completions.create_with_completion(
         model=OPENAI_MODEL,
         messages=messages,
         max_tokens=1200,
         stop=None,
         temperature=0.5,
         response_model=schema,
      )

      usage = completion.usage
      prompt_tokens_ = usage.prompt_tokens
      completion_tokens_ = usage.completion_tokens
      
      if dump:
         result = response.model_dump()
         self.save_in_parallel(prompt, result, prompt_tokens_, completion_tokens_)
      else:
         result = response
         self.save_in_parallel(prompt, result.model_dump(), prompt_tokens_, completion_tokens_)

      return result

   def get_raw_response(
        self, system: str, prompt: str
    ) -> str:
      """
      Função responsável por obter a resposta do OpenAI.

      :param system: Mensagem do sistema.
      :param prompt: Prompt de entrada.
      """
      if Config().isLoadTesting():
            mock = MagicMock()
            mock.model_dump.return_value = {"message": "Teste de carga"}
            return mock

      logger.info("Obtendo resposta do OpenAI...")

      messages = [{"role": "system", "content": system}, {"role": "user", "content": prompt}]

      response = self.raw_client.chat.completions.create(
         model=OPENAI_MODEL,
         messages=messages,
         max_tokens=1200,
         stop=None,
         temperature=0.5,
      )

      usage_tokens = response.usage
      prompt_tokens_ = usage_tokens.prompt_tokens
      completion_tokens_ = usage_tokens.completion_tokens

      result = response.choices[0].message.content
      
      self.save_in_parallel(
            prompt,
            {"result": result},
            prompt_tokens_,
            completion_tokens_
        )

      return result

   @WorkersTracer(
      span_name_prefix=f"{__name__}.__del__ - (Destrutor)",
      span_description="OpenAIResponseModule finalizado",
   )
   def __del__(self):
      logger.info("OpenAIResponseModule finalizado.")
      pass
