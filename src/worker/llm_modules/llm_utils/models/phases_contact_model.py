"""Módulo para os modelos que definem a estratégia de contato pós fase"""

from typing import List

from pydantic import BaseModel, Field


class Strategy(BaseModel):
    days_after: int = Field(
        ..., description="A quantidade de dias depois do contato que deverá ser feito o envio"
    )
    instruction: str = Field(
        ..., description="Instruções sobre como deve ser estruturada a mensagem"
    )


class PhaseScheduleStrategy(BaseModel):
    strategy: List[Strategy] = Field(
        ..., description="A estratégia de envios criada com base na solicitação"
    )
