"""Modelo de retorno para sucesso de conversa."""
from enum import Enum
from pydantic import BaseModel, Field


class ConversationSuccessType(str, Enum):
    """
    Enum responsável por definir os tipos de sucesso de conversa.
    """
    SUCCESS = "success"
    NO_SUCCESS = "no_success"

class ConversationSuccess(BaseModel):
    """
    Classe responsável por validar os dados de sucesso de conversa.
    """
    type: ConversationSuccessType = Field(..., description="Tipo de sucesso de conversa")
    description: str = Field(..., description="Explicações sobre o sucesso de conversa")
