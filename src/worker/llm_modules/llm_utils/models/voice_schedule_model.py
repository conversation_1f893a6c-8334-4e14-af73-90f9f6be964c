from pydantic import BaseModel, Field
from enum import Enum
from typing import List

class VoiceScheduleTypeExamples:
    """
    Classe responsável por definir os exemplos de agendamento de voz.
    """
    hourly = {
            "08:00": "feminino",
            "18:00": "masculino"
            }
    weekly = {
            "Monday": "feminino",
            "Tuesday": "feminino",
            "Wednesday": "masculino",
            "Thursday": "masculino",
            "Friday": "masculino",
            "Saturday": "feminino",
            "Sunday": "feminino"
            }
    none = {"voice": "feminino"}


class VoiceScheduleType(str, Enum):
    """
    Enum responsável por definir os tipos de agendamento de voz.
    """
    hourly = "hourly"
    weekly = "weekly"
    none = "none"

class Voices(str, Enum):
    """
    Enum responsável por definir as vozes disponíveis.
    """
    feminino = "feminino"
    masculino = "masculino"

class WeeklyDays(str, Enum):
    """
    Enum responsável por definir os dias da semana.
    """
    Monday = "Monday"
    Tuesday = "Tuesday"
    Wednesday = "Wednesday"
    Thursday = "Thursday"
    Friday = "Friday"
    Saturday = "Saturday"
    Sunday = "Sunday"

class Schedule(BaseModel):
    """
    Classe responsável por validar os dados de agendamento de voz.
    """
    type: VoiceScheduleType = Field(..., description="Tipo de agendamento de voz")

class HourlyScheduleData(BaseModel):
    """
    Classe reponsável por validar os dados completos de horário de voz diário.
    """
    class HourlySchedule(BaseModel):
        """
        Classe responsável por validar os dados de horário de voz diário.
        """
        hour: str = Field(..., description="Hora do agendamento")
        voice: Voices = Field(..., description="Voz do agendamento")

    data: List[HourlySchedule] = Field(..., description="Lista de agendamentos diários")

class WeeklyScheduleData(BaseModel):
    """
    Classe reponsável por validar os dados completos de horário de voz semanal.
    """
    class WeeklySchedule(BaseModel):
        """
        Classe responsável por validar os dados de horário de voz semanal.
        """
        day: WeeklyDays = Field(..., description="Dia da semana")
        voice: Voices = Field(..., description="Voz do agendamento")
    data: List[WeeklySchedule] = Field(..., description="Lista de agendamentos semanais")

class NoneScheduleData(BaseModel):
    """
    Classe responsável por validar os dados de horário de voz padrão.
    """
    data: Voices = Field(..., description="Voz do agendamento")

class VoiceSchedule:
    """
    Classe responsável por permitir acesso aos modelos de agendamento de voz.
    """
    hourly = HourlyScheduleData
    weekly = WeeklyScheduleData
    none = NoneScheduleData