from pydantic import BaseModel, Field
from enum import Enum
from typing import List



from pydantic import BaseModel, Field
from enum import Enum
from typing import List, Optional


class Unit(str, Enum):
    """
    Enum para os tipos de notificação.
    """
    hours = "hours"
    minutes = "minutes"
    seconds = "seconds"       

class Time(BaseModel):
    time: str = Field(..., description="Horário de envio (formato HH:MM)")
    message_instructions: str = Field(..., description="Instruções da mensagem a ser enviada!")

class TimeBefore(BaseModel):
    unit: Unit = Field(..., description="Unidade de tempo hora, minuto ou segundo")
    time: int = Field(..., description="Tempo antes do evento agendado")
    message_instructions: str = Field(..., description="Instruções da mensagem a ser enviada!")

class TimeAfter(BaseModel):
    unit: Unit = Field(..., description="Unidade de tempo hora, minuto ou segundo")
    time: int = Field(..., description="Tempo depois do evento agendado")
    message_instructions: str = Field(..., description="Instruções da mensagem a ser enviada!")


class SameDayNotification(BaseModel):
    """
    Configuração para notificações no dia do ato do agendamento de futuro evento.
    """
    notifications_after: List[TimeAfter] = Field(..., description="Lista de notificações logo depois do ato de agendamento do futuro evento")

class EventDayNotification(BaseModel):
    """
    Configuração para notificações no dia do evento acontecer.
    """
    notifications_at: List[Time] = Field(None, description="Lista de notificações em um horário específico no dia do evento")
    notifications_before: List[TimeBefore] = Field(None, description="Lista de notificações antes do evento acontecer")
    notifications_after: List[TimeAfter] = Field(None, description="Lista de notificações depois do evento ter acontecido")

class IntermediateDaysNotification(BaseModel):
    """
    Configuração para notificações nos dias intermediários entre o dia do ato de agendar e do evento em si.
    """
    notifications_at: List[Time] = Field(..., description="Lista de notificações para os dias intermendiários entre o dia do ato de agendar e do evento em si")

class NotificationSchedule(BaseModel):
    """
    Esquema completo de agendamento de notificações.
    """
    same_day: Optional[SameDayNotification] = Field(None, description="Configuração para mesmo dia que o ato do agendamento foi realizado")
    event_day: Optional[EventDayNotification] = Field(None, description="Configuração para o dia que o evento foi acontecer")
    intermediate_days: Optional[IntermediateDaysNotification] = Field(None, description="Configuração para dias intermediários")