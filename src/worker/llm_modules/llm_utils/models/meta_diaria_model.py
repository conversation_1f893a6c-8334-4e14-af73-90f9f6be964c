from datetime import date, datetime
from enum import Enum
from typing import Optional
from pydantic import BaseModel, Field, model_validator


class ConclusaoMetaDiariaType(str, Enum):
    """
    Enum responsável por definir os tipos de Conclusão da Meta Diária.
    """
    agendamento = "agendamento"
    objecao = "objecao"
    simples_registro = "simples_registro"

class ClassificacaoMetaDiariaType(BaseModel):
    """
    Classe responsável determinar a conclusão das Metas Diárias
    """
    type: ConclusaoMetaDiariaType = Field(..., description="Tipo de Conclusão da Meta Diária")

class Objecao(BaseModel):
    """
    Modelo responsável por definir as objeções da meta diária.
    """
    @classmethod
    def set_types(cls, descriptions):
        """
        Método responsável por definir os tipos de objeções da meta diária.
        """
        def formatar_descs(desc: str):
            return desc.split(" - ")[0].lower().replace(" ", "_")
        valores_enum = {formatar_descs(desc): desc for desc in descriptions}
        
        cls.__annotations__["tipo"] = Enum("TipoObjecao", valores_enum)


class Agendamento(BaseModel):
    """
    Classe responsável por definir os tipos de agendamentos da meta diária.
    """
    tipo_agendamento: str = Field(..., description="Tipo de Agendamento")
    horario: str = Field(..., description="Horário do Agendamento")
    data: date = Field(..., description="Data do Agendamento")
    codigo_aula: Optional[str] = Field(None, description="Código da Aula agendada (se for o caso)")

    @classmethod
    def set_types(cls, descriptions: list):
        """
        Define os tipos de agendamentos da meta diária.
        """
        valores_enum = {desc: desc for desc in descriptions}

        cls.__annotations__["tipo"] = Enum("TipoAgendamento", valores_enum)


class SimplesRegistro(BaseModel):
    """
    Classe responsável por definir os tipos de simples registro da meta diária.
    """
    observacao: str = Field(..., description="Observação do Simples Registro")

class AcoesMetaDiaria:
    agendamento: Agendamento = Agendamento
    objecao: Objecao = Objecao
    simples_registro: SimplesRegistro = SimplesRegistro

