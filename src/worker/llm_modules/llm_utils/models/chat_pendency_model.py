from pydantic import BaseModel, Field
from enum import Enum

class ChatPendencyType(str, Enum):
    """
    Enum responsável por definir os tipos de pendência de chat.
    """
    has_pendency = "has_pendency"
    no_pendency = "no_pendency"

class ChatPendency(BaseModel):
    """
    Classe responsável por validar os dados de pendência de chat.
    """
    type: ChatPendencyType = Field(..., description="Tipo de pendência de chat")
    pendency: str = Field(..., description="Pendência de chat")