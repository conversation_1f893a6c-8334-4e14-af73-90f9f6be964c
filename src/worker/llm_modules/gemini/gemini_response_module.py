# Esse módulo é responsável por realizar a comunicação com a API do Gemini
# Ele recebera esse dados:
# Contexto do aluno
# Contexto da academia
# Informação dos planos do aluno com link de compra
# Mensagem inicial para ser enviada ao aluno
# A ideia é esse bot funcionar como um assistente de crm para a academia

from pandas import DataFrame, Timestamp
import json
from multiprocessing import Process
import logging
import requests as req
import google.generativeai as genai
import os

from src.extras.util import register_log, timestamp_formatado, WorkersTracer
from src.extras.config import Config
from src.worker.entrypoint import connections
from src.data.bigquery_data import BigQueryData as bq
from src.data.data_processor import DataProcessor as dp
from src.worker.messager_modules.whatsapp_messager.whatsapp_messager import WhatsappMessager
from src.worker.llm_modules.base_response import BaseResponse
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools as PIT

import pandas as pd
from requests.exceptions import ConnectTimeout

import unidecode

logger = logging.getLogger("conversas_logger")
STOP = "STOP"

keys_path = "src/connections/keys.json"
with open(keys_path) as f:
    keys = json.load(f)
    GEMINI_API_KEY = keys["GEMINI_API_KEY"]

# TODO: Trocar todas as funções relacionadas à Pacto para o módulo de Integrações
class GeminiResponseModule:
    """
    Classe responsável por realizar a comunicação com a API do Gemini e permitir a execução de ações de forma autônoma.

    Args:
        user_context (dict): Contexto do usuário.
        id_empresa (str): ID da empresa.
        phase (str): ID da fase.
        telefone (str): Número de telefone do usuário.
        first_message (bool): Indica se é a primeira mensagem da conversa. Default é False.
    """
    def __init__(self, user_context: dict, id_empresa: str, phase: str, telefone: str, first_message: bool = False, is_group: bool = False, id_matriz: str = None) -> None:
        # Conexão com o BigQuery e Gemini
        self.bq = bq(id_empresa=id_empresa)
        self.pit = PIT(id_empresa=id_empresa)
        # Dados específicos
        try:
            self.user_context = json.dumps(user_context)
        except:
            self.user_context = user_context
        self.gym_context = dp.process_gym_data(self.bq.get_gym_context())
        self.phase_id = phase
        phase = self.bq.get_phase_context(phase_name=phase)
        self.context = phase['descricao_fase']
        self.goal = phase['instrucao_ia_fase']
        self.telefone = telefone
        self.first_message = first_message
        self.id_empresa = id_empresa
        self.is_group = is_group
        self.id_matriz = id_matriz
        self.previous_messages = []
        genai.configure(api_key=GEMINI_API_KEY)
        self.model = genai.GenerativeModel(model_name = 'gemini-1.5-flash', system_instruction = self._init_system_content(), tools = self._init_functions())

    @WorkersTracer(
        span_name_prefix=f"{__name__}._init_system_content",
        span_description="Iniciando o system content para o Gemini model",
        span_attributes={
            "model family": "gemini",
            "personality": "personality",
        }
    )
    def _init_system_content(self):
        personality = self.bq.get_personality_context()
        vinculo_academia = f"O usuário está vinculado a academia {self.id_empresa}" if self.id_empresa is not None else "O usuário não está vinculado a nenhuma academia. Tente procurar com cpf, se não encontrar, sugira academias próximas."
        self.ground_rules = (
                f"Hoje é {timestamp_formatado(weekday=True)} e você sabe disso."
                f"Este é o contexto da conversa: {self.context}."
                f"Você tem esse objetivo específico com este cliente: {self.goal}."
                f"Contexto do usuário: {dp.process_user_data(json.loads(self.user_context))}. "
                f"Sintuação de vinculo do usuário: {vinculo_academia}"
                f"Essas são as informações da academia que você pode fornecer: {json.dumps(self.gym_context)}."
                "Nota: [Você entende e pode enviar mensagens de áudio.]"
                "Nota: [Seja claro e preciso em suas respostas, sempre baseando-se nas informações fornecidas.]"
                "Nota: [Você não faz cadastro de alunos em planos diretamente no sistema, apenas registra visitantes.]"
                "Nota: [Se você não souber a resposta, informe que não possui a informação.]"
                "Nota: [Lembre-se de não fornecer informações erradas.]"
                "Nota: [Fale com os pronomes adequados para o seu gênero.]"
                "Nota: [Você não sabe nada sobre a academia ou o usuário além do que é apresentado aqui.]"
                "Nota: [Você não pode falar sobre modalidades ou aulas que não estão no contexto fornecido.]"
                "Nota: [Você pode gerar treinos personalizados com a nossa IA *APENAS para alunos com plano ATIVO*.]"
                "Nota: [Você não tem permissão para falar sobre outros tópicos que não estejam relacionados à saúde, academia e bem-estar.]"
                "Nota: [Você não tem permissão para falar sobre política, religião ou qualquer outro tópico controverso.]"
                "Nota: [Se for solicitado algo que você não pode fazer ou não sabe, diga que não pode.]"
                "Nota: [Se perguntado sobre o horário de funcionamento da academia, não forneça informações que não estejam no contexto fornecido.]"
                "Nota: [Se perguntado sobre treinar em casa, avise que é sempre recomendado buscar ajuda profissional.]"
                "Nota: [Se necessário, use a função warn_user para alertar o usuário sobre comportamento inadequado.]"
        )
        if self.is_group:
            self.ground_rules += "Nota: [Você está em um grupo, então responda apenas a mensagens direcionadas para você.]"
        

        if not self.first_message:
            self.add_previous_message(self.telefone)
        
        return personality + self.ground_rules

    # TODO: Criar um classe em um arquivo separado para gerenciar as funções, ela vai ter um Gemini_response_module como atributo para poder acessar os dados
    # ^ Isso vai ser difícil...
    @WorkersTracer(
        span_name_prefix=f"{__name__}._init_functions",
        span_description="Iniciando as funções para o Gemini model",
        span_attributes={
            "model family": "gemini",
        }
    )
    def _init_functions(self):
        context = json.loads(self.user_context)
        if isinstance(context, list):
            context = context[0]
        elif isinstance(context, str):
            context = json.loads(context)
        
        situacao = context.get("aluno", {}).get("situacao", None)

        if situacao is None:
            situacao = "LEAD"
        elif isinstance(situacao, dict):
            situacao = situacao.get("codigo", "LEAD")

        self.function_descriptions = [
            self.warn_user,
            self.end_conversation
        ]
        
        if self.id_empresa is not None:
            self.function_descriptions.extend([
                self.get_additional_context,
                self.save_user_level,
                self.save_user_birthdate,
                self.check_classes_day,
                self.check_class_details,
                self.book_class,
                self.book_call
            ])

        if self.is_group:
            self.function_descriptions.append(self.dont_respond)

        if situacao == "LEAD":
            if self.id_matriz is not None: 
                # só pode sugerir academias se for uma rede e se tratar de um LEAD
                self.function_descriptions.extend([
                    self.search_by_cpf,
                    self.suggest_gyms_by_state_city_context,
                    self.save_user_empresa
                ])

            if self.id_empresa is not None:
                self.function_descriptions.extend([
                    self.save_user_name,
                    self.register_visitor,
                ])
        elif situacao == "VI":
            self.function_descriptions.append(self.search_by_cpf)
        elif situacao == "AT":
            self.function_descriptions.append(self.generate_train)
        
        return self.function_descriptions

    @WorkersTracer(
        span_name_prefix=f"{__name__}.register_visitor",
        span_description="Registrando visitante",
        span_attributes={
            "nome": "nome",
            "data_nascimento": "data_nascimento",
            "goal": "goal"
        }
    )
    def register_visitor(self, nome: str, data_nascimento: str = None, goal: str = "outro") -> str:
        """
        Esta função serve para registrar um visitante na academia. *O VISITANTE DEVE SER A PESSOA DONA DO TELEFONE*. 
        O usuário precisa estar vinculado a uma academia para usar esta função.

        :param str nome: O nome do visitante.
        :param str data_nascimento: A data de nascimento do visitante, neste formato: yyyy-mm-dd.
        :param str goal: O objetivo do cadastro de visitante, pode ser {book_class} -> para cadastro de aula, {book_call} -> para agendar uma ligação, ou {outro} -> se não houver objetivo definido.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Registrando visitante...")
        url = f"{self.pit.get_url('admMsUrl', id_empresa_)}/v1/cliente"
        token_auth = self.pit.get_token_auth(id_empresa_)
        req_body = {
            "nome": nome,
            "empresa": cod_empresa,
            "celular": str(self.telefone).replace("+55", ""),
            "dataNascimento": data_nascimento,
        }
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        response = req.post(url, json=req_body, headers=headers)
        register_log(url, req_body, headers, "POST", response, "register_visitor", self.id_empresa)
        if response.status_code == 200:
            contexto_aluno = json.loads(connections.redis_client.get(f"{self.telefone}-{self.id_empresa}"))
            contexto_aluno["aluno"]["situacao"] = "VI"
            contexto_aluno["aluno"]["matricula"] = response.json().get("content", {}).get("matricula", None)
            contexto_aluno["aluno"]["codigo"] = response.json().get("content", {}).get("codigo", None)
            connections.redis_client.set(
                f"{self.telefone}-{self.id_empresa}",
                json.dumps(contexto_aluno),
                ex=8*60*60
            )
            extra_message = None
            message = "Visitante registrado com sucesso!"
            match goal:
                case "book_class":
                    extra_message = " *A reserva de aula não foi realizada ainda!*"
                case "book_call":
                    extra_message = " *A ligação não foi agendada ainda!*"
            return message + extra_message if extra_message else message
        else:
            return "Erro ao registrar visitante."
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_additional_context",
        span_description="Obtendo contexto adicional",
        span_attributes={
            "plans": "addicional_context['plans']",
            "classes": "addicional_context['classes']",
            "products": "addicional_context['products']"
        }
    )
    def get_additional_context(self, context_type: list[str]) -> dict:
        """
        Esta função é a única fonte de informações sobre o contexto específico da academia, como Planos, Produtos e Turmas. 
        Sempre que houver necessidade de fornecer detalhes sobre os planos, produtos e turmas disponíveis, ou qualquer outra 
        informação relacionada, você **deve** utilizar essa função para obter dados atualizados.
        
        :param list context_type: Uma lista de tipos de contexto a serem recuperados do banco de dados. Deve ser uma lista de strings como ['planos', 'turmas', 'produtos'],
                                podendo ter um ou ambos. Se não tiver certeza sobre quais informações são necessárias, inclua ambas as opções.
        """
        additional_context = {}
        logger.info("Obtendo contexto adicional...")
        for type_ in context_type:
            if type_ == "planos":
                additional_context["plans"] = dp.process_plans_data(self.bq.get_plans_context())[0]
            if type_ == "turmas":
                additional_context["classes"] = self.bq.get_classes_context()
            if type_ == "produtos":
                additional_context["products"] = dp.process_products_data(self.bq.get_products_context())
                    
        return json.dumps(additional_context)

    @WorkersTracer(
        span_name_prefix=f"{__name__}.suggest_gyms_by_state_city_context",
        span_description="Sugerindo academias próximas ao usuário",
        span_attributes={
            "state": "state",
            "city": "city"
        }
    )
    def suggest_gyms_by_state_city_context(self, state: str, city: str) -> str:
        """
        Essa função deve ser usada para sugerir academias próximas ao usuário usando o estado e a cidade.

        :param str state: O estado que o usuário mora em sigla (ex: SP, GO, SC, etc...).
        :param str city: O cidade do usuário em que será usado para encontrar academias próximas.
        """  

        cache = connections.redis_client.get(f"CPF_search:{self.id_matriz}:{self.telefone}")
        logger.info(f"cache do CPF_search: {cache.decode('utf-8')}")
        if cache is None: 
            return "Ainda não foi feita uma busca por CPF, faça uma busca por CPF antes de sugerir academias."
        elif "Pergunte a cidade e estado do usuário" not in cache.decode('utf-8'):
            return "O usuário já está vinculado matriculado a uma academia"
        
        lista_empresas = bq(id_empresa=self.id_matriz).get_chain_context()
        if not lista_empresas:
            return "Desculpe, rede ainda não cadastrada."
    
        df_empresas = pd.json_normalize(lista_empresas)[["id_empresa", "nome_empresa", "cidade", "estado", "endereco"]]
        
        df_empresas["cidade"] = df_empresas["cidade"].apply(lambda x: unidecode.unidecode(x).lower())
        df_empresas["estado"] = df_empresas["estado"].apply(lambda x: unidecode.unidecode(x).lower())
        df_nearby = df_empresas.loc[
            (df_empresas["cidade"] == unidecode.unidecode(city).lower()) &
            (df_empresas["estado"] == unidecode.unidecode(state).lower())
        ]

        if df_nearby.empty:
            return "Não encontramos nenhuma empresa perto da sua região"
        
        nearby_empresas = df_nearby.to_dict(orient="records")
        #connections.redis_client.set(f"{city}_{state}_{self.id_matriz}", json.dumps(nearby_empresas, ensure_ascii=False, indent=2))
        repr_nearby_empresas = "\n\n".join([f"\tid_empresa:{empresa['id_empresa']}\t\nNome:{empresa['nome_empresa']}\n\tEndereço:{empresa['endereco']}" for empresa in nearby_empresas])
        return f"""
            Empresas em {city} - {state}:
                {repr_nearby_empresas}.
            Não mostre o id_empresa para o usuario, só nome e endereço. Depois que o usuário escolher, chame a função save_user_empresa com o id_empresa correspondente.
        """

    @WorkersTracer(
        span_name_prefix=f"{__name__}.search_by_cpf",
        span_description="Buscando usuário por CPF",
    )
    def search_by_cpf(self, cpf: str) -> str:
        """Esta função deve ser usada se o usuário informar que já é aluno da academia.
        É importante que você busque o usuário no banco de dados da academia para obter informações sobre ele.
        Ela deve receber uma string que representa o CPF do usuário.

        :param str cpf: O CPF do usuário que será buscado no banco de dados.      
        """

        logger.info("Buscando usuário por CPF...")
        logging.info(f"CPF: {cpf}")
        logging.info(f"ID da empresa: {self.id_empresa}")
        logging.info(f"ID da rede: {self.id_matriz}")
        user_context_cpf = json.loads(self.user_context).get("aluno", {}).get("pessoa", {}).get("cpf", None)
        if user_context_cpf == cpf:
                return "Já sei que você é aluno, não é necessário buscar por CPF."
        cache_id_empresa = self.id_empresa or self.id_matriz
        cache = connections.redis_client.get(
            f"CPF_search:{cache_id_empresa}:{self.telefone}")
        if cache:
            return str(cache)
        
        if self.id_empresa is None:
            logger.info("É uma rede")
            logger.info(f"ID da rede: {self.id_matriz}")
            
            bq_ = bq(id_empresa=self.id_matriz)
            redes_json = self.bq.get_chain_context(extra_query=f"OR id_empresa = '{self.id_matriz}'")
            lista_empresas = [rede.get("id_empresa") for rede in redes_json] if redes_json else None
            if not lista_empresas:
                lista_empresas = self.pit.list_rede(self.id_matriz)
            logger.info(f"Empresas encontradas: {len(lista_empresas)}")

            if len(lista_empresas) == 0:
                return "Nenhuma empresa encontrada na rede."

            for id_empresa_ in lista_empresas:
                logger.info(f"ID da empresa: {id_empresa_}")
                bq_ = bq(id_empresa=id_empresa_)
                pit = PIT(id_empresa=id_empresa_)
                try:
                    url, headers, params, response = pit.get_user_by_cpf(cpf)
                except ConnectTimeout as e:
                    logger.error(f"Erro na requisição: {e} na função get_user_by_cpf para a empresa {id_empresa_}.")
                    continue

                if response.status_code == 200:
                    self.bq = bq_
                    self.pit = pit
                    if response.json().get("content", []) != []:
                        cod_empresa = response.json().get("content", [])[0].get("empresa", {}).get("codigo", None)
                        new_id_empresa, *_ = id_empresa_.split("-") 
                        new_id_empresa += f"-{cod_empresa}"
                        logger.info(f"ID da empresa certa: {new_id_empresa}")
                        break
                    else:
                        new_id_empresa = self.id_empresa
            
        else:
            logger.info("É uma empresa específica.")
            logger.info(f"ID da empresa: {self.id_empresa}")
            url, headers, params, response = self.pit.get_user_by_cpf(cpf)
            new_id_empresa = self.id_empresa

        register_log(url, {}, headers, "GET", response, "search_by_cpf", new_id_empresa, params)
        
        try:
            user_id = response.json().get("content", [{}])[0].get("codigo", None)
        except:
            user_id = None
        
        if (response.status_code != 200 and (user_id is None)):
            msg = "Usuário não encontrado. Pergunte a cidade e estado do usuário para sugerir empresas com suggest_gyms_by_state_city_context"
            connections.redis_client.set(f"CPF_search:{cache_id_empresa}:{self.telefone}", msg, ex=5*24*60*60)
            return msg
        user_data = self.pit.get_user_by_id(user_id)
        if user_data == {}:
            msg = "Erro na busca do usuário. Pergunte a cidade e estado do usuário para sugerir empresas com suggest_gyms_by_state_city_context"
            connections.redis_client.set(f"CPF_search:{cache_id_empresa}:{self.telefone}", msg, ex=5*24*60*60)
            return msg
        else:
            msg = dp.process_user_data({"aluno": user_data})
            # TODO: Pensar melhor no tempo desse chache do CPF_search
            connections.redis_client.set(f"CPF_search:{cache_id_empresa}:{self.telefone}", msg, ex=5*24*60*60)
            task = {
                "type": "user_empresa",
                "id_empresa": self.id_empresa,
                "id_matriz": self.id_matriz,
                "data":
                {
                "telefone": self.telefone,
                "contexto": json.dumps(user_data),
                "fase": user_data.get("fase_crm", "LEADS_HOJE"),
                "new_empresa": new_id_empresa
                }
            }
            connections.redis_client.set(
                f"{self.telefone}-{self.id_empresa}",
                json.dumps(user_data),
                ex=8*60*60
            )
            connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            self.id_empresa = new_id_empresa
            return msg
    
    @WorkersTracer(
        span_name_prefix=f"save_user_name",
        span_description="Salvando nome do usuário",
        span_attributes={
            "user_name": "user_name"
        }
    )
    def save_user_name(self, user_name: str) -> str:
        """Esta função deve ser usada para salvar o nome completo do usuário no banco de dados.
        Ela deve receber uma string que representa o nome do usuário.
        
        :param str user_name: O nome completo do usuário que será salvo no banco de dados.
        """
        logger.info("Salvando nome do usuário...")
        contexto_aluno = json.loads(self.user_context)
        contexto_aluno["aluno"]["pessoa"]["nome"] = user_name
        contexto_aluno["aluno"]['fase_atual'] = "LEADS_HOJE"
        contexto_aluno['fase_atual'] = "LEADS_HOJE"
        task = {
                "type": "user",
                "id_empresa": self.id_empresa,
                "id_matriz": self.id_matriz if self.id_matriz is not None else self.id_empresa,
                "data":
                {
                    "telefone": self.telefone,
                    "contexto": json.dumps(contexto_aluno),
                    "fase": "LEADS_HOJE",
                    "origin_last_update": "save_user_name",
                }
            }
        connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
        connections.redis_client.set(
            f"{self.telefone}-{self.id_empresa}",
            json.dumps(contexto_aluno),
            ex=8*60*60
        )
        return user_name
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.save_user_empresa",
        span_description="Salvando empresa do usuário",
        span_attributes={
            "id_empresa_": "id_empresa_"
        }
    )
    def save_user_empresa(self, id_empresa_: str) -> str:
        """Esta função deve ser usada para vincular uma academia ao usuário no banco de dados.
        Ela deve receber uma string representando o id_empresa. 
        Importante: Chamar logo após a função suggest_gyms_by_state_city_context, com o id_empresa da opção escolhida pelo usuário.
        
        :param str id_empresa_: O id_empresa atrelado a empresa que o usuário escolheu.
        """
        logger.info("Salvando empresa do usuário...")

        cache = connections.redis_client.get(f"CPF_search:{self.id_matriz}:{self.telefone}")
        if cache is None:
            return "Ainda não foi feita uma busca por CPF, faça uma busca por CPF antes de sugerir academias."
        
        if self.phase_id in ["LEADS_HOJE", "LEADS"]:
            task = {
                "type": "user_empresa",
                "id_empresa": id_empresa_, #self.id_empresa,
                "id_matriz": self.id_matriz,
                "data":
                {
                    "telefone": self.telefone,
                    "contexto": self.user_context,
                    "fase": "LEADS_HOJE",
                    "new_empresa": id_empresa_
                }
                }
            connections.redis_client.lpush('task_queue_bd_contexts', json.dumps(task))
            self.id_empresa = id_empresa_
            return f"Aluno foi vinculado a academia {id_empresa_} com exito."
        return "O aluno não pode mudar de academia pelo conversa, pois já está matriculado em uma. Fale para ele entrar em contato com um atendente"

    @WorkersTracer(
        span_name_prefix=f"{__name__}.save_user_level",
        span_description="Salvando nível do usuário",
        span_attributes={
            "level_id": "level_id"
        }
    )
    def save_user_level(self, level_id: int):
        """
        Esta função serve para salvar o nível do aluno.

        :param int level_id: O código do nível do aluno, um inteiro que representa o nível.
        """
        contexto_aluno = connections.redis_client.get(f"{self.telefone}-{self.id_empresa}")
        if contexto_aluno:
            contexto_aluno = json.loads(contexto_aluno)
            matricula = contexto_aluno.get("aluno", {}).get("matricula", None)

        if matricula is None:
            return "Não foi possível salvar o nível do aluno pois ele não tem cadastro no sistema, cadastre ele primeiro."

        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Salvando nível do aluno...")
        url_micro_servicos = f"{self.pit.get_url('treinoUrl', id_empresa_)}/prest/psec/alunos/nivel/{matricula}"
        token_auth = self.pit.get_token_auth(id_empresa_)
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        body = {"nivelId":level_id}
        response = req.put(url_micro_servicos, headers=headers, json=body)
        register_log(url_micro_servicos, body, headers, "PUT", response, "save_user_level", self.id_empresa)
        try:
            return response.json()
        except:
            return response.text
        finally:
            return f"{response} {response.status_code}"
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.save_user_birthdate",
        span_description="Salvando data de nascimento do aluno",
    )
    def save_user_birthdate(self, data_nascimento: str) -> str:
        """
        Esta função serve para salvar a data de nascimento do aluno.

        :param str data_nascimento: A data de nascimento do aluno, neste formato: yyyy-mm-dd.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Salvando data de nascimento do aluno...")
        url = f"{self.pit.get_url('admMsUrl', id_empresa_)}/v1/cliente"
        token_auth = self.pit.get_token_auth(id_empresa_)      
        detalhes_aluno = json.loads(self.user_context)
        codigo = detalhes_aluno.get("aluno", {}).get("codigo", None)
        req_body = {
            "nome": detalhes_aluno.get("aluno", {}).get("pessoa", {}).get("nome", None),
            "empresa": cod_empresa,
            "celular": str(self.telefone).replace("+55", ""),
            "dataNascimento": data_nascimento,
        }
        if codigo is not None:
            req_body["codigo"] = codigo
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json"
        }
        response = req.post(url, json=req_body, headers=headers)
        register_log(url, req_body, headers, "POST", response, "save_user_birthdate", self.id_empresa)
        if response.status_code == 200:
            detalhes_aluno["aluno"]["pessoa"]["data_nascimento"] = data_nascimento
            if codigo is None:
                detalhes_aluno["aluno"]["codigo"] = response.json().get("content", {}).get("codigo", None)
                detalhes_aluno["aluno"]["matricula"] = response.json().get("content", {}).get("matricula", None)
                detalhes_aluno["aluno"]["situacao"] = "VI"
            connections.redis_client.set(
                f"{self.telefone}-{self.id_empresa}",
                json.dumps(detalhes_aluno),
                ex=8*60*60
            )
            return "Data de nascimento salva com sucesso!"
        else:
            return "Erro ao salvar data de nascimento."

    @WorkersTracer(
        span_name_prefix=f"{__name__}.end_conversation",
        span_description="Finalizando conversa",
    )
    def end_conversation(self) -> None:
        """
        Esta função deve ser executada se o usuário se despedir,
        ou se você identificar que sua missão foi cumprida.

        Se perceber que o usuário teve sua solicitação resolvida, 
        agradeça ao contato e finalize a conversa. Aqui vão algumas
        formas de identificar se o usuário está se despedindo:
            - mensagens como "só isso mesmo, obrigado"/"só isso mesmo, obrigado"
            - o usuário fala obrigado várias vezes, repetidamente
            - o usuario pode interagir/reagir sem que necessariamente ele precise de algo

        Obs: sempre verifique se o usuário teve sua solicitação devidamente atendida, 
        caso isso tenha sido validado corretamente e o usuário foi bem atendido,
        só se despeça uma única vez, a não ser que o usuário faça alguma pergunta.
        """
        logger.info("Finalizando conversa...")
        user_context = json.loads(self.user_context)
        first_name = user_context['aluno']['pessoa']['nome'].split()[0]
        last_name = user_context['aluno']['pessoa']['nome'].split()[1] if len(user_context['aluno']['pessoa']['nome'].split()) > 1 else "-"
        
        logger.info("Finalizando conversa...")
        messager = WhatsappMessager()
        messager.end_conversation(flow_name=Config.QUESTIONARY_FLOW_NAME,
                                    phone=self.telefone, first_name=first_name,
                                    last_name=last_name, id_empresa=self.id_empresa)
        return STOP
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.dont_respond",
        span_description="Não respondendo",
    )
    def dont_respond(self) -> str:
        """"Esta função deve ser chamada caso a mensagem não seja direcionada a você, ou caso o usuário esteja claramente se referindo a outra pessoa."""
        logger.info("Não respondendo...")
        return STOP
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.warn_user",
        span_description="Aviso ao usuário",
    )
    def warn_user(self) -> str:
        """
        Esta função serve para dar um aviso ao usuário, em qualquer sinal de que ele esteja falando coisas fora do contexto da conversa, ou mandando mensagens repetitivas.
        É importante que você dê um aviso ao usuário por meio desta função, para que ele saiba que está fazendo algo errado.
        """
        user_strikes = connections.redis_client.get(f"strikes:{self.telefone}-{self.id_empresa}")
        if user_strikes:
            user_strikes = json.loads(user_strikes).get("user_strikes", 0)
            user_strikes += 1
        else:
            user_strikes = 1
        connections.redis_client.set(f"strikes:{self.telefone}-{self.id_empresa}", json.dumps({"user_strikes": user_strikes}), ex=24*60*60)
        if user_strikes >= 3:
            return f"Usuário marcado com {user_strikes} strikes, estará bloqueado por 24 horas, mande o contato da academia para que ele solicite desbloqueio."
        
        return f"Usuário marcado com {user_strikes} strikes. Apenas 3 strikes são permitidos."
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.check_classes_day",
        span_description="Verificando aulas do dia",
        span_attributes={
            "dia": "dia",
            "mes": "mes",
            "periodo": "periodo",
            "id_empresa_": "id_empresa_",
        }
    )
    def check_classes_day(self, dia: int, mes: int, periodo: str) -> str:
        """
        Esta função deve ser usada para verificar as aulas disponíveis em um dia específico. Antes de reservar uma aula para um aluno, você deve verificar se a aula está disponível, por meio dessa função.

        :param int dia: O dia do mês que a aula ocorre.
        :param int mes: O mês que a aula ocorre.
        :param str periodo: O período do dia que a aula ocorre. Pode ser 'DIA'.
        """
        mes = str(mes).zfill(2)
        dia = str(dia).zfill(2)
        ano = Timestamp.now().year
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Verificando agenda de turmas...")
        url_micro_servicos = f"{self.pit.get_url('treinoUrl', id_empresa_)}/prest/psec/agenda-cards"
        token_auth = self.pit.get_token_auth(id_empresa_)
        ref = f"{ano}{mes}{dia}"
        periodo = "DIA"
        tipo = "AC"
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "empresaId": cod_empresa,
            "Accept": "*/*",
        }
        
        params = {
            "ref": int(ref),
            "periodo": periodo,
            "filtros": json.dumps({
                "tipo": None,
                "professoresIds": [],
                "ambientesIds": [],
                "alunosIds": [],
                "tipoDuracao": [],
                "disponibilidade": [],
                "modalidadesIds": [],
                "situacaoHorario": "",
                "turno": "",
                "search": ""
            })
        }

        try:
            response = req.get(url_micro_servicos, headers=headers, params=params)

        except Exception as e:
            response = f"Erro ao verificar aulas: {e}"

        register_log(url_micro_servicos, {}, headers, "GET", response, "check_classes_day", self.id_empresa, params)

        try:
            return dp.process_classes_data(response.json())
        except:
            return response.text

    @WorkersTracer(
        span_name_prefix=f"{__name__}.check_class_details",
        span_description="Verificando detalhes da aula",
        span_attributes={
            "aula": "aula",
            "dia": "dia",
            "mes": "mes",
            "id_empresa_": "id_empresa_"
        }
    )
    def check_class_details(self, aula: int, dia: int, mes: int) -> str:
        """
        Esta função deve ser usada para verificar detalhes sobre uma aula específica.

        :param int aula: O codigo da aula que você deseja verificar os detalhes.
        :param int dia: O dia do mês que a aula ocorre.
        :param int mes: O mês que a aula ocorre.
        """
        mes = str(mes).zfill(2)
        dia = str(dia).zfill(2)
        ano = Timestamp.now().year
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Verificando detalhes da aula...")
        url_micro_servicos = f"{self.pit.get_url('treinoUrl', id_empresa_)}/prest/psec/agenda/turmas/{aula}/aula-detalhada"
        token_auth = self.pit.get_token_auth(id_empresa_)
        dia = f"{ano}{mes}{dia}"
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "empresaId": cod_empresa
        }
        params = {"dia": dia, "completo": "false"}

        try:
            response = req.get(url_micro_servicos, headers=headers, params=params)
        except Exception as e:
            response = f"Erro ao verificar detalhes da aula: {e}"

        register_log(url_micro_servicos, {}, headers, "GET", response, "check_class_details", self.id_empresa, params)
        
        try:
            return json.dumps(response.json())
        except:
            return response.text
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.book_call",
        span_description="Agendando ligação",
        span_attributes={
            "duvida": "duvida",
            "dia": "dia",
            "mes": "mes",
            "hora": "hora",
            "minuto": "minuto"
        }
    )
    def book_call(self, duvida: str, dia: int, mes: int, hora: int, minuto: int) -> str:
        """
        Esta função serve para agendar uma ligação com um consultor da academia, caso você não saiba responder a alguma dúvida do aluno.

        :param str duvida: A dúvida do aluno que você não consegue responder.
        :param int dia: O dia do mês que a ligação deve ocorrer.
        :param int mes: O mês que a ligação deve ocorrer.
        :param int hora: A hora que a ligação deve ocorrer.
        :param int minuto: O minuto que a ligação deve ocorrer.
        """
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        url_micro_servicos = f"{self.pit.get_url('contatoMsUrl', id_empresa_)}/v1/avulso/historico/agendamento/ligacao"
        token_auth = self.pit.get_token_auth(id_empresa_)
        codigo = json.loads(self.user_context).get("aluno", {}).get("codigo", None)
        if codigo is None:
            return "Será necessário cadastrar o aluno com a função register_visitor para tentar agendar essa ligação."
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "accept": "*/*",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        req_body = {
            "cliente": codigo,
            "hora": hora,
            "minuto": minuto,
            "dia": Timestamp.now().replace(day=dia, month=mes).strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            "colaboradorresponsavel": 2,
            "responsavelcadastro": 2,
            "empresa": cod_empresa,
            "observacao": duvida,
            "fase": "VA",
        }
        response = req.post(url_micro_servicos, headers=headers, json=req_body)
        register_log(url_micro_servicos, req_body, headers, "POST", response, "book_call", self.id_empresa)
        if response.status_code == 200:
            return f"Ligação agendada com sucesso! Informe ao usuário que foi agenda uma ligação para o dia {dia}/{mes} às {hora}:{minuto} sobre a dúvida: {duvida}"
        else:
            return "Não foi possível agendar a ligação."

    @WorkersTracer(
        span_name_prefix=f"{__name__}.book_class",
        span_description="Agendando aula",
        span_attributes={
            "aula": "aula",
            "dia": "dia",
            "mes": "mes",
            "id_empresa_": "id_empresa_"
        }
    )
    def book_class(self, aula: int, dia: int, mes: int) -> str:
        """
        Você é capaz de agendar/reservar/marcar uma aula/aula experimental para um aluno por meio dessa função.

        :param int aula: O codigo da aula que você deseja reservar.
        :param int dia: O dia do mês que a aula ocorre.
        :param int mes: O mês que a aula ocorre.
        """
        mes = str(mes).zfill(2)
        dia = str(dia).zfill(2)
        ano = Timestamp.now().year
        id_empresa_ = self.id_empresa.split("-")[0]
        cod_empresa = self.id_empresa.split("-")[1]
        logger.info("Reservando aula...")
        url_micro_servicos = f"{self.pit.get_url('treinoUrl', id_empresa_)}/prest/psec/agenda/turmas/{aula}/marcar-aluno"
        token_auth = self.pit.get_token_auth(id_empresa_)
        data = f"{ano}{mes}{dia}"
        matricula = json.loads(self.user_context).get("aluno", {}).get("matricula", None)
        if matricula is None:
            return "Será necessário cadastrar o aluno com a função register_visitor para reserver a aula."
        headers = {
            "Authorization": f"Bearer {token_auth}",
            "Content-Type": "application/json",
            "empresaId": cod_empresa
        }
        params = {"matricula":f"{matricula}","dia":f"{data}","autorizado":"false","acao":"AULA_EXPERIMENTAL","origem": "CONVERSAS_IA"}
        response = req.put(url_micro_servicos, headers=headers, params=params, json={})
        register_log(url_micro_servicos, {}, headers, "PUT", response, "book_class", self.id_empresa, params)

        if response.status_code == 200:
            return "Aula reservada com sucesso!"
        else:
            if isinstance(response, dict):
                message = response.get("meta", {}).get("message", "Não foi possível reservar a aula.")
            else:
                message = response.text
            if "níveis exigidos" in message:
                return "O aluno não tem os níveis necessários, pergunte em qual destes ele se enquadra melhor: " + json.dumps(self.pit.get_levels())
            
            elif "idade do aluno" in message:
                return "Confirme a data de nascimento do aluno e salve com a função save_user_birthdate."
            return "Não foi possível reservar a aula, o assistente provavelmente errou o dia, ou o código da aula."
    
    @WorkersTracer(
        span_name_prefix=f"{__name__}.generate_train",
        span_description="Gerando treino",
        span_attributes={
            "idade": "idade",
            "altura": "altura",
            "sexo": "sexo",
            "objetivo": "objetivo",
            "experiencia": "experiencia",
            "dias_de_treino": "dias_de_treino",
            "tempo_de_treino": "tempo_de_treino"
        }
    )
    def generate_train(self, idade: int, altura: int, sexo: str, objetivo: str, experiencia: str, dias_de_treino: str, tempo_de_treino: str) -> str:
        """
        Esta função é usada para gerar um treino personalizado com a nossa IA para o aluno com base em suas características pessoais e objetivos. O treino é adaptado às necessidades do aluno.
        
        :param int idade: A idade do aluno em anos.
        :param int altura: A altura do aluno em centímetros.
        :param str sexo: O sexo do aluno, pode ser {Masculino} ou {Feminino}.
        :param str objetivo: O objetivo principal do aluno, pode ser {Força}, {Hipertrofia} ou {Emagrecimento}.
        :param str experiencia: O nível de experiência do aluno, pode ser {Iniciante}, {Intermediário} ou {Avançado}.
        :param int dias_de_treino: O número de dias que o aluno deseja treinar por semana.
        :param int tempo_de_treino: O tempo disponível que o aluno terá para cada treino em minutos.
        """
        ## TODO: Usar o id do cliente correto
        ## TODO: Usar a condição atual do cliente -> Isso ainda não está sendo usado pela IA do treino
        client_id = 2029
        current_condition = "Treino entre 6 meses e 1 ano"
        url = "https://treino-por-ia-1060005216921.us-east1.run.app/generate-training-plan"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        request_data = {
            "client_id": client_id,
            "age": idade,
            "height": altura,
            "body_type": sexo,
            "goal": objetivo,
            "training_days": dias_de_treino,
            "training_time": tempo_de_treino,
            "experience_level": experiencia,
            "current_condition": current_condition
        }

        logger.info("Gerando treino...")

        response = req.post(url, headers=headers, json=request_data)

        return dp.process_train_data(response.json())[0]

    @WorkersTracer(
        span_name_prefix=f"{__name__}.add_previous_message",
        span_description="Adicionando mensagem anterior",
    )
    def add_previous_message(self, telefone) -> None:
        previous_messages_ = self.bq.get_last_messages(limit=10, telefone=telefone)

        if previous_messages_ is not None and type(previous_messages_) == DataFrame:
            for _, message in previous_messages_.iterrows():
                self.previous_messages.append({
                    "role": "user" if message["enviado_por"] == "user" else "model",
                    "parts": message["mensagem"]
                })

    @WorkersTracer(
        span_name_prefix=f"{__name__}.execute",
        span_description="Executando",
    )
    def execute(self) -> str:
        response = self.get_response()

        #register_log()

        del self

        return response

    @WorkersTracer(
        span_name_prefix=f"{__name__}.save_messages",
        span_description="Salvando mensagens",
    )
    def save_messages(self, user_response, result, telefone, prompt_tokens=0, completion_tokens=0):
        if user_response:
            self.bq.save_message("user", user_response, telefone)
        if result:
            self.bq.save_message("assistant", result, telefone, model="gemini-1.5-flash", prompt_tokens=prompt_tokens, completion_tokens=completion_tokens)

    @WorkersTracer(
        span_name_prefix=f"{__name__}.save_in_parallel",
        span_description="Salvando em paralelo",
    )
    def save_in_parallel(self, user_response, result, telefone, prompt_tokens=0, completion_tokens=0):
        save_message_process = Process(target=self.save_messages, args=(user_response, result, telefone, prompt_tokens, completion_tokens))
        save_message_process.start()

    @WorkersTracer(
        span_name_prefix=f"{__name__}.create_function_message",
        span_description="Criando mensagem de função",
    )
    def create_function_message(self, name: str, content: str, messages: list) -> genai.types.GenerateContentResponse:
        #return self.client.chat.completions.create(
        #        model="gpt-4o-mini",
        #        messages=messages + [{
        #        "role": "function",
        #        "name": name,
        #        "content": content
        #        }],
        #        max_tokens=1200,
        #        stop=None,
        #        temperature=0.5
        #    )
        return self.model.start_chat(history=messages + [{
            "role": "tool",
            "name": name,
            "content": content
        }]).send_message("")

    @WorkersTracer(
        span_name_prefix=f"{__name__}.process_function_call",
        span_description="Processando chamada de função",
        span_attributes={
            "function_name": "function_name",
        }
    )
    def process_function_call(self, function_name: str, function_args: dict, messages: list) -> str:
        logger.info(f"Processando chamada de função: {function_name}")
        logger.info(f"Argumentos: {function_args}")
        data = None
        generate = True
        save = True
        if function_name == "get_additional_context":
            data = self.get_additional_context(**function_args)
        
        elif function_name == "search_by_cpf":
            data = self.search_by_cpf(**function_args)
        
        elif function_name == "save_user_name":
            data = self.save_user_name(**function_args)
        
        elif function_name == "save_user_empresa":
            data = self.save_user_empresa(**function_args)

        elif function_name == "save_user_level":
            data = self.save_user_level(**function_args)

        elif function_name == "check_classes_day":
            data = self.check_classes_day(**function_args)
        
        elif function_name == "check_class_details":
            data = self.check_class_details(**function_args)

        elif function_name == "book_class":
            data = self.book_class(**function_args)
        
        elif function_name == "book_call":
            data = self.book_call(**function_args)

        elif function_name == "save_user_birthdate":
            data = self.save_user_birthdate(**function_args)

        elif function_name == "register_visitor":
            data = self.register_visitor(**function_args)

        elif function_name == "warn_user":
            data = self.warn_user()
        
        elif function_name == "suggest_gyms_by_state_city_context":
            data = self.suggest_gyms_by_state_city_context(**function_args)

        elif function_name == "generate_train":
            data = BaseResponse(self.generate_train(**function_args))
            usage_tokens = None
            generate = False
            save = False

        elif function_name == "end_conversation":
            data = self.end_conversation()
            usage_tokens = None
            generate = False
        
        elif function_name == "dont_respond":
            data = self.dont_respond()
            usage_tokens = None
            generate = False
    
        if save:
            self.save_in_parallel(None, f"Função chamada: {function_name}, argumentos: {function_args}, resposta: {data}", self.telefone)

        if generate:
            response = self.create_function_message(function_name, data, messages)
            result_message = response.text
            usage_tokens = response.usage_metadata
        else:
            result_message = data

        return result_message, usage_tokens

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_response",
        span_description="Obtendo resposta do Gemini",
        span_attributes={
            "user_response": "user_response",
            "result": "result",
            "telefone": "telefone",
            "model_family": "Gemini",
            "usage_tokens": "usage_tokens",
            "prompt_tokens": "prompt_tokens",
            "completion_tokens": "completion_tokens",
            "id_empresa": "id_empresa",
        }
    )
    def get_response(self, user_response=None) -> tuple[str, dict]:
        if Config().isLoadTesting():
                return "Olá! Eu sou a Tom Tom! Este é um teste de carga. Eu não utilizei llm para não gastar dim dim 💙"

        logger.info("Obtendo resposta do Gemini...")
        history = self.previous_messages
        try:
            response = self.model.start_chat(history=history).send_message(user_response if user_response else "")
        except Exception as e:
            import traceback
            traceback.print_exc()
            return "Erro ao obter resposta do Gemini."

        logger.info(f"Resposta do Gemini: {response.text}")

        result_message = response.text
        
        usage_tokens = response.usage_metadata
        prompt_tokens_ = usage_tokens.prompt_token_count
        completion_tokens_ = usage_tokens.candidates_token_count

        if response.candidates[0].content.parts[0].function_call:
            function_call = response.candidates[0].content.parts[0].function_call
            function_name = function_call.name
            function_args = json.loads(function_call.args)
            function_call_result = self.process_function_call(function_name, function_args, history)
            result_message = function_call_result[0]
            if result_message == STOP:
                return None
            function_call_usage_tokens = function_call_result[1]
            if function_call_usage_tokens:
                prompt_tokens_ += function_call_usage_tokens.prompt_token_count
                completion_tokens_ += function_call_usage_tokens.candidates_token_count

        result = result_message if result_message else None

        # self.save_in_parallel(user_response, result, self.telefone, prompt_tokens_, completion_tokens_)
        
        infos = {
            "prompt_tokens": prompt_tokens_, 
            "completion_tokens": completion_tokens_,
            "n_chars": len(str(result).replace(" ", "").replace("\n", "").strip()) if result else 0,
            "model": "gemini-1.5-flash"
        }
        
        if result:
            result = dp.preprocess_text(result)

        return result, infos

    @WorkersTracer(
        span_name_prefix=f"{__name__}.__del__ - (Destrutor)",
        span_description="GeminiResponseModule finalizado",
    )
    def __del__(self):
        logger.info("GeminiResponseModule finalizado.")
        pass
