"""<PERSON><PERSON><PERSON><PERSON> responsável por realizar a comunicação com o OpenAI para embeddings."""
import logging
from typing import List, Union
from src.connections.connections import Connections
from src.extras.util import WorkersTracer

logger = logging.getLogger("conversas_logger")
CONNECTIONS = Connections.get_instance()


class OpenAIEmbeddingsModule:
    def __init__(self) -> None:
        self.client = CONNECTIONS.openai_client
        self.model = "text-embedding-ada-002"

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_embeddings",
        span_description="Gerando embeddings para o texto em chunks",
        span_attributes={
            "chunk_size": "chunk_size"
        }
    )
    def get_embeddings(
            self,
            text: Union[str, List],
            chunk_size: int = 50
    ) -> List[dict]:
        """Gera os embeddings para o texto em chunks."""
        logger.info("Getting embeddings...")
        if isinstance(text, str):
            chunks = self._split_text_into_chunks(text, chunk_size)
        else:
            chunks = text
        embeddings = []
        for chunk in chunks:
            response = self.client.embeddings.create(
                input=chunk, model=self.model)
            result = {
                "embedding": response.data[0].embedding, "text_chunk": chunk}
            embeddings.append(result)
        return embeddings

    @WorkersTracer(
        span_name_prefix=f"{__name__}._split_text_into_chunks",
        span_description="Dividindo o texto em chunks",
        span_attributes={
            "chunk_size": "chunk_size"
        }
    )
    def _split_text_into_chunks(self, text: str, chunk_size: int):
        """Divide o texto em chunks de tamanho especificado com sobreposição."""
        chunks = []
        queued = None
        paragraphs = text.split("\n")
        for p in paragraphs:
            words = p.split()
            # Caso 1: Muito pequeno, menor que a metade do chunk_size
            # -> Adiciona o parágrafo ao próximo chunk
            if len(words) < chunk_size // 2:
                if queued is None:
                    queued = p
                else:
                    # Se o próximo chunk que veio também for pequeno:
                    # Caso 1: Os tamanho dos dois satisfaz o tamanho mínimo
                    # -> Adiciona o parágrafo ao chunk atual
                    if len(queued.split(" ")) + len(p.split(" ")) > chunk_size // 2:
                        chunks.extend(["\n".join([queued, p])])
                        queued = None
                        continue
                    # Caso 2: Não satisfaz o tamanho mínimo
                    # -> O atual é passado pra frente
                    queued += "\n" + p
            # Caso 2: Muito grande, maior que o chunk_size * 1.2
            # -> Divide o parágrafo em partes menores
            elif len(words) > chunk_size * 1.2:
                # Divide o parágrafo em partes menores
                for i in range(0, len(words), chunk_size):
                    word = " ".join(words[i:i + chunk_size])
                    if queued is not None:
                        word = "\n".join([queued, word])
                        queued = None
                    chunks.append(word)
            # Caso 3: O parágrafo é do tamanho certo
            # -> Adiciona o parágrafo ao chunk atual
            else:
                if queued is not None:
                    p = "\n".join([queued, p])
                    queued = None
                chunks.append(p)
        return chunks
