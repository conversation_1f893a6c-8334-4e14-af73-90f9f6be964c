import os
import logging
from apscheduler.schedulers.background import BackgroundScheduler
from src.worker.instance_status.check_instance_status import Instance<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.extras.util import WorkersTracer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Monitoramento de status de instâncias desabilitado",
    span_attributes={
        "func": "instance_status_checker.check_instance_status",
        "trigger": "interval",
        "hours": "interval_hours",
        "name": "Verificação periódica de status das instâncias"
    }
)
def run(redis_client):
    if os.getenv("Z_API_INSTANCE_STATUS_CHECK") != "True":
        logger.info("Monitoramento de status de instâncias desabilitado.")
        return
    scheduler = BackgroundScheduler()
    interval_hours = int(os.getenv("Z_API_INSTANCE_STATUS_CHECK_INTERVAL", "2"))

    instance_status_checker = InstanceStatusChecker()

    scheduler.add_job(
        func=instance_status_checker.check_instance_status,
        trigger="interval",
        hours=interval_hours,
        id="instance_status_check",
        name="Verificação periódica de status das instâncias",
        replace_existing=True,
    )

    scheduler.start()
    logger.info(f"Agendamento do monitoramento configurado para {interval_hours} horas.")

    try:
        scheduler._thread.join()
        
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
