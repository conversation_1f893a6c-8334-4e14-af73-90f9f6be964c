import logging
import requests
from datetime import datetime
import json
import os

from src.connections.connections import Connections
from src.data.bigquery_data import BigQueryData as bq
from src.integrations.google.gmail.email_service import GmailAPI
from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools as PIT
from src.extras.util import WorkersTracer


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

GCP_BIGQUERY_DATASET = os.getenv("GCP_BIGQUERY_DATASET")

class InstanceStatusChecker:
    def __init__(self):
        self.connections = Connections.get_instance()
        self.bigquery_client = self.connections.bigquery_client
        self.redis_client = self.connections.redis_client
        self.openai_client= self.connections.openai_client
        self.z_api_client_token = os.getenv("Z_API_CLIENT_TOKEN")

    @WorkersTracer(
        span_name_prefix="get_instances_from_bq",
        span_description="Buscando os dados da instância do BQ",
    )
    def get_instances_from_bq(self):
        """
        Método para buscar os dados das instâncias do BigQuery.
        """
        query = f"""
            SELECT distinct instance_id, token, id_empresa
            FROM {GCP_BIGQUERY_DATASET}.instances
        """
        try:
            query_job = self.bigquery_client.query(query)
            results = query_job.result()
            return [
                {
                    "instance_id": row["instance_id"],
                    "token": row["token"],
                    "id_empresa": row["id_empresa"]
                }
                for row in results if row["instance_id"] != 'None' and row["token"] != 'None'
            ]
        except Exception as e:
            logger.error(f"Erro ao recuperar instâncias do BigQuery: {e}")
            return []

    @WorkersTracer(
        span_name_prefix="verify_status_with_api",
        span_description="Verificando o status da instância",
        span_attributes={
            "istance_id": "instance_id",
            "id_empresa": "id_empresa"
        }
    )
    def verify_status_with_api(self, instance_id, token, id_empresa):
        """
        Verifica o status de uma instância via API e faz o log de acordo com a resposta.
        """

        base_url = f'https://api.z-api.io/instances/{instance_id}/token/{token}'
        url = f'{base_url}/status'

        try:
            response = requests.get(url, headers={'client-token': self.z_api_client_token})
            
            if response.status_code == 400:
                error_data = response.json()
                if "error" in error_data and "To continue sending a message, you must subscribe to this instance again" in error_data["error"]:
                    log_data = {
                        "error": "instancia precisa ser reconectada",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "chave_empresa": id_empresa
                    }
                    self.save_log_to_bigquery(log_data)
                    self.save_log_to_redis(log_data)
                    self.notify_admin(id_empresa) 
                    return None  
                
            response.raise_for_status() 
            status_data = response.json()
            status = status_data.get("connected", None)

            if status is None:
                return None
            
            if status == False:
                log_data = {
                    "error": "instancia desconectada",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "chave_empresa": id_empresa
                }
                self.save_log_to_bigquery(log_data)
                self.save_log_to_redis(log_data)
                logger.warning(f"Instância {instance_id} desconectada para a empresa {id_empresa}.")
                self.notify_admin(id_empresa)
            else:
                logger.info(f"Instância {instance_id} conectada para a empresa {id_empresa}. Detalhes: {status_data.get('data')}")
            return status

        except requests.RequestException as e:
            logger.error(f"Erro ao verificar status da instância {instance_id}: {e}")
            return None

    @WorkersTracer(
        span_name_prefix="save_log_to_redis",
        span_description="Salvando o log de desconexão no Redis.",
    )
    def save_log_to_redis(self, log_data):
        """
        Salva o log de desconexão no Redis.
        """
        log_json = json.dumps(log_data)
        try:
            self.redis_client.rpush("task_queue_instance_status", log_json)
            logger.info(f"Log de desconexão salvo na fila do Redis.")
        except Exception as e:
            logger.error(f"Erro ao tentar salvar o log no Redis: {e}")

    @WorkersTracer(
        span_name_prefix="generate_notification_message",
        span_description="Gerando mensagem de notificação.",
    )
    def generate_notification_message(self, contexto):
        """
        Gera uma mensagem personalizada de notificação usando IA (OpenAI).
        """
        try:
            notification_message = (
                "Você é um assistente de Suporte da ConversasAI" 
                "Você precisa notificar o cliente do ConversasAI sobre a desconexão da instância do WhatsApp. Se apresente e Peça para o cliente ler o QR Code novamente para restaurar a conexão. Não escreva muito seja breve "
                "Gere uma mensagem amigável e profissional e curta.Com base no no seguinte contexto da academia, não passe informações sendiveis como o Id da academia:"
                f"Contexto da Academia: {contexto}"
                "Mensagem esperada: Uma notificação clara e objetiva para o administrador da instância, sem necessidade de resposta. Avise que esse é meio é usado apenas para notifiações o suporte que não responderá a mensagens."
            )
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "Você é um assistente de notificação para academias."},
                    {"role": "user", "content": notification_message}
                ],
                max_tokens=150
            )
            message = response.choices[0].message.content.strip()
            return message
        except Exception as e:
            logger.error(f"Erro ao gerar mensagem com IA: {e}")
    
    @WorkersTracer(
        span_name_prefix="get_url_login",
        span_description="Gerando URL de login.",
        span_attributes={
            "id_empresa": "id_empresa"
        }
    )
    def get_url_login(self, id_empresa):
        """
        Retorna a URL de login da plataforma.
        """
        pit = PIT(id_empresa=id_empresa)

        id_empresa_ = id_empresa.split("-")[0]
        cod_empresa = id_empresa.split("-")[1]

        url = pit.get_url("zwFrontUrl", id_empresa_)

        token = pit.get_token_auth(id_empresa_)

        url += f"/pt/adicionarConta?token={token}&moduleId=NZW&idiomabanco=0&integracaoZW=true&empresaId={cod_empresa}&goBackModule=ZW&redirect=/configuracao/v2"

        return url

    @WorkersTracer(
        span_name_prefix="notify_admin",
        span_description="Notificando administrador da empresa.",
        span_attributes={
            "id_empresa": "id_empresa"
        }
    )
    def notify_admin(self, id_empresa):
        """
        Envia uma notificação para o administrador da empresa informando sobre a desconexão.
        Envia notificações por e-mail e por telefone.
        """
        try:
            query = f"""
                SELECT telefone_responsavel_empresa, email_responsavel_empresa
                FROM {GCP_BIGQUERY_DATASET}.empresas
                WHERE id_empresa = '{id_empresa}'
            """
            query_job = self.bigquery_client.query(query)
            result = query_job.result()

            if result.to_dataframe().empty:
                logger.error(f"Dados de contato para empresa {id_empresa} não encontrados.")
                return
            
            telefone = result.to_dataframe().iloc[0].get('telefone_responsavel_empresa') if result else None
            email = result.to_dataframe().iloc[0].get('email_responsavel_empresa') if result else None

            if telefone:
                contexto = bq(id_empresa=id_empresa).get_gym_context()
                url_login = self.get_url_login(id_empresa)

                message = self.generate_notification_message(contexto)

                task = {
                    "id_empresa": id_empresa,
                    "data": {
                        "telefone_empresa": telefone, 
                        "message": message,
                        "contexto": contexto
                    }
                }

                self.redis_client.rpush('task_queue_disconnect_notifications', json.dumps(task))
                logger.info(f"Notificação de desconexão enviada ao telefone da empresa {id_empresa}.")

            if email:
                gmail_api = GmailAPI()
                assunto = "Instância Desconectada - Conversas.AI"
                url_login = self.get_url_login(id_empresa)
                body= url_login
                gmail_api.enviar_email_status(email,assunto,body)
                logger.info(f"Notificação de desconexão enviada para o e-mail da empresa {id_empresa}.")

        except Exception as e:
            logger.error(f"Erro ao notificar administrador da empresa {id_empresa}: {e}")
    
    @WorkersTracer(
        span_name_prefix="save_log_to_bigquery",
        span_description="Salvando o log de desconexão no BigQuery.",
    )
    def save_log_to_bigquery(self, log_data):
        """
        Salva o log de desconexão no BigQuery.
        """
        dataset_id = f"{GCP_BIGQUERY_DATASET}"
        table_id = "logs_conexao_whatsapp"
        rows_to_insert = [log_data]
        try:
            errors = self.bigquery_client.insert_rows_json(f"{self.bigquery_client.project}.{dataset_id}.{table_id}", rows_to_insert)
            if errors:
                logger.error(f"Erro ao inserir dados no BigQuery: {errors}")
            else:
                logger.info(f"Log de desconexão inserido com sucesso no BigQuery.")
        except Exception as e:
            logger.error(f"Erro ao tentar registrar log no BigQuery: {e}")

    @WorkersTracer(
        span_name_prefix="check_instance_status",
        span_description="Verificando o status das instâncias do BigQuery.",
    )
    def check_instance_status(self):
        """
        Método para buscar as instâncias do BigQuery, verificar seu status e fazer o log.
        """
        instances = self.get_instances_from_bq()

        if not instances:
            logger.warning("Nenhuma instância encontrada no BigQuery.")
            return

        for instance in instances:
            instance_id = instance["instance_id"]
            token = instance["token"]
            id_empresa = instance["id_empresa"]

            logger.info(f"Verificando o status da instância {instance_id}...")

            _ = self.verify_status_with_api(instance_id, token, id_empresa)
