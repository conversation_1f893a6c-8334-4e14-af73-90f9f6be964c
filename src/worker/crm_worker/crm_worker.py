import logging
import json
import requests

from src.integrations.pacto.tools.integration_tools import PactoIntegrationTools
from src.extras.util import register_log
from src.extras.util import WorkersTracer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="CRM worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
        "id_empresa": "id_empresa"
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        try:
            task = redis_client.blpop('task_queue_crm_msg_history')
            if task:
                task = json.loads(task[1].decode('utf-8'))
                id_empresa = task['id_empresa']
                data = task['data']
                message = task['message']
                cliente = task['cliente']
                tipoContato = task['tipoContato']
                fase = task['fase']

                pacto = PactoIntegrationTools(id_empresa)

                id_empresa_ = id_empresa.split("-")[0]
                cod_empresa = id_empresa.split("-")[1]
                url_crm = pacto.get_url("contatoMsUrl", id_empresa_)
                token_auth = pacto.get_token_auth(id_empresa_)
                url_micro_servicos = f"{url_crm}/v1/ia/conversa/historico-contato"
                headers = {
                    "Authorization": f"Bearer {token_auth}",
                    "empresaId": cod_empresa
                }
                body = {"cliente": cliente,
                        "message": message,
                        "tipoContato": tipoContato,
                        "fase": fase,
                        "data": data}
                try:
                    response = requests.post(url=url_micro_servicos,
                                            json=body,
                                            headers=headers)
                    register_log(url_micro_servicos, body, headers, "POST", response, "send_message_to_crm", id_empresa)
                    logger.info(f" [x] Task processed successfully: {response.status_code}")
                
                except requests.exceptions.RequestException as e:
                    logger.error(f" [!] Error processing task: {e}")
        
        except Exception as e:
            logger.error(f" [!] Unexpected error: {e}")
            continue
        iter_count += 1
