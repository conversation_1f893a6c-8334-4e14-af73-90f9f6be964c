from whatsapp_messager.whatsapp_messager import WhatsappMessager
from src.extras.util import WorkersTracer

class MessagerSelector:
    def __init__(self, messager_type):
        self.messager_type = messager_type

    @WorkersTracer(
        span_name_prefix=f"{__name__}.get_messager",
        span_description="<PERSON><PERSON><PERSON><PERSON><PERSON> o messager"
    )
    def get_messager(self):
        if self.messager_type == 'whatsapp':
            return WhatsappMessager()
        else:
            raise ValueError('Messager type not supported')
        
    def __del__(self):
        pass
