import logging
import json
from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import WorkersTracer


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="API Keys Worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
    }
)
def run(redis_client, max_iter=None):
    logger.info(" [*] Waiting for tasks")
    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        try:
            task = redis_client.brpop('task_queue_api_keys')
            if task:
                task = json.loads(task[1].decode('utf-8'))
                api_key = task['api_key']
                task_type = task['task_type']
                id_empresa = task.get('id_empresa', None)

                bq_ = bq(id_empresa=id_empresa)

                match task_type:
                    case 'registration':
                        logger.info(f"Salvando os dados de registro da api_key: {api_key}")

                        integration = task.get('integration')
                        if integration:
                            bq_.save_auth_api_key(api_key=api_key, user=integration, active=True)
                        else:
                            logger.error("Integration is required for registration task!.")

                    case 'authorization':
                        logger.info(f"Salvando os dados de autentificação da api_key: {api_key}.")

                        auth_token = task.get('auth_token')
                        logger.info(f"token {auth_token} ")
                        expiration_time = task.get('expiration')
                        logger.info(f"exp {expiration_time} ")
                        if auth_token and expiration_time:
                            bq_.save_auth_session(api_key=api_key, token=auth_token, expiration_time=expiration_time)
                        else:
                            logger.error("Auth token and expiration time are required for authorization task!.")

                    case 'deletion':
                        logger.info(f"Deletando a api_key {api_key}")
                        bq_.delete_api_key(api_key)

                    case _:
                        logger.info(f"Tarefa desconhecida: {task_type}")

                logger.info(" [x] Done")
            
        except Exception as e:
            logger.error(f" [x] Error: {e}")
            iter_count += 1
            continue
        iter_count += 1
