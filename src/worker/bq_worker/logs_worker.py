import logging
import json

from src.data.bigquery_data import BigQueryData as bq
from src.extras.util import WorkersTracer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="BQ logs Worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")

    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        task = redis_client.brpop('logs')
        if task:
            task = json.loads(task[1].decode('utf-8'))
            data = task.get('data', {})
            id_empresa = data.get('id_empresa', None)
            table = task.get('table', None)
            if id_empresa is None:
                if data.get('status', None) == 'error':
                    id_empresa = "*"
                else:
                    id_empresa = "*"
            if table is None:
                continue
            if data == {}:
                continue
            bq_ = bq(id_empresa=id_empresa)
            bq_.register_log(data, table)
            logger.info(" [x] Done")

        iter_count += 1
