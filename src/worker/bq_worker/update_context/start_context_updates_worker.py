import logging
import os
import time
from apscheduler.schedulers.background import BackgroundScheduler
from src.worker.bq_worker.update_context.update_context import run as run_updates
from src.extras.util import WorkersTracer

logger = logging.getLogger("conversas_logger")


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Atualização de contexto das empresas",
    span_attributes={
        "redis_client": "redis_client",
        "func": "run_updates",
        "trigger": "interval",
        "minutes": "interval_minutes"
    }
)
def run(redis_client):
    if not os.getenv("RUN_CONTEXT_UPDATES", "false").lower() == "true":
        logger.info("Atualizações de contexto desativadas.")
        return
    scheduler = BackgroundScheduler()
    interval_minutes = int(os.getenv("MINUTES_FOR_CONTEXT_UPDATE", "10"))
    run_updates()

    scheduler.add_job(
        func=run_updates,
        trigger="interval",
        minutes=interval_minutes,
        id="update_empresas_context",
        name="Atualização de contexto das empresas",
        replace_existing=True,
    )

    scheduler.start()
    logger.info(f"Agendamento configurado para rodar a cada {interval_minutes} minutos.")

    try:
        while True:
            time.sleep(1)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        logger.info("Agendador finalizado.")
