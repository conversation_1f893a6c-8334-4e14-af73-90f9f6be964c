"""Worker para gerenciar o processamento dos documentos dos clientes"""
import base64
import json
import logging
import os
from typing import Literal

from docling.document_converter import DocumentConverter

from src.connections.connections import Connections
from src.data.google_storage import Bucket
from src.data.rag import Rag
from src.extras.util import WorkersTracer

REDIS = Connections.get_instance().redis_client
logger = logging.getLogger()


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Worker to manage the processing of documents from clients",
    span_attributes={
        "action": "action"
    }
)
def process_task(action: Literal["create", "update", "delete"], task: dict) -> None:
    """
    Processa a tarefa de acordo com a ação especificada.
    """
    id_empresa = task.get("id_empresa")

    if action != "delete":
        data_b64 = task.get("data")
        data_b64 = data_b64.replace("\r", "").replace("\n", "").strip()

        if data_b64.startswith("data:"):
            try:
                data_b64 = data_b64.split("base64,", 1)[1]
            except IndexError:
                logger.error(f"[empresa {id_empresa}] Data URI malformada: sem vírgula após base64")
                REDIS.set(f"docs:pending:{id_empresa}", "error", ex=86400)
                return

        data_b64 = data_b64.replace("\r", "").replace("\n", "").strip()

        remainder = len(data_b64) % 4
        if remainder:
            data_b64 += "=" * (4 - remainder)

        try:
            pdf_bytes = base64.b64decode(data_b64)
        except (base64.binascii.Error, TypeError) as e:
            logger.error(f"Erro ao decodificar Base64 para empresa {id_empresa}: {e}")
            REDIS.set(f"docs:pending:{id_empresa}", "error", ex=86400)
            return

        temp_file = f"/tmp/{id_empresa}.pdf"
        with open(temp_file, "wb") as f:
            f.write(pdf_bytes)

    try:
        match action:
            case "create":
                converter = DocumentConverter()
                result = converter.convert(temp_file)
                text = result.document.export_to_markdown()

                bucket = Bucket("custom-pdf")
                bucket.upload(temp_file, f"empresa-{id_empresa}.pdf")

                os.remove(temp_file)

                rag = Rag(id_empresa=id_empresa)
                rag.insert(
                    text=text,
                    tag="custom_pdf",
                )
                REDIS.delete(f"docs:pending:{id_empresa}")
                # TODO: Notificar ao cliente que o documento foi processado
                # notify_client(id_empresa, "Document updated successfully.")

            case "update":
                converter = DocumentConverter()
                result = converter.convert(temp_file)
                text = result.document.export_to_markdown()

                bucket = Bucket("custom-pdf")
                bucket.delete(file=f"empresa-{id_empresa}.pdf")
                bucket.upload(temp_file, f"empresa-{id_empresa}.pdf")

                os.remove(temp_file)

                rag = Rag(id_empresa=id_empresa)
                rag.delete(tag="custom_pdf")
                rag.insert(
                    text=text,
                    tag="custom_pdf",
                )
                REDIS.delete(f"docs:pending:{id_empresa}")
                # notify_client(id_empresa, "Document updated successfully.")

            case "delete":
                bucket = Bucket("custom-pdf")
                bucket.delete(file=f"empresa-{id_empresa}.pdf")

                rag = Rag(id_empresa=id_empresa)
                rag.delete(tag="custom_pdf")

                REDIS.delete(f"docs:pending:{id_empresa}")

    except:  # noqa
        REDIS.set(
            f"docs:pending:{id_empresa}",
            "error",
            ex=60 * 60 * 24,  # 1 dia
        )
        if action != "delete":
            os.remove(temp_file)

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Processing documents from clients and upload to Qdrant",
    span_attributes={
        "action": "action"
    }
)
def run() -> None:
    """
    Processa os documentos dos clientes, e sobe no Qdrant.
    """
    while True:
        _, data = REDIS.brpop("task_queue_document")
        if data:
            task = json.loads(data.decode("utf-8"))
            action = task.get("action")
            try:
                process_task(action, task)
            except ValueError as e:
                logger.error(f"Erro ao processar a tarefa: {e}")
