"""Entrypoint para o worker de processamento dos documentos para rag"""
import logging
from multiprocessing import Process

from src.worker.docs_worker.worker import run
from src.worker.health_check.start_health_check import start_health_check_server
from src.extras.util import WorkersTracer


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")

@WorkersTracer(
    span_name_prefix=__name__,
    span_description="Entrypoint para o worker de processamento dos documentos para rag",
)
def run_workers():
    processes = []

    docs_worker_process = Process(target=run)
    processes.append(docs_worker_process)
    docs_worker_process.start()

    health_check_process = Process(target=start_health_check_server)
    processes.append(health_check_process)
    health_check_process.start()

    for process in processes:
        process.join()


if __name__ == "__main__":
    run_workers()
