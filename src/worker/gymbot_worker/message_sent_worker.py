import logging
import json
import uuid

from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.extras.util import parse_phone, WorkersTracer
from src.data.bigquery_data import get_from_telefone
from src.data.bigquery_data import BigQueryData as bq
from src.data.data_processor import DataProcessor as dp
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="<PERSON><PERSON><PERSON> enviadas (GymBot Worker)",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
        "id_empresa": "id_empresa"
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")

    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        task = redis_client.brpop('message_sent_gymbot')

        if task:
            task = json.loads(task[1].decode('utf-8'))
            logger.info(f" [MESSAGE_SENT_WORKER] Chegou nova task")
            try:
                id_empresa = task['id_empresa']
                telefone = task['data']['phone']
                message_id = task['data']['messageId']
                departamento = task['data']['departamento']
                colaborador = task['data']['colaborador']
                data = task['data']

                logger.info(f" [MESSAGE_SENT] checkpoint")

                user_info_msg, content = dp.process_message(data)
                logger.info(f" [MESSAGE_SENT] user_info_msg: {user_info_msg}")
                logger.info(f" [MESSAGE_SENT] content: {content}")
                user_msg_type = user_info_msg.get('type', None)
                if user_msg_type == 'text':
                    message = content.get('message')
                elif user_msg_type == 'audio':
                    message = str(content)
                else:
                    message = user_msg_type
                bq_ = bq(id_empresa=id_empresa)
                user_context, fase = bq_.get_user_context(telefone=telefone)

                bq_.save_message(
                    "assistant",
                    message,
                    telefone,
                    message_type=user_msg_type,
                    n_chars=len(message),
                    message_id=message_id,
                    provider="gym_bot",
                    departamento=departamento,
                    colaborador=colaborador,
                )

                if user_context and 'aluno' in user_context and 'codigo' in user_context['aluno']:
                    if user_context['aluno']['codigo']:
                        current_time = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')
                        
                        redis_client.lpush(
                            'task_queue_crm_msg_history',
                            json.dumps({
                                "id_empresa":id_empresa,
                                "message":f"{departamento} - {colaborador}: {message}",
                                "cliente": user_context['aluno']['codigo'],
                                "tipoContato": "WA",
                                "fase": fase,
                                "data": current_time
                        }))

            except Exception as e:
                logger.error(f"Erro {e}")

            logger.info(" [x] Done")

        iter_count += 1
