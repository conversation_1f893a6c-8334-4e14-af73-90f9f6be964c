import logging
import json

from src.integrations.gymbot.tools.integration_tools import GymbotIntegrationTools
from src.extras.util import parse_phone, WorkersTracer, register_indicator
from src.data.bigquery_data import get_from_telefone, BigQueryData

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("conversas_logger")


@WorkersTracer(
    span_name_prefix=__name__,
    span_description="transfer conversation worker",
    span_attributes={
        "redis_client": "redis_client",
        "max_iter": "max_iter",
        "id_empresa": "id_empresa"
    }
)
def run(redis_client, max_iter=None):
    
    logger.info(" [*] Waiting for tasks")

    iter_count = 0
    while True:
        if max_iter and iter_count >= max_iter:
            break
        # Bloquear até receber uma nova tarefa
        task = redis_client.brpop('transfer_conversation')
        if task:
            try:
                task = json.loads(task[1].decode('utf-8'))
                logger.info(f" [TRANSFER] Chegou nova task")
                data = task["data"]
                id_empresa = task["id_empresa"]
                contato_json = data["contactDetails"]
                telefone = parse_phone(contato_json["phonenumber"])
                department = data["departmentDetails"]["name"]
                ai_department = "ConversasAI"
                if department:
                    user_data, _ = BigQueryData(id_empresa).get_user_context(telefone) or ({}, None)
                    register_indicator(
                        "transferencia_departamento",
                        id_empresa,
                        indicador=ai_department,
                        telefone=telefone,
                        nome=user_data.get("aluno", {}).get("pessoa", {}).get("nome", "")
                    )
                else:
                    logger.info("[TRANSFER] Erro ao transferir usuário para o departamento ConversasAI")
            except Exception as e:
                logger.error(f"[TRANSFER] Erro ao transferir usuário para o departamento ConversasAI: {e}")
            logger.info(" [x] Done")

        iter_count += 1
