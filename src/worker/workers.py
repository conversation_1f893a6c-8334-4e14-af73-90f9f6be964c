import os
import logging
from multiprocessing import Process
import time
import debugpy
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from src.connections.delayed_queue import DelayedQ<PERSON><PERSON>Worker
from src.worker.bq_worker.indicadores_worker import run as run_indicadores
from src.worker.messages_worker.messages_received_worker import run as run_received
from src.worker.messages_worker.messages_to_send_worker import run as run_send
from src.worker.crm_worker.crm_worker import run as run_crm
from src.worker.bq_worker.updates_worker import run as run_updates
from src.worker.bq_worker.logs_worker import run as run_logs
from src.worker.bq_worker.api_keys_worker import run as run_api_keys
from src.worker.bq_worker.update_context.start_context_updates_worker import run as run_context_updates
from src.worker.health_check.start_health_check import start_health_check_server
from src.worker.instance_status.start_instance_worker import run as start_instance_worker
from src.worker.gymbot_worker.message_sent_worker import run as run_message_sent
from src.worker.gymbot_worker.transfer_conversation_worker import run as run_transfer_conversation
from src.worker.gymbot_worker.save_status_worker import run as run_save_status
from src.extras.util import monitor_health

logger = logging.getLogger(__name__)


class Worker:
    """Classe que representa um worker do Conversas."""
    def __init__(
        self, name, run, debug_port
    ):
        self.name = name
        self.run = run
        self.debug_port = debug_port


WORKERS = [
    Worker("messages_received", run_received, 5678),
    Worker("messages_to_send", run_send, 5679),
    Worker("crm", run_crm, 5680),
    Worker("updates", run_updates, 5681),
    Worker("logs", run_logs, 5682),
    Worker("delayed_queue", DelayedQueueWorker().run, 5683),
    Worker("api_keys", run_api_keys, 5684),
    Worker("instance_status", start_instance_worker, 5685),
    Worker("transfer_conversation", run_transfer_conversation, 5686),
    Worker("message_sent", run_message_sent, 5687),
    Worker("context_updates", run_context_updates, 5688),
    Worker("indicadores", run_indicadores, 5689),
    Worker("save_status", run_save_status, 5690)

]


def parse_enabled_workers(env_value):
    """
    Retorna uma lista de nomes de workers com base na configuração.

    Se a variável não estiver definida ou estiver vazia, retorna todos os workers disponíveis.
    Se a variável contiver "all", retorna todos os workers disponíveis.
    Se a variável contiver nomes de workers separados por vírgula, retorna apenas esses workers.
    Se a variável contiver nomes inválidos, ignora esses nomes e loga um erro.
    """
    if not env_value or env_value.strip().lower() == "all":
        return [w.name for w in WORKERS]
    return [w.strip() for w in env_value.split(",") if w.strip()]


class CodeChangeHandler(FileSystemEventHandler):
    """
    Manipulador de eventos para reiniciar os workers quando um arquivo Python é modificado.

    Usa debounce para evitar múltiplos eventos de modificação em um curto período de tempo.
    """
    def __init__(self, restart_function):
        self.restart_function = restart_function
        self.last_modified = time.time()
        self.debounce_timer = None
        super().__init__()

    def on_modified(self, event):
        # Ignorar arquivos que não são Python
        if not event.src_path.endswith('.py'):
            return

        # Evitar múltiplos reloads para o mesmo evento usando debounce
        current_time = time.time()
        if current_time - self.last_modified < 1:  # Ignorar eventos em menos de 1 segundo
            return

        self.last_modified = current_time
        print(f"Detected change in {event.src_path}, restarting workers...")
        self.restart_function()


def run_workers(redis_client):
    """
    Inicializa os workers do Conversas conforme a variável de ambiente CONVERSAS_ENABLED_WORKERS.

    Variável de ambiente:
        CONVERSAS_ENABLED_WORKERS: Lista separada por vírgula dos workers a serem executados.
            Possíveis valores: messages_received, messages_to_send, crm, updates, logs, all
            Exemplo: "messages_received,messages_to_send"
            Valor especial: "all" para rodar todos os workers.
            Padrão: roda todos os workers se não definido.

    Comportamento para valores inválidos:
        - Workers não reconhecidos são ignorados e um erro é logado.
        - Se nenhum worker válido for especificado, loga erro e não inicia nenhum worker.
    """
    enabled_env = os.getenv("CONVERSAS_ENABLED_WORKERS")
    enabled_workers = parse_enabled_workers(enabled_env)
    all_worker_names = {w.name for w in WORKERS}
    selected_workers: list[Worker] = []
    for name in enabled_workers:
        if name in all_worker_names:
            selected_workers.append(next(w for w in WORKERS if w.name == name))
        else:
            logger.error(
                f"[WORKERS] Worker desconhecido na variável CONVERSAS_ENABLED_WORKERS: '{name}'")

    if not selected_workers:
        logger.error(
            "[WORKERS] Nenhum worker válido especificado. Nenhum worker será iniciado.")
        return

    processes = []

    # Configuração do debugpy para VSCode
    debug_mode = os.environ.get('DEBUG', 'False').lower() == 'true'
    hot_reload = os.environ.get('HOT_RELOAD', 'False').lower() == 'true'

    if debug_mode:
        print("Debug mode enabled. Waiting for VSCode to attach...")

    def run_worker_with_debug(worker: Worker):
        # Cada worker usa uma porta diferente
        nonlocal redis_client
        debug_port = worker.debug_port
        try:
            # Configurar o debugpy para escutar na porta específica
            # Usar try/except para evitar erros se a porta já estiver em uso
            debugpy.listen(("0.0.0.0", debug_port))
            print(f"Worker {worker.name} waiting for debugger on port {debug_port}...")
        except Exception as e:
            print(f"Debug port {debug_port} already in use or error: {e}")

        # Executar o worker com monitoramento de saúde
        monitor_health(worker.run)(redis_client)

    def start_workers():
        nonlocal processes
        # Encerrar processos existentes se houver
        for process in processes:
            if process.is_alive():
                process.terminate()
                process.join(timeout=5)

        processes = []

        # Iniciar o servidor de health check
        health_check_process = Process(target=start_health_check_server)
        processes.append(health_check_process)
        health_check_process.start()

        if len(selected_workers) == 1:
            print(f"Starting single worker: {selected_workers[0].name}")
            worker = selected_workers[0]
            if debug_mode:
                run_worker_with_debug(worker)
            else:
                monitor_health(worker.run)(redis_client)
        else:
            for worker in selected_workers:
                # Se estiver em modo debug, configure o debugpy para este worker
                if debug_mode:
                    process = Process(
                        target=run_worker_with_debug,
                        args=(worker,)
                    )
                else:
                    process = Process(
                        target=monitor_health(worker.run),
                        args=(redis_client,)
                    )

                processes.append(process)
                process.start()

            print(f"Started {len(processes)} worker processes")

    # Iniciar os workers pela primeira vez
    start_workers()

    # Configurar hot reload se estiver habilitado
    if hot_reload:
        print("Hot reload enabled. Watching for file changes...")
        event_handler = CodeChangeHandler(start_workers)
        observer = Observer()
        # Monitorar diretório src
        src_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '')
        observer.schedule(event_handler, path=src_path, recursive=True)
        observer.start()

        try:
            # Manter o processo principal rodando
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            observer.stop()
            # Encerrar todos os processos de worker
            for process in processes:
                if process.is_alive():
                    process.terminate()
                    process.join()
        observer.join()
    else:
        # Se não estiver em modo hot reload, apenas aguardar os processos
        try:
            for process in processes:
                process.join()
        except KeyboardInterrupt:
            # Encerrar todos os processos de worker
            for process in processes:
                if process.is_alive():
                    process.terminate()
                    process.join()
