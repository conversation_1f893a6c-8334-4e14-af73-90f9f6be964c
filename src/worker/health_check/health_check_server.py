from http.server import BaseHTTPRequestHandler
import logging
import json
from src.connections.connections import Connections

logger = logging.getLogger("conversas_logger")
connections = Connections.get_instance()


class SimpleHTTPRequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path != '/health_check':
            self.send_response(404)
            self.end_headers()
            return
        health_check_keys = connections.redis_client.keys('health_check:*')
        response_code = 200
        health_check_data = {}
        if health_check_keys:
            for key in health_check_keys:
                data = json.loads(connections.redis_client.get(key))
                if 'last_error' in data.keys():
                    response_code = 400
                    logger.error(f"Health check failed for {key.decode('utf-8')} with error: {data['last_error']}")
                health_check_data[key.decode('utf-8')] = data
        # Definindo o tipo de conteúdo
        self.send_response(response_code)
        self.send_header("Content-type", "application/json")
        self.end_headers()
        # Resposta do corpo
        self.wfile.write(json.dumps(health_check_data).encode())
