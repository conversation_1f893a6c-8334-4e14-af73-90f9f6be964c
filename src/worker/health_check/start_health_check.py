from http.server import HTTPServer
import logging
import os

from src.worker.health_check.health_check_server import SimpleHTTPRequestHandler

logger = logging.getLogger("conversas_logger")


def start_health_check_server():
    #host = domain.split(":")[1].replace("//", "")
    host = "0.0.0.0"
    port = int(os.getenv("HEALTHCHECK_PORT", 8081))
    server_address = (host, port)

    # Iniciando o servidor
    httpd = HTTPServer(server_address, SimpleHTTPRequestHandler)
    logger.info(f"Health check serving on http://{host}:{port}")
    httpd.serve_forever()
