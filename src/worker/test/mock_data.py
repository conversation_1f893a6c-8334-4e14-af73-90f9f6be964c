class MockData:
    @staticmethod
    def get_whatsapp_user_info():
        return {'id': 532932010, 'full_name': '<PERSON><PERSON><PERSON>', 'first_name': '<PERSON><PERSON><PERSON>', 'last_name': '<PERSON>', 'phone': '+556296026753', 'ddd': '62', 'created_at': '2024-08-14T19:46:13.408333Z', 'live_chat': 'live-chat/all/+556296026753', 'referrer': None, 'referral_count': 0, 'campaigns': [], 'tags': [], 'variables': {'#Nota': None, '#participanteNPS': None, 'Empresa': '60af85257bcbbea7569312fe6ab602ed-1', 'Id Botconversa': None, 'Id Contexto': None, 'Id Conversa': None, 'Id Pacto': None, 'Idade': None, 'Matrícula': None, 'Ponto de Contato': None, 'Resposta': 'eu prefiro musculação', 'Resposta Orion': None, 'Resposta <PERSON>': None, 'Sexo': None, 'VAZIO': 'tudo bem e você? de qual academia você é?', 'assunto': None}, 'sequences': []}

    @staticmethod
    def get_llm_message():
        return 'Olá, Kévio! 😊 Eu sou Marcia e este é um teste de carga  🏋️‍♂️✨'