import requests
import os
from pydub import AudioSegment
import io

from src.worker.entrypoint import connections
from src.extras.util import WorkersTracer

import logging
logger = logging.getLogger("conversas_logger")


@WorkersTracer(
    span_name_prefix=f"{__name__}.proccess_audio",
    span_description="Verificando se os dados possuem um arquivo de áudio",
    span_attributes={
        "url": "data.get('audioUrl')",
        "mimetype": "data.get('mimeType').split(';')[0].split('/')[-1]",
        "message_id": "data.get('messageId')"
    }
)
def proccess_audio(data):
    """
    Check if the data has an audio file.
    """
    url = data.get('audioUrl')
    mimetype = data.get('mimeType').split(';')[0].split('/')[-1]
    message_id = data.get('messageId')
    
    r = requests.get(url, allow_redirects=True)
    data = r.content
    try:
        audio = AudioSegment.from_file(io.BytesIO(data), format="ogg")
        duration = audio.duration_seconds
    except Exception as error:
        logger.error(f'proccess_audio Error: {error}')
        duration = None

    open(f'./{message_id}.{mimetype}', 'wb').write(data)
    
    #audio_path = f'./{message_id}.{mimetype}'
    transcription = get_transcription(message_id, mimetype)
    logger.info(f'CONVERT TO TEXT:\n\tduração: {duration}')
    return transcription, duration

@WorkersTracer(
    span_name_prefix=f"{__name__}.get_transcription",
    span_description="Obtém a transcrição do áudio",
    span_attributes={
        "message_id": "message_id",
        "mimetype": "mimetype"
    }
)
def get_transcription(message_id, mimetype):
    """
    Get the transcription of the audio file.
    """
    client = connections.openai_client
    with open(f'./{message_id}.{mimetype}', "rb") as audio_file:
        transcription = client.audio.transcriptions.create(
        model="whisper-1", 
        file=audio_file
        )
        
    os.remove(f'./{message_id}.{mimetype}')
        
    return transcription.text
