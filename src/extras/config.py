import os
import datetime

class Config:
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    FLOW_NAME = os.getenv("FLOW_NAME", "Ponte")
    QUESTIONARY_FLOW_NAME = os.getenv("QUESTIONARY_FLOW_NAME", "Pesquisa de Satisfação / NPS")
    ENV = os.getenv("FLASK_ENV", "development")
    JWT_EXPIRATION_TIME = int(datetime.timedelta(days=1).total_seconds())
    API_MASTER_KEY = os.getenv("API_MASTER_KEY")
    AUTH_ON = os.getenv("AUTH_ON", "true")
    ALLOWED_ORIGINS =[origin.strip() for origin in os.getenv("ALLOWED_ORIGINS", "").split(",")]
    if ENV == "development":
        ALLOWED_ORIGINS.append("localhost:8080")
    SECRET_KEY = os.getenv("SECRET_KEY", "secret_key")
    GOOGLE_LOGIN_DOC = os.getenv("GOOGLE_LOGIN_DOC", "false")
    GLOBAL_RATE_LIMIT = os.getenv("GLOBAL_RATE_LIMIT", "60/minute")
    RATE_LIMIT_ENABLED = os.getenv("RATE_LIMIT_ENABLED", "true") == "true"
    

    def isLoadTesting(self):
        return self.ENV == "loadtesting"