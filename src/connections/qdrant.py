import os

from qdrant_client import QdrantClient

QDRANT_HOST = os.getenv("QDRANT_HOST", "qdrant")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))


class QdrantConnection:
    """
    Classe para gerenciar a conexão com o Qdrant
    """
    _instance = None
    _initialized = False

    def __init__(self) -> None:
        if self._initialized:
            return
        self.client: QdrantClient = QdrantClient(
            host=QDRANT_HOST,
            port=QDRANT_PORT,
        )

    def __new__(cls, *args, **kwargs) -> "QdrantConnection":
        if not cls._instance:
            cls._instance = super(QdrantConnection, cls).__new__(cls)
        return cls._instance
