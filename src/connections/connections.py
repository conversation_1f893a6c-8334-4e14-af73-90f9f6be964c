import os
import logging
import json
import redis
from openai import OpenAI
from google.oauth2 import service_account
from google.cloud import bigquery
from google.cloud import storage

with open("src/connections/keys.json") as f:
    keys = json.load(f)

logger = logging.getLogger("conversas_logger")

current_dir = os.path.dirname(__file__)
keys_path = os.path.join(current_dir, 'keys.json')
gbq_credentials_file_path = os.path.join(current_dir,'conversas-ai.json')
gcl_credentials_file_path = os.path.join(current_dir,'conversas-ai-buckets.json')

with open(keys_path) as f:
    keys = json.load(f)
    OPENAI_API_KEY = keys["OPEN_AI_API_KEY"]

with open(gbq_credentials_file_path, "r") as file:
    CREDENTIALS = json.load(file)

with open(os.path.join(gcl_credentials_file_path), "r") as file:
    CREDENTIALS_BUCKET = json.load(file)

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

class Connections:
    _instance = None

    def __init__(self) -> None:
        self.openai_client: OpenAI = OpenAI(api_key=OPENAI_API_KEY)
        logger.info("OpenAI client loaded!")
        credentials = service_account.Credentials.from_service_account_info(CREDENTIALS)
        self.bigquery_client: bigquery.Client = bigquery.Client(credentials=credentials, project=CREDENTIALS['project_id'])
        logger.info("BigQuery client loaded!")
        credentials_bucket = service_account.Credentials.from_service_account_info(CREDENTIALS)
        self.storage_client: storage.Client = storage.Client(credentials=credentials_bucket, project=CREDENTIALS_BUCKET['project_id'])
        logger.info("Google Storage client loaded!")
        self.redis_client: redis.Redis = redis.from_url(REDIS_URL)
        logger.info("Redis client loaded!")

    @classmethod
    def get_instance(cls) -> "Connections":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
