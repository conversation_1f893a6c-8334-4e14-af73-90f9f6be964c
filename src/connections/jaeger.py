"""Module for the Jae<PERSON> connection."""
import os
import socket
import logging

from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import <PERSON><PERSON>gerExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.trace import NoOpTracerProvider # Correção: Importado do local correto
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.trace.sampling import <PERSON><PERSON>, SamplingResult, Decision
from opentelemetry.sdk.resources import Resource
from opentelemetry.semconv.resource import ResourceAttributes

# Configuração do logging para ver as mensagens de status
logging.basicConfig(level=logging.INFO)

JAEGER_HOST = os.getenv("JAEGER_HOST", "jaeger")
JAEGER_PORT = int(os.getenv("JAEGER_PORT", 6831))


def is_jaeger_available(host: str, port: int) -> bool:
    """
    Verifica de forma simples se o host do Jaeger está disponível na porta especificada.
    Tenta estabelecer uma conexão de socket com um timeout curto.

    :param host: O hostname ou IP do agente Jaeger.
    :param port: A porta do agente Jaeger.
    :return: True se a conexão for bem-sucedida, False caso contrário.
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(1)  # Timeout de 1 segundo para não bloquear a aplicação
        try:
            s.connect((host, port))
            return True
        except (socket.error, socket.timeout):
            return False


class JaegerConnection:
    """
    Classe para gerenciar a conexão com o Jaeger, com fallback para no-op tracer.
    """

    _instance = None
    _tracer = None

    def __new__(cls) -> "JaegerConnection":
        if cls._instance is None:
            cls._instance = super(JaegerConnection, cls).__new__(cls)
        return cls._instance

    def connect(self, service_name: str) -> trace.Tracer:
        """
        Estabelece conexão com o Jaeger se disponível, caso contrário, usa um tracer NoOp.

        :param service_name: Nome do serviço a ser rastreado.
        """
        if self._tracer is None:
            if is_jaeger_available(JAEGER_HOST, JAEGER_PORT):
                logging.info(f"Jaeger disponível em {JAEGER_HOST}:{JAEGER_PORT}. Iniciando JaegerExporter.")
                
                resource = Resource.create({ResourceAttributes.SERVICE_NAME: service_name})
                jaeger_exporter = JaegerExporter(
                    agent_host_name=JAEGER_HOST,
                    agent_port=JAEGER_PORT,
                )
                exclude_traces_sampler = ExcludeTracesSampler(["BigQuery"])
                tracer_provider = TracerProvider(sampler=exclude_traces_sampler, resource=resource)
                tracer_provider.add_span_processor(BatchSpanProcessor(jaeger_exporter))

                trace.set_tracer_provider(tracer_provider)
            else:
                logging.warning(
                    f"Jaeger não disponível em {JAEGER_HOST}:{JAEGER_PORT}. "
                    "Usando NoOpTracer. Os traces não serão enviados."
                )
                trace.set_tracer_provider(NoOpTracerProvider())

            self._tracer = trace.get_tracer(service_name)

        return self._tracer

    def get_tracer(self) -> trace.Tracer:
        """
        Retorna o tracer configurado (seja o real do Jaeger ou o NoOp).
        """
        if not self._tracer:
            raise Exception("Tracer não conectado. Chame o método connect() primeiro.")
        return self._tracer


class ExcludeTracesSampler(Sampler):
    def __init__(self, excluded_attributes: list):
        super().__init__()
        self.excluded_attributes = excluded_attributes

    def should_sample(self, parent_context, trace_id, name, kind, attributes, links):
        if attributes and attributes.get("db.system") in self.excluded_attributes:
            return SamplingResult(Decision.DROP, attributes, None)
        return SamplingResult(Decision.RECORD_AND_SAMPLE, attributes, None)

    def get_description(self):
        return "ExcludeTracesSampler"
