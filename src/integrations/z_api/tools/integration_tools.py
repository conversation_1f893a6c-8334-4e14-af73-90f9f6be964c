import logging
import os

import requests as req

from src.connections.connections import Connections
from src.extras.util import register_log, register_indicator, token_guard

from src.data.bigquery_data import BigQueryData as bq, get_from_empresa

connections = Connections.get_instance()

logger = logging.getLogger("conversas_logger")


class ZApiIntegrationTools:
    """
    Classe utilitária para integração com a API Z-API, fornecendo métodos para manipulação de chats,
    tags e operações relacionadas a contatos via API externa.
    """

    def __init__(self, id_empresa=None):
        """
        Inicializa a instância da integração ZApiIntegrationTools.

        Args:
            id_empresa (str, opcional): Identificador da empresa para contexto das operações.
        """
        logger.info("[ZAIT] Inicializando ZApiIntegrationTools")
        self.id_empresa = id_empresa
        self.bq = bq(id_empresa=id_empresa)
        self.client_token = os.getenv('Z_API_CLIENT_TOKEN')

    def get_tags(self) -> list:
        """
        Obtém as tags existentes no Z-API.

        Returns:
            list: Lista de tags.
        """
        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/tags"
        response = req.get(url, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            None,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'GET',
            response,
            'get_tags',
            self.id_empresa,
            table="requests_zapi"
        )
        if response.status_code == 200:
            return response.json()
        return {}

    def get_tag_colors(self):
        """
        Obtém as cores disponíveis para tags na instância da empresa.

        Returns:
            dict: Dicionário de cores disponíveis para tags.
        """
        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/business/tags/colors"
        response = req.get(url, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            None,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'GET',
            response,
            'get_tag_colors',
            self.id_empresa,
            table="requests_zapi"
        )
        if response.status_code == 200:
            return response.json()
        return {}

    def create_tag(
        self, tag_name: str, tag_color: int | None
    ) -> dict:
        """
        Cria uma nova tag na instância da empresa.

        Args:
            tag_name (str): Nome da tag a ser criada.
            tag_color (int | None): Cor da tag (opcional).

        Returns:
            dict: Dados da tag criada, ou dicionário vazio em caso de falha.
        """
        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/business/create-tag"
        requestbody = {
            "name": tag_name,
        }
        if tag_color:
            requestbody["color"] = tag_color
        response = req.post(url, json=requestbody, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            requestbody,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'POST',
            response,
            'create_tag',
            self.id_empresa,
            table="requests_zapi"
        )
        if response.status_code == 200:
            return response.json()
        return {}

    def edit_tag(
        self, tag_id: str, color: str | None, name: str | None
    ):
        """
        Edita uma tag na instância da empresa.

        Args:
            tag_id (str): ID da tag a ser editada.
            color (str | None): Cor da tag a ser editada.
            name (str | None): Nome da tag a ser editada.

        Returns:
            dict: Dados da tag criada, ou dicionário vazio em caso de falha.
        """
        if not any((name, color)):
            return {}

        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/business/edit-tag/{tag_id}"
        requestbody = {}

        if name:
            requestbody["name"] = name
        if color:
            requestbody["color"] = color

        response = req.post(url, json=requestbody, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            requestbody,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'POST',
            response,
            'create_tag',
            self.id_empresa,
            table="requests_zapi"
        )
        if response.status_code == 200:
            return response.json()
        return {}

    def add_tag_to_phone(
        self, phone: str, tag: int
    ) -> bool:
        """
        Adiciona uma tag a um telefone específico.

        Args:
            phone (str): Número do telefone do contato.
            tag (int): Identificador da tag a ser adicionada.

        Returns:
            bool: True se a tag foi adicionada com sucesso, False caso contrário.
        """
        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/chats/{phone}/tags/{tag}/add"
        response = req.put(url, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            None,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'PUT',
            response,
            'add_tag_to_phone',
            self.id_empresa,
            table="requests_zapi"
        )
        return response.status_code == 200 and response.json().get("value", False)

    def remove_tag_from_phone(
        self, phone: str, tag: int
    ) -> bool:
        """
        Adiciona uma tag a um telefone específico.

        Args:
            phone (str): Número do telefone do contato.
            tag (int): Identificador da tag a ser adicionada.

        Returns:
            bool: True se a tag foi adicionada com sucesso, False caso contrário.
        """
        instance_id, instance_token = get_from_empresa(self.id_empresa)
        base_url = f"https://api.z-api.io/instances/{instance_id}/token/{instance_token}"
        url = f"{base_url}/chats/{phone}/tags/{tag}/remove"
        response = req.put(url, headers={
            'Content-Type': 'application/json',
            'client-token': self.client_token
        })
        register_log(
            url,
            None,
            {'Content-Type': 'application/json', 'client-token': self.client_token},
            'PUT',
            response,
            'remove_tag_from_phone',
            self.id_empresa,
            table="requests_zapi"
        )
        return response.status_code == 200 and response.json().get("success", False)
