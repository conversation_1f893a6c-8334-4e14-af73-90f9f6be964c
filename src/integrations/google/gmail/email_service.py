import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from src.integrations.google.gmail.html import html_email
import os


logger = logging.getLogger("conversas_logger")

SMTP_SERVER = "smtp.mailgun.org"
SMTP_PORT = 587
SMTP_USERNAME = os.getenv("SMTP_USERNAME")
SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")

class GmailAPI:
    def __init__(self, smtp_server=SMTP_SERVER, smtp_port=SMTP_PORT, smtp_username=SMTP_USERNAME, smtp_password=SMTP_PASSWORD):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_username = smtp_username
        self.smtp_password = smtp_password

    def enviar_email_status(self, destinatario, assunto, url):
        """
        Envia o e-mail para o destinatário especificado.
        """
        try:
            msg = MIMEMultipart()
            msg["From"] = self.smtp_username
            msg["To"] = destinatario
            msg["Subject"] = assunto

            corpo_email = html_email

            corpo_email = corpo_email.replace("{{url}}", url)

            msg.attach(MIMEText(corpo_email, "html"))

            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.sendmail(self.smtp_username, destinatario, msg.as_string())
        except Exception as e:
            logger.error(f"Erro ao enviar e-mail: {e}")
