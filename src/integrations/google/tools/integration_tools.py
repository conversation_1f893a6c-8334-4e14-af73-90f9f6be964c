from pandas import Timestamp
import os
import requests as req

import json
from src.connections.connections import Connections
from src.data.bigquery_data import BigQueryData
from src.extras.util import register_log

connections = Connections().get_instance()
import logging
logger = logging.getLogger("conversas_logger")



class GoogleIntegrationTools:
    def __init__(self):
        self.maps_api = os.getenv("GOOGLE_MAPS_API")

    def get_address_info(self, address="", **kwargs) -> dict:
        if not address and not kwargs: return {}
        components = "|".join([f"{k}:{v}" for k, v in kwargs.items()])
        logger.info(f"Extracting address information from Google Maps API: {address} {components}")
        response = connections.redis_client.get(f"google_maps:{address}_{components}")
        if not response:
            url = "https://maps.googleapis.com/maps/api/geocode/json"

            parameters = {
                "address": address,
                "components": components,
                "key": self.maps_api,
            }

            response = req.get(url, params=parameters)

            if response.status_code == 200:
                logger.info(f"Maps API response sucessful")
                connections.redis_client.set(
                    f"google_maps:{address}_{components}",
                    json.dumps(response.json()),
                    ex=8*60*60
                )
                return response.json()
            else:
                logger.info(f"Maps API response failed")
                return {}
        else:
            return json.loads(response)
