import requests as req

from src.connections.connections import Connections
from src.data.bigquery_data import check_if_key_exists, redis_client_get, redis_client_set
from src.extras.util import register_log, register_indicator

from src.data.bigquery_data import BigQueryData as bq

import logging

connections = Connections.get_instance()

logger = logging.getLogger("conversas_logger")


class GymbotIntegrationTools:

    def __init__(self, id_empresa=None):
        logger.info("[GBIT] Inicializando GymbotIntegrationTools")
        self.id_empresa = id_empresa
        self.bq = bq(id_empresa=id_empresa)
        self.token =  self.bq.get_gymbot_token()
        logger.info("[GBIT] Token Gymbot: " + str(self.token))

    def get_conversa(self, session_id, includeDetails=[]):
        logger.info(f"[GBIT] Buscando conversa {session_id}")
        url = f"https://api.wts.chat/chat/v1/session/{session_id}?"

        details_list = []
        for detail in includeDetails:
            details_list.append(f"includeDetails={detail}")
        url = f"https://api.wts.chat/chat/v1/session/{session_id}?" + "&".join(details_list)

        headers = {
            "Authorization": self.token,
            "accept": "application/json"
        }

        response = req.get(url=url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_conversa", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info(f"[GBIT] Conversa encontrada {session_id}")

        return response_json
    

    def get_canais_atendimento(self, tipo_canal="Whatsapp"):
        logger.info(f"[GBIT] Buscando canais de atendimento {tipo_canal}")

        url = f"https://api.wts.chat/chat/v1/channel?ChannelType={tipo_canal}"
        
        headers = {
            "Authorization": self.token,
            "accept": "application/json"
        }

        response = req.get(url=url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_canais_atendimento", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info(f"[GBIT] Canais de atendimento encontrados {tipo_canal}")

        return response_json
    
    
    def get_contato(self, contato_id):
        logger.info(f"[GBIT] Buscando contato {contato_id}")

        url = f"https://api.wts.chat/core/v1/contact/{contato_id}"
        
        headers = {
            "Authorization": self.token,
            "accept": "application/json"
        }

        response = req.get(url=url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_contato", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info(f"[GBIT] Contato encontrado {contato_id}")


        return response_json
    
    def get_colaboradores(self):
        logger.info("[GBIT] Buscando usuários")

        url = "https://api.wts.chat/core/v1/agent"

        headers = {
            "accept": "application/json",
            "Authorization": self.token
        }

        response = req.get(url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_colaboradores", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info("[GBIT] Usuários encontrados")
        else:
            logger.info("[GBIT] Erro ao buscar usuários")

        return response_json
    
    def get_departamento(self) -> tuple[int, list[dict]]:

        logger.info("[GBIT] Buscando departamentos")
        url = "https://api.wts.chat/core/v1/department"

        headers = {
            "accept": "application/json",
            "Authorization": self.token
        }

        response = req.get(url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_departamento", self.id_empresa, table="requests_gymbot")
        if response.status_code == 200:
            logger.info("[GBIT] Departamentos encontrados")
            return response.status_code, response.json()
        logger.info(" [GBIT] Erro ao buscar departamentos")
        return response.status_code, []
    
    def transfer_user_departament(self, id_departament: str, id_session: str) -> tuple[int, dict]:
        logger.info(f"[GBIT] Transferindo para o departamento {id_departament}")
        # tenho atualizar o cache quando tranferir
        url = f"https://api.wts.chat/chat/v1/session/{id_session}/transfer"

        payload = {
            "type":"DEPARTMENT",
            "newDepartmentId": id_departament
        } 
        headers = {
            "accept": "application/json",
            "content-type": "application/*+json",
            "Authorization": self.token
        }

        response = req.put(url, json=payload, headers=headers)
        register_log(url, payload, headers, "PUT", response, "transfer_user_departament", self.id_empresa, table="requests_gymbot")
        if response.status_code == 200:
            logger.info(f"[GBIT] Transferido para o departamento {id_departament}")
            return response.status_code, response.json()

        logger.info(f"[GBIT] Erro ao transferir para o departamento")
        return response.status_code, {"ERRO": "Erro ao transferir para o departamento"}
    
    def create_webhook(self, events, name, url_webhook, enabled=False) -> tuple[int, dict]:
        url = "https://api.wts.chat/core/v1/webhook/subscription"
        
        payload = {
            "events": events,
            "name": name,
            "url": url_webhook,
            "enabled": enabled
        }
        
        headers = {
            "Authorization": self.token,
            "accept": "application/json",
            "content-type": "application/*+json"
        }
        
        response = req.post(url, json=payload, headers=headers)
        register_log(url, payload, headers, "POST", response, "create_webhook", self.id_empresa, table="requests_gymbot")
        if response.status_code == 200:
            logger.info("[GBIT] Webhook criado com sucesso")
            return response.status_code, response.json()
        
        logger.info("[GBIT] Erro ao criar webhook")
        return response.status_code, {"ERRO": "Erro ao criar webhook"}
    
    def get_sessao_por_contato(self, contato_id):
        logger.info(f"[GBIT] Buscando sessão por contato {contato_id}")
        url = f"https://api.wts.chat/chat/v1/session?ContactId={contato_id}"

        headers = {
            "Authorization": self.token,
            "accept": "application/json"
        }

        response = req.get(url=url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_sessao_por_contato", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info(f"[GBIT] Sessão encontrada para o contato {contato_id} - 1")
        else:
            return None
        
        last_page = int(response_json["totalPages"])

        url = f"https://api.wts.chat/chat/v1/session?ContactId={contato_id}&PageNumber={last_page}"

        headers = {
            "Authorization": self.token,
            "accept": "application/json"
        }

        response = req.get(url=url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_sessao_por_contato", self.id_empresa, table="requests_gymbot")
        response_json = response.json()
        if response.status_code == 200:
            logger.info(f"[GBIT] Sessão encontrada para o contato {contato_id} - 2")
        else: 
            return None

        return response_json["items"][-1]["id"]

    def get_webhooks(self) -> tuple[int, list[dict]]:

        logger.info("[GBIT] Buscando webhooks ativos")
        url = "https://api.wts.chat/core/v1/webhook/subscription"

        headers = {
            "accept": "application/json",
            "Authorization": self.token
        }

        response = req.get(url, headers=headers)
        register_log(url, {}, headers, "GET", response, "get_webhooks", self.id_empresa, table="requests_gymbot")
        if response.status_code == 200:
            logger.info("[GBIT] webhooks encontrados")
            return response.status_code, response.json()
        logger.info(" [GBIT] Erro ao buscar webhooks")
        return response.status_code, [{"ERRO": "Erro ao buscar webhooks"}]
    
    def enviar_mensagem(self, payload, headers):
        logger.info(f"[GBIT] Enviando mensagem from {payload['from']} to {payload['to']}")
        url = f"https://api.wts.chat/chat/v1/message/send-sync"

        response = req.post(url, json=payload, headers=headers)
        register_log(url, payload, headers, "POST", response, "enviar_mensagem", self.id_empresa, table="requests_gymbot")
        if response.status_code == 200:
            return response.status_code, response.json()

        return response.status_code, {"ERRO": "Erro ao enviar mensagem para sessão"}
