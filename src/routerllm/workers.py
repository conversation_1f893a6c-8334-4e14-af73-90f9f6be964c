from multiprocessing import Process
# from src.routerllm import messages_received_worker
from src.worker.workers import messages_received_worker
from src.extras.util import monitor_health
from src.worker.health_check.start_health_check import start_health_check_server

WORKERS = [
    messages_received_worker
]

def run_workers(redis_client):
    processes = []

    for worker in WORKERS:
        process = Process(target=monitor_health(worker.run), args=(redis_client,))
        processes.append(process)
        process.start()

    health_check_process = Process(target=start_health_check_server)
    health_check_process.start()
