# 📑 **Índice**  

1. [Pacto Conversas](#pacto-conversas)  
2. [Arquitetura](#arquitetura)  
   - [Arquitetura e Conteinerização](#arquitetura-e-conteinerização)  
   - [Camadas da Arquitetura](#camadas-da-arquitetura) 
   - [Imagens](#imagens)
   - [Variáveis de Ambiente](#Variáveis-de-ambiente)
   - [Fluxo da aplicação](#fluxo-da-aplicação)
3. [Como Rodar Localmente](#como-rodar-localmente)  
   - Clonar o repositório
   - Executar com Docker Compose
   - Acessar a API
   - Envio de mensagem
   - Executar testes automatizados
4. [Autenticação](#autenticação)  
5. [Ambiente de Homologação](#ambiente-de-homologação)  
6. [Código de Conduta](#código-de-conduta)  
   - [Criação de novas funcionalidades](#criação-de-novas-funcionalidades)  
   - [Nomeação da branch](#nomeação-da-branch)  
   - [Novas rotas de API](#novas-rotas-de-api)  
   - [Criação de um novo recurso para a API](#criação-de-um-novo-recurso-para-a-api)  
     - Blueprint
     - Task padrão
     - Documentação da rota 
     - Implementação dos métodos no BigQuery
     - Exemplo de Teste CRUD 
   - [Criação de novos functools/workers para LLM](#criação-de-novos-functools-workers-llm)
     - [Módulos Principais de Workers](#módulos-principais-de-workers)
     - [Padrão de Implementação de um Novo Worker](#padrão-de-implementação-de-um-novo-worker)
7. [Testes Automatizados](#testes-automatizados)


# Pacto Conversas

Este projeto tem como objetivo oferecer uma plataforma para gerenciar e conduzir conversas inteligentes, integrando diferentes módulos que compõem uma arquitetura de serviços distribuídos. A solução visa facilitar a criação, gerenciamento e monitoramento de fluxos de comunicação, garantindo escalabilidade e facilidade de manutenção.

---

# Arquitetura

A aplicação foi construída utilizando uma arquitetura baseada em microsserviços e conteinerização. Os principais pontos são:

## Arquitetura e Conteinerização

- **API Service:**  
  Gerencia requisições e expõe endpoints para interação com os clientes. Está configurada no serviço `api` do [<img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRLvXeiPMnc9s14KM6rZqYkRPp68psODZqFoW0VnrjiWoPZNwtbSW5OeSB7E0PcMIpfcbQ&usqp=CAU" alt="Docker" width="20"> docker-compose.yml](docker-compose.yml) e utiliza o [Dockerfile.api](Dockerfile.api).

- **Volumes Compartilhados:**  
  Os diretórios locais, como `src/api`, `src/connections`, `src/data`, `src/extras` e `src/integrations`, são montados nos contêineres para facilitar o desenvolvimento e integração contínua.

- **Outros Módulos:**  
  A estrutura do projeto também possui outros Dockerfiles (por exemplo, [Dockerfile.worker](Dockerfile.worker), [Dockerfile.monitoring](Dockerfile.monitoring)) e pastas dedicadas a workers, testes, e configurações de supervisão (como o `supervisord.conf`).

## Camadas da Arquitetura

- **Monitoring:**  
  Responsável por monitorar a saúde do ambiente, garantindo que os serviços e conexões estejam operando de forma estável.
  - **BigQuery:** Identificação e notificação à equipe sobre possíveis consultar problemáticas executadas no banco de dados principal.

- **Workers:**  
  Localizados na pasta `worker`, são dedicados à execução de operações que podem demandar mais tempo:
  - **bq_worker:** Realiza operações de consultas e registros no serviço BigQuery do Google Cloud.
  - **crm_worker:** Responsável por enviar mensagens de alunos e da IA para o módulo CRM do Sistema Pacto.
  - **health_check:** Fornece informações sobre a saúde dos workers utilizados na infraestrutura.
  - **instance_status:** Verifica se as instâncias estão conectadas ao Whatsapp e enviam notificações personalizadas aos clientes via E-mail e Whatsapp.
  - **messages_worker:** Gerencia o envio de mensagens aos clientes, sendo o principal fluxo de processamento da aplicação.
  - **route_llm:** Gerencia o envio de mensagens com um roteador de modelos de LLM, para reduzir custos, migrando entre um modelo melhor e mais caro para mensagens complexas, e um modelo menor e mais barato para mensagens mais simples.

- **Connections:**  
  Responsável por gerenciar conexões:
  - **BigQuery**, para operações no banco de dados e utilização de buckets para dados temporários.
  - **Redis**, para gerenciamento de cache. 
  - **RQ (RedisQueue)**, para gerenciamento de filas.
  - **OpenAI**, para geração de respostas da IA.

- **Extras:**  
  Conjunto de utilitários para formatação de textos, datas, entre outros.

- **LLM Modules:**  
  Responsável pela comunicação com modelos LLM, facilitando o processamento e geração de textos de forma inteligente.

- **Messager Modules:**  
  Gerencia o envio, registro e administração de mensagens das conversas da IA.

## Imagens

- **API:**  
    Este container contém a lógica para comunicação com os endpoints da API, gerenciando o fluxo de dados entre o sistema e os usuários ou serviços externos.
  - Imagem: 
    `registry.gitlab.com/plataformazw/ia/orion/api:main`  

- **Worker:** 
    Responsável por lidar com o fluxo interno do envio de mensagens e gerenciar o fluxo de dados no banco. Executa funções essenciais para atualizar dados e enviar mensagens. 
  - Imagem:  
    `registry.gitlab.com/plataformazw/ia/orion/worker:main`

- **Routellm:**  
    Gerencia o envio de mensagens por meio de um roteador de modelos de LLM. Esse container alterna entre um modelo mais robusto (e caro) para mensagens complexas e um modelo mais simples (e econômico) para mensagens menos exigentes.
  - Imagem:  
    `registry.gitlab.com/plataformazw/ia/orion/routellm:main`

- **Monitoring:** 
    Monitora o sistema e envia notificações sobre atividades que possam comprometer o funcionamento do ambiente, com foco especial em identificar consultas problemáticas no banco de dados.
  - Imagem:  
    `registry.gitlab.com/plataformazw/ia/orion/monitoring:main`


- **Redis:**  
    Responsável por fornecer a conexão com o Redis, que é utilizado para caching, filas e armazenamento temporário de dados. O Redis é um sistema de armazenamento em memória, baseado em estrutura de dados chave-valor, que melhora a performance e a escalabilidade do sistema.
  - Imagem:  
    `redis`

- **Locust:** 
    Executa o Locust, uma ferramenta de testes. Este container simula o comportamento dos usuários para avaliar o desempenho do sistema sob diferentes níveis de tráfego. 
  - Imagem:
    `registry.gitlab.com/plataformazw/ia/orion/test:main`

- **RedisInsight:**  
    Fornece uma interface gráfica para monitoramento e gerenciamento do Redis, facilitando a visualização dos dados e a análise do desempenho do serviço Redis.
  - Imagem: 
    `redislabs/redisinsight:latest`

## Variáveis de Ambiente
Nessa seção, iremos detalhar as variáveis de ambiente utilizadas no containers. Essas variáveis configuram o comportamento da aplicação, permitindo ajustes conforme o ambiente e necessidades do projeto

### Variáveis - API: 

| Variável              | Descrição                                                                                                                                                    | Valores Válidos                                                      | Valor Padrão                                                      | Exemplo                                                             |
|-----------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|------------------------------------------------------------------|---------------------------------------------------------------------|
| **SERVER_PORT**        | Define a porta em que a API será exposta, direcionando as requisições externas para o serviço correto.                                                     | Número da porta (ex: 8081, 8082, 8083, 3000, 5000, 8000...)        | 8080 (obrigatória)                                               | `SERVER_PORT=8080`                                                  |
| **REDIS_URL**          | Especifica a URL de conexão com o Redis, utilizado para caching, filas e outras operações em memória.                                                      | No formato `redis://<hostname>:<porta>/<db>`                        | `redis://redis:6379/0`                                            | `REDIS_URL=redis://redis:6379/0`                                     |
| **FLASK_ENV**          | Determina o ambiente de execução da aplicação Flask, influenciando configurações como debug e recarregamento automático.                                     | `development`, `production`, `loadtesting`                         | `development` (não obrigatória – caso não definida, assume "development") | `FLASK_ENV=development`                                              |
| **WAITRESS_THREADS**   | Especifica o número de threads que o servidor Waitress utilizará para processar requisições, melhorando a capacidade de atendimento em ambientes de alta carga. | Número inteiro                                                     | 4                                                                | `WAITRESS_THREADS=4`                                                |
| **API_MASTER_KEY**     | Chave utilizada para autenticar requisições sensíveis na API, garantindo acesso seguro aos recursos protegidos.                                            | Qualquer string que funcione como uma chave segura (geralmente um UUID ou token similar) | Não há valor padrão seguro; é obrigatória a configuração de uma chave exclusiva | `API_MASTER_KEY=5eea8f6d-8001-4165-ad00-e1c8543b520b3e68fa15-886c-4491-b057-4b27b17df562` |
| **AUTH_ON**            | Determina se a autenticação está ativada na aplicação. Quando `true`, a API exige autenticação para acessar recursos protegidos.                             | `true` ou `false`                                                  | `true`                                                           | `AUTH_ON=true`                                                      |
| **GOOGLE_LOGIN_DOC**   | Determina se o login via Google é necessário para acessar a documentação do Swagger, aumentando a segurança no ambiente de produção.                         | `true` ou `false`                                                  | `false`                                                          | `GOOGLE_LOGIN_DOC=false`                                             |
| **DOMAIN**             | Define o domínio base do sistema, utilizado para construir URLs e redirecionamentos dentro da aplicação.                                                     | URL válida                                                         | `https://orion.pactosolucoes.com.br`                               | `DOMAIN=https://orion.pactosolucoes.com.br`                         |
| **GLOBAL_RATE_LIMIT**  | Define o limite global de requisições permitidas por usuário/IP para a API, ajudando a controlar o tráfego e prevenir abusos.                               | No formato de string, como `60/minute` ou `100/hour`                | `60/minute`                                                      | `GLOBAL_RATE_LIMIT=60/minute`                                       |
| **MODE**               | Indica o modo de operação do container.                                                                                                                        | `api`, `worker`                                                    | -                                                                | `MODE=api`                                                         |
| **ALLOWED_ORIGINS**    | Especifica as origens permitidas para acessar a API, controlando as políticas de CORS.                                                                     | URLs válidas separadas por vírgula (ex.: 'https://api.z-api.io, http://localhost:8080, ************') | -                                                                | `ALLOWED_ORIGINS=https://api.z-api.io, http://localhost:8080, ************` |
| **Z_API_CLIENT_TOKEN** | Token utilizado para autenticar a comunicação com a Z-API, permitindo o envio de mensagens via WhatsApp.                                                   | String segura                                                      | Obrigatório                                                     | `Z_API_CLIENT_TOKEN=F3b14fd459eb9462ebd51b359510e2c4dS`              |
| **GYMBOT**             | Token utilizado para autenticar e enviar mensagens via GymBot, o sistema de comunicação via WhatsApp.                                                     | String segura                                                      | Obrigatório                                                     | `GYMBOT=pn_nyxJdLBJZk5raQzkiSVsjxmdpe7XwqvBJt8vBqdNlTU`             |
| **RETRY_MESSAGE**      | Indica se mensagens de retry (tentativas de reenvio) serão disparadas caso o usuário não responda à interação inicial.                                        | `true` ou `false`                                                  | `false`                                                          | `RETRY_MESSAGE=false`                                               |
| **BUCKET_NAME_AUDIO**  | Define o nome do bucket de armazenamento utilizado para arquivos de áudio enviados pela aplicação.                                                         | String                                                             | `temporary_audios`                                               | `BUCKET_NAME_AUDIO=temporary_audios`                                 |

### Variáveis - Worker: 
***Algumas variáveis já foram explicadas na seção "Variáveis-API" acima. Para evitar redundância, recomenda-se verificar essa seção antes de consultar as descrições neste bloco.***

| Variável                     | Descrição                                                                                                                                                    | Valores Válidos                                                      | Valor Padrão                                                      | Exemplo                                                             |
|------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------|------------------------------------------------------------------|---------------------------------------------------------------------|
| **NUM_THREADS**               | Define o número de threads utilizadas no processamento do sistema.                                                                                          | Número inteiro (ex.: 4, 8, 16)                                      | 4                                                                | `NUM_THREADS=4`                                                     |
| **Z_API_INSTANCE_STATUS_CHECK** | Indica se o sistema deve verificar e notificar para os clientes se sua instância do Z-API está desconectada. Em produção, essa variável deve estar sempre definida como `false`. | `true` ou `false`                                                  | `false`                                                          | `Z_API_INSTANCE_STATUS_CHECK=false`                                 |
| **Z_API_INSTANCE_STATUS_CHECK_INTERVAL** | Define o intervalo, em horas, para a verificação do status das instâncias de conexão do Z-API.                                                                | Número inteiro representando horas                                 | 2                                                                | `Z_API_INSTANCE_STATUS_CHECK_INTERVAL=2`                            |
| **CHECK_PENDENCY**            | Indica se o sistema deve verificar pendências na conversa do WhatsApp.                                                                                      | `true` ou `false`                                                  | `true`                                                           | `CHECK_PENDENCY=true`                                               |
| **PENDENCY_VERIFICATION_TIME** | Define o intervalo, em minutos, para verificar se há alguma pendência na conversa do WhatsApp com o cliente.                                                | Número inteiro (ex.: 1, 5, 10)                                      | 5                                                                | `PENDENCY_VERIFICATION_TIME=5`                                      |
| **ROLES_TO_KEEP_REDIS**       | Define quais os nomes dos papéis das conversas devem ser armazenados no Redis.                                                                               | Lista separada por vírgulas contendo os papéis válidos (ex.: "assistant", "user", "system") | `"assistant, user, system"`                                       | `ROLES_TO_KEEP_REDIS="assistant, user, system"`                     |
| **LINK_APP_TREINO**           | URL para download do aplicativo Treino.                                                                                                                     | URL válida                                                         | `https://apptreino.com.br/#baixar`                                | `LINK_APP_TREINO="https://apptreino.com.br/#baixar"`                 |
| **HEALTHCHECK_PORT**          | Define a porta utilizada para o health check do sistema.                                                                                                    | Número de porta registrado (ex.: 8081, 8082, 8083, 3000, 5000, 8000...) | 8081                                                             | `HEALTHCHECK_PORT=8081`                                             |
| **RETRY_CONTACT_TIME**        | Define após quantos minutos deve ser feito um novo contato com o usuário após o último contato.                                                              | Número inteiro                                                     | 1                                                                | `RETRY_CONTACT_TIME=1`                                              |
| **CONVERSAS_ENABLED_WORKERS** | Define quais workers serão executados pelo sistema. Pode ser uma lista separada por vírgulas ou o valor especial `all` para executar todos os workers.       | Lista de nomes de workers ou `all`                                 | `all`                                                            | `CONVERSAS_ENABLED_WORKERS=messages_received,messages_to_send`      |


#### Workers Disponíveis:
Os seguintes workers estão disponíveis para configuração na variável `CONVERSAS_ENABLED_WORKERS`:

- **messages_received**: Gerencia mensagens recebidas e processa respostas automáticas.
- **messages_to_send**: Gerencia o envio de mensagens para os clientes.
- **crm**: Integra mensagens com o módulo CRM.
- **updates**: Realiza atualizações no banco de dados.
- **logs**: Processa e armazena logs de mensagens.
- **delayed_queue**: Gerencia tarefas agendadas.
- **api_keys**: Gerencia chaves de API.
- **instance_status**: Verifica o status das instâncias conectadas.
- **transfer_conversation**: Gerencia transferências de conversas entre departamentos.
- **message_sent**: Processa mensagens enviadas para o sistema.
- **context_updates**: Atualização dos dados de forma periódica.


### Variáveis - Routerllm: 
***Algumas variáveis já foram explicadas na seção "Variáveis-API" e "Variáveis-Worker" acima. Para evitar redundância, recomenda-se verificar essas seções antes de consultar as descrições neste bloco.***

| Variável           | Descrição                                                                                                          | Valores Válidos                                                       | Valor Padrão                                                   | Exemplo                                                                |
|--------------------|--------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|---------------------------------------------------------------|------------------------------------------------------------------------|
| **ROUTER_TYPE**     | Define o tipo de roteador que será usado para distribuir as requisições entre diferentes modelos de LLM.            | Qualquer string representando o tipo de roteador desejado           | "mf"                                                          | `ROUTER_TYPE=mf`                                                     |
| **ROUTER_THRESHOLD** | Define qual modelo será considerado o mais fraco no balanceamento de carga.                                        | "GEMINI", "TOGETHER" ou outro modelo configurado                     | "GEMINI"                                                      | `WEAKER_MODEL=GEMINI`                                                 |
| **STRONGER_MODEL**  | Define qual modelo será considerado o mais forte no balanceamento de carga.                                        | "OPEN_AI", "GEMINI", "TOGETHER" ou outro modelo configurado          | "OPEN_AI"                                                     | `STRONGER_MODEL=OPEN_AI`                                              |
| **OPEN_AI**         | Nome do modelo OpenAI usado no sistema.                                                                             | Qualquer nome de modelo OpenAI configurado                           | "openai/gpt-4o-mini"                                           | `OPEN_AI="openai/gpt-4o-mini"`                                        |
| **GEMINI**          | Nome do modelo Gemini usado no sistema.                                                                             | Qualquer nome de modelo Gemini configurado                           | "gemini/gemini-2.0-flash"                                      | `GEMINI="gemini/gemini-2.0-flash"`                                    |
| **TOGETHER**        | Nome do modelo Together AI usado no sistema.                                                                        | Qualquer nome de modelo Together AI configurado                      | "together_ai/togethercomputer/LLaMA-2-7B-32K"                  | `TOGETHER="together_ai/togethercomputer/LLaMA-2-7B-32K"`              |


### Variáveis - Monitoring: 
***Algumas variáveis já foram explicadas na seção "Variáveis-API" e "Variáveis-Worker" acima. Para evitar redundância, recomenda-se verificar essa seção antes de consultar as descrições neste bloco.***

| Variável               | Descrição                                                                                      | Valores Válidos                                        | Valor Padrão                                                | Exemplo                                                                                   |
|------------------------|------------------------------------------------------------------------------------------------|------------------------------------------------------|------------------------------------------------------------|-------------------------------------------------------------------------------------------|
| **BIGQUERY_NOTIFICATION** | Ativa ou desativa notificações sobre consultas problemáticas no BigQuery.                                                            | true ou false | false                                                      | `BIGQUERY_NOTIFICATION=false`                                                            |
| **TZ**                  | Define o fuso horário da aplicação                     | Qualquer fuso horário válido conforme a base de dados IANA                                         | "UTC"                                                       | `TZ="America/Sao_Paulo"`                                                                  |
| **SMTP_USERNAME**       | Nome de usuário utilizado para autenticação no servidor SMTP.                                  | Endereço de e-mail válido                             | "<EMAIL>"                         | `SMTP_USERNAME="<EMAIL>"`                                        |
| **SMTP_PASSWORD**       | Senha utilizada para autenticação no servidor SMTP.                                            | String representando a senha associada ao usuário SMTP | "a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"      | `SMTP_PASSWORD="a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"`                      |

## Fluxo da aplicação

```mermaid
flowchart TD
    %% Camada de Entrada
    CLI[Cliente]
    AUT[Autenticação]
    API[API Service]
    CLI -->|Requisição HTTP| AUT
    AUT --> API

    %% Camadas Internas da Aplicação
    subgraph "Camadas Internas"
        MON[Monitoring]
        WRK[Workers]
        CON[Connections]
        EX[Extras]
        LLM[LLM Modules]
        MSG[Messager Modules]
    end

    %% Comunicação entre API e Camadas Internas
    API --> MON
    API --> WRK
    API --> CON
    API --> EX
    API --> LLM
    API --> MSG

    %% Detalhamento dos LLM Modules
    subgraph "LLM Modules Detalhados"
        RLLM[routerLLM]
        LLMModel[LLM Model]
    end
    API -->|Envia mensagem para LLM| RLLM
    RLLM -->|Seleciona modelo| LLMModel
    LLMModel -->|Gera resposta| RLLM
    RLLM -->|Retorna resposta à API| API

    %% Detalhamento dos Workers (exceto Messages Workers)
    subgraph "Workers Detalhados"
        BQ[bq_worker]
        CRM[crm_worker]
        HC[health_check]
        IS[instance_status]
    end
    WRK --> BQ
    WRK --> CRM
    WRK --> HC
    WRK --> IS

    %% Integração com Serviços Externos
    BQ_EXT[BigQuery]
    RED[Redis]
    BQ -->|Consulta/Registro| BQ_EXT
    CON -->|Gerencia cache, filas - RQ e conexoes - OpenAI| RED

    %% Enfileiramento e Processamento de Tarefas - Lógica para messages_worker
    API -->|Envia tarefa à fila| RED
    RED --> D1[Decide: Modelo Específico?]
    D1 -- Sim --> MW_N[messages_worker Normal]
    D1 -- Não --> MW_R[messages_worker RouteLLM]

    %% Roteamento interno simplificado para messages_workers
    MW_N -->|Decide: Rede ou Academia| API
    MW_R -->|Decide: Rede ou Academia| API

    %% Monitoramento dos Serviços
    MON -->|Monitora BigQuery| BQ_EXT
    MON -->|Monitora Redis| RED

    %% Notificações do Instance Status
    IS -->|Envia notificações - Email e Whatsapp| API
  ```

# Como Rodar Localmente

Para executar o projeto localmente, siga os passos abaixo:

1. **Clonar o repositório:**

   ```sh
   <NAME_EMAIL>:Plataformazw/ia/orion.git
   cd orion
2. **Executar com Docker Compose:**

  - O arquivo [<img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRLvXeiPMnc9s14KM6rZqYkRPp68psODZqFoW0VnrjiWoPZNwtbSW5OeSB7E0PcMIpfcbQ&usqp=CAU" alt="Docker" width="20"> docker-compose.yml](docker-compose.yml) define os serviços e volumes necessários. Para iniciar a aplicação, execute:
    ```sh
    docker compose up
    ```
  - Ou, para rodar apenas alguns containers específicos, há um script utilitário.
  - Uso:
    ```sh
    python development/entrypoint.py --help    
    usage: entrypoint.py [-h] [--run] [--no_router] [--no_locust] [--with_monitor]

    Run Conversas.AI locally

    options:
      -h, --help           show this help message and exit
      --run, -r            Run Conversas.AI locally
      --no_router, -nr     Run Conversas.AI locally without the router
      --no_locust, -nl     Run Conversas.AI locally without Locust
      --with_monitor, -wm  Run Conversas.AI locally with BigQuery monitoring
3. **Acessar a API**:

- Com o serviço api ativo, a API estará disponível na porta configurada (por exemplo, 8080).

- [Documentação da API](http://localhost:8080/apidocs)
  - Através da documentação da API, podem ser executados testes nas rotas que controlam os dados do sistema.

4. **Envio de mensagem**:
- Para testar o envio de mensagem, é necessário simular uma requisição ao webhook que recebe as mensagens na sua máquina local:
  - Vá ao arquivo `test/send_message.py` e coloque o seu número de telefone na linha 22.
  - Rode o arquivo:
    ```python
    python test/send_message.py "Sua mensagem"
  - Isso irá simular uma conversa com a IA.

5. **Executar testes automatizados**:
- Existe um script para rodar os testes com base em argumentos.

- Uso: 

  ```sh
  tests.py [-h] [--unit] [--integration] [--workers] [--api] [--functionalities] [--verbose] [--coverage]

  Run tests for Conversas.AI

  options:
    -h, --help            show this help message and exit
    --unit, -u            Run unit tests
    --integration, -i     Run integration tests
    --workers, -w         Run Worker tests
    --api, -a             Run API tests
    --functionalities, -f
                          Run functionalities tests
    --verbose, -v         Run tests in verbose mode

# Autenticação
Todas as rotas da API requerem um de dois tipos de autenticação:
- **Sessão por chave de API**: O utilizador deverá trocar uma chave de api por um token de autenticação com expiração automática após *8 horas*.
  - Essa forma de autenticação foi desenvolvida para comunicação com sistemas integradores e requer explicitação de um ID relacionado à empresa que está realizando operações.

- **Token de usuário do Firebase**: O utilizador poderá utilizar as rotas referentes à integração com o Z-API e as rotas da `V2` da API (Para integração com o App Conversas.AI).
  - Essa forma de autenticação identifica através do token qual usuário está se comunicando com a API, então não requer IDs específicos de usuário nos parâmetros da requisição.

- A **documentação da API** requer uma autorização com uma conta da Google do domínio `@pactosolucoes.com.br`.

# Ambiente de homologação

A infraestrutura do projeto oferece um ambiente preparado para apontar para alguma branch específica, que irá rodar como se estivesse em produção.

- **Uso:**
Para acessar esse ambiente, é necessário acessar a plataforma da infra e apontá-lo para a branch que será validada.

- **Acesso:**
Os controles do ambiente de homologação são feitos de forma análoga ao projeto em produção, através da rota: [https://orion-hml.pactosolucoes.com.br/](https://orion-hml.pactosolucoes.com.br/)

[Documentação da API](https://orion-hml.pactosolucoes.com.br/apidocs)

# Código de conduta
## Criação de novas funcionalidades
Devido à complexidade dos processos presentes na aplicação, é necessário que toda nova feature mantenha a **retrocompatibilidade**.

É recomendado seguir ao máximo os princípios do [Zen of Python (Padrão PEP)](https://peps.python.org/pep-0020/).

[Referência.](https://testdriven.io/blog/clean-code-python/)

<details>
  <summary>Zen of Python</summary>
  <pre><code>
Bonito é melhor que feio.
Explícito é melhor que implícito.

Simples é melhor que complexo.
Complexo é melhor que complicado.

Plano é melhor que aninhado.
Esparso é melhor que denso.

Legibilidade conta.

Casos especiais não são especiais o suficiente para quebrar as regras.
Embora a praticidade vença a pureza.

Erros nunca devem passar silenciosamente.
A menos que explicitamente silenciados.

Diante da ambiguidade, recuse a tentação de adivinhar.

Deve haver uma — e de preferência apenas uma — maneira óbvia de fazer as coisas.
Embora essa maneira possa não ser óbvia no início, a menos que você seja holandês.

Agora é melhor que nunca.
Embora nunca seja frequentemente melhor que *agora*.

Se a implementação for difícil de explicar, é uma má ideia.
Se a implementação for fácil de explicar, pode ser uma boa ideia.

Namespaces são uma ótima ideia — vamos fazer mais disso!
</code></pre>
</details>

## Nomeação da branch
Toda branch deve estar relacionada a algum **Ticket no JIRA**, e ela deve ser nomeada seguindo o identificador do ticket, e o tipo de alteração que é proposto na branch.
- A branch pode ser nomeada com os seguintes prefixos: 
  - `feature/`: Para adição de nova funcionalidade.
  - `bugfix/`: Para correção de algum bug.
  - `hotfix/`: Para correção urgente de bug que impacta no produto.
  - `refactor/`: Para refatorações.
  - `migration/`: Para migrações.
- O sufixo da branch, deve ser apenas o **identificador** do ticket.
- É ideal que os textos do commit sempre começem com "Fix:" ou "Feat:", e tenham uma descrição básica da adição/correção.
- Exemplo, bugfix para o ticket PRPI-1:
  ```sh
  git checkout -n bugfix/PRPI-1
  git add .
  git commit -m "Fix: Descrição da correção"
## Novas rotas de API
Caso seja necessário, podem ser criadas novas rotas de API para aplicação.
- As rotas legado do sistema não estão padronizadas, porém é necessário que as novas rotas sigam o padrão REST em sua implementação, para melhorar a legibilidade e interpretabilidade da API.
  - [Referência.](https://auth0.com/blog/best-practices-for-flask-api-development/)

- Todas as rotas novas criadas deverão ser colocadas no caminho: `src/api/app/routes/v2`
- Todas as rotas devem utilizar as seguintes anotações:
<details>
<summary>Anotações</summary>

```python
@blueprint.route('', methods=[...])
@limiter # Para ativar o Rate Limit
@authentication_required # Para ativar a autenticação obrigatória.
```
</details>

### Criação de um novo **recurso** para a API:
Quando for necessário criar um recurso completo para a API, ele deve ser implementado em um arquivo `<nome-do-recurso>.py` na pasta específica com o nome do recurso.
- Deve ser criado apenas um blueprint para a página seguindo o padrão `<nome-do-recurso>_bp`.
- Nesse blueprint, devem ser adicionados todos os métodos para o recurso em funções separadas.
- Todo processo de atualização de dados deve ser passado para a fila `api-v2` do Redis, seguindo o padrão:
<details>
<summary>Task padrão</summary>

```python
task = {
    "type": "api-v2",
    "id_empresa": id_empresa,
    "data": data, # dado a ser processado
    "action": "create", # pode ser create, update, patch ou delete
    "resource": <nome-do-recurso> 
}
```
</details>

- Para que a documentação apareça corretamente no Swagger, é necessário incluir na docstring da função um bloco YAML estruturado. Esse YAML deve definir, por exemplo, as descrições da rota, os parâmetros esperados, os modelos de dados (em definitions) e as respostas (em responses). Essa estrutura padronizada permite que ferramentas como o Swagger leiam a docstring e gerem a documentação interativa da API automaticamente, eg:
<details>
<summary>Documentação da rota</summary>

```yml
Descrição da rota
---
tags:
  - Exemplo
parameters:
  - in: body
    name: body
    required: true
    description: Dados de entrada para a operação.
    schema:
      $ref: '#/definitions/ExemploModel'
responses:
  200:
    description: Operação realizada com sucesso.
    schema:
      type: object
      properties:
        success:
          type: string
          example: "Operação realizada com sucesso"
  400:
    description: Erro de validação.
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Dados inválidos"
```
</details>

- Para finalizar parte da API, deve-se importar o *blueprint* criado no arquivo `src/api/app/__init__.py` e adicioná-lo com o método `app.register_blueprint(<nome-do-recurso>_bp, url_prefix='/<nome-do-recurso>')`
- Após isso, é necessário adicionar a implementação desses métodos no arquivo `src/data/bigquery_data.py` na classe `BigQueryData`.
  - A implementação deve compor todos os ***actions*** criados para o recurso seguindo o padrão: `<action>_<recurso>`
- Por fim, deve ser feito o teste de um CRUD completo para esse recurso, verificado:
  - Criação e cacheamento (create).
  - Busca com cache-hit e sem cache-hit (get).
  - Atualização do recurso completo (update).
  - Atualização parcial do recurso (patch).
  - Deleção do recurso no cache e no banco de dados principal (delete).
- Ex. com o recurso `produto.py`:
<details>
<summary>Arquivo do recurso (`produto.py`)</summary>

```python
from flask import Blueprint, request, current_app
import json

from src.api.app.auth.utils.auth_wrappers import authentication_required
from src.api.app.limiter.limiter import limiter
from src.data.bigquery_data import BigQueryData as bq

produto_bp = Blueprint('produto', __name__)

@produto_bp.route('', methods=['GET'])
@limiter
@authentication_required
def get_produto(id_empresa: str):
    """
    Documentação
    """
    bq_ = bq(id_empresa=id_empresa)
    produto = bq_.get_produto()  # método que deve ser implementado na classe BigQueryData
    if not produto:
        return {"error": "Produto não encontrado"}, 404

    return produto, 200

@produto_bp.route('', methods=['POST'])
@limiter
@authentication_required
def create_produto(id_empresa: str):
    """
    Documentação
    """
    required = ["nome", "preco", "descricao"]
    data = request.json

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    if not id_empresa:
        return {"error": "Erro ao criar produto"}, 400

    task = ...

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 201

@produto_bp.route('', methods=['PUT'])
@limiter
@authentication_required
def update_produto(id_empresa: str):
    """
    Documentação
    """
    required = ["nome", "preco", "descricao"]
    data = request.json

    for key in required:
        if key not in data or not data.get(key):
            return {"error": f"Dados inválidos: {key}"}, 400

    task = ...

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200

@produto_bp.route('', methods=['PATCH'])
@limiter
@authentication_required
def patch_produto(id_empresa: str):
    """
    Documentação
    """
    data = request.json

    task = ...

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200

@produto_bp.route('', methods=['DELETE'])
@limiter
@authentication_required
def delete_produto(id_empresa: str):
    """
    Documentação
    """
    task = ...

    current_app.redis_client.rpush("task_queue_bd_contexts", json.dumps(task))

    return {
            "success": "success",
            "id_empresa": id_empresa
    }, 200
```

</details>
<details>
<summary>Implementação dos métodos no BigQuery (`src/data/bigquery_data.py`)</summary>

```python
class BigQueryData:
    def get_produto(self, data):
        # Implementação da busca do produto no BigQuery e limpeza do cache
        print("Buscando produto no BigQuery:", data)
        # Código de busca no BigQuery e cache aqui

    def create_produto(self, data):
        # Implementação da criação do produto no BigQuery
        print("Criando produto no BigQuery:", data)
        # Código de inserção no BigQuery aqui

    def update_produto(self, data):
        # Implementação da atualização completa do produto
        print("Atualizando produto no BigQuery:", data)
        # Código de update no BigQuery aqui

    def patch_produto(self, data):
        # Implementação da atualização parcial do produto
        print("Realizando patch no produto no BigQuery:", data)
        # Código de patch no BigQuery aqui

    def delete_produto(self, data):
        # Implementação da deleção do produto no BigQuery e limpeza do cache
        print("Deletando produto no BigQuery:", data)
        # Código de deleção no BigQuery e cache aqui
```

</details>
<details>
<summary>Exemplo de Teste CRUD</summary>

```bash
# Criação do produto (create)
curl -X POST -H "Content-Type: application/json" \
-d '{"id_empresa": 123, "nome": "Produto A"}' \
http://localhost:8080/produto

# Busca do produto (get)
curl http://localhost:8080/produto/1

# Atualização completa do produto (update)
curl -X PUT -H "Content-Type: application/json" \
-d '{"id_empresa": 123, "nome": "Produto A Atualizado"}' \
http://localhost:8080/produto/1

# Atualização parcial do produto (patch)
curl -X PATCH -H "Content-Type: application/json" \
-d '{"id_empresa": 123, "nome": "Produto A Patch"}' \
http://localhost:8080/produto/1

# Deleção do produto (delete)
curl -X DELETE -H "Content-Type: application/json" \
-d '{"id_empresa": 123}' \
http://localhost:8080/produto/1
```

</details>

## Criação de novos functools/workers para LLM

Caso seja necessário, podem ser criadas novas funcionalidades para serem utilizadas pelos modelos de linguagem (LLMs) da aplicação. Chamamos essas funcionalidades de "workers" ou "functools".

Para garantir a consistência e a correta integração com os diferentes modelos de LLM, é fundamental que a criação de novos workers siga o padrão estabelecido abaixo.

### Módulos Principais de Workers

Atualmente, o projeto conta com três módulos principais onde os workers são implementados, um para cada provedor de LLM:

  - `src/worker/llm_modules/openai/openai_response_module.py`
  - `src/worker/llm_modules/gemini/gemini_response_module.py`
  - `src/worker/llm_modules/llama/llama_response_module.py`

A criação de uma nova funcionalidade deve ser replicada nos três arquivos para garantir a compatibilidade entre os modelos.

### Padrão de Implementação de um Novo Worker

Para criar e integrar um novo worker, siga os três passos a seguir:

#### Crie a Função do Worker

Primeiramente, implemente a lógica da sua funcionalidade como uma função (método) dentro das classes das LLMs (`OpenAIResponseModule`, `GeminiResponseModule` e `LlamaResponseModule`).

É crucial que a função tenha uma *docstring* clara e detalhada, explicando seu propósito, os parâmetros que recebe e o que retorna. A *docstring* é utilizada pelo LLM para entender como e quando utilizar a ferramenta.

**Exemplo: Função `book_service`**

```python
def book_service(
    self,
    codigo_horario: int,
    data: str,
    horario_inicial: str,
    horario_final: str,
    professor_id: int,
    tipo_agendamento_id: int
) -> str:
    """
    Agenda o serviço para o usuário após ele confirmar o horário desejado.
    **NUNCA use esta função sem antes confirmar a disponibilidade com 'check_disponibilidade_agenda_servicos'.**
    Use os dados EXATOS retornados pela função de verificação.

    :param int codigo_horario: O 'codigo_horario' retornado pela função de verificação.
    :param str data: A data do agendamento no formato 'YYYYMMDD'.
    :param str horario_inicial: O horário de início (ex: '19:00').
    :param str horario_final: O horário final (ex: '20:00').
    :param int professor_id: O ID do professor retornado pela verificação.
    :param int tipo_agendamento_id: O ID do tipo de agendamento.
    """
    user_data = json.loads(self.user_context)
    aluno_id = user_data.get("aluno", {}).get("id", None)

    if not aluno_id:
        return "Não consegui identificar o aluno. Por favor, confirme o CPF do aluno para continuar com o agendamento."

    # Dados de agendamento exemplo para o POST
    dados_agendamento = {
        "alunoId": aluno_id,
        "data": data,
        "horarioInicial": horario_inicial,
        "horarioFinal": horario_final,
        "professor": professor_id,
        "tipo": tipo_agendamento_id,
        "horarioDisponibilidadeCod": codigo_horario,
        "status": "CONFIRMADO",
        "observacao": "Agendado via Inteligência Artificial Generativa."
    }

    result_str = self.pit.agendar_servicos(dados_agendamento)
    result = json.loads(result_str)

    if result.get("success"):
        register_indicator("agendamento_servico", self.id_empresa, telefone=self.telefone)
        # FIXME: a lógica de notificação pode ser adicionada aqui similar a função 'book_class'
        return result.get("message")
    else:
        return f"Houve um problema ao tentar agendar: {result.get('message')}"
```

#### Adicione a Função na Inicialização

Após criar a função, você deve registrá-la na lista de funções disponíveis para o LLM. Adicione sua nova função à lista `self.function_descriptions` dentro do método `_init_functions` da classe.

**Exemplo: Adicionando a função à lista**

```python
def _init_functions(self):
    context = json.loads(self.user_context)
    if isinstance(context, list):
        context = context[0]
    elif isinstance(context, str):
        context = json.loads(context)
    
    situacao = context.get("aluno", {}).get("situacao", None)

    if situacao is None:
        situacao = "LEAD"
    elif isinstance(situacao, dict):
        situacao = situacao.get("codigo", "LEAD")

    self.function_descriptions = [
        self.warn_user,
        self.end_conversation
    ]
    
    if self.id_empresa is not None:
        self.function_descriptions.extend([
            self.get_additional_context,
            self.save_user_level,
            self.save_user_birthdate,
            self.check_classes_day,
            self.check_class_details,
            self.book_class,
            self.book_call,
            self.get_servicos_disponiveis,
            self.book_service, # <-- Adicione sua nova função na _init_functions, onde for conveniente
            # ... continua a lógica da função _init_functions
        ])
```

#### Adicione o Tratamento de Chamada da Função

Por fim, adicione a lógica para que o sistema execute sua função quando o LLM decidir usá-la. No método `process_function_call`, adicione um novo bloco `elif` para o `function_name` da sua função, garantindo que ela seja chamada com os argumentos corretos (`function_args`).

**Exemplo: Adicionando o `elif` para a nova função**

```python
def process_function_call(self, function_name: str, function_args: dict, messages: list) -> str:
    logger.info(f"Processando chamada de função: {function_name}")
    logger.info(f"Argumentos: {function_args}")
    data = None
    generate = True
    save = True

    if function_name == "get_additional_context":
        data = self.get_additional_context(**function_args)
    
    elif function_name == "search_by_cpf":
        data = self.search_by_cpf(**function_args)
        
    elif function_name == "get_servicos_disponiveis":
        data = self.get_servicos_disponiveis(**function_args)
    
    elif function_name == "book_service": # <-- Adicione o bloco para sua função aqui
        data = self.book_service(**function_args)
          
    # ... continua a lógica da função process_function_call
```

Após seguir estes três passos nos três módulos de LLM (`openai`, `gemini` e `llama`), a sua nova funcionalidade estará integrada e pronta para ser utilizada pelo assistente de IA.

> Obs: Reiteramos que esta documentação pode mudar caso outros tipos de implementação venham a existir, mas por enquanto, são esses os padrões.

## Testes automatizados
Todo **Merge Request** deverá passar em todos os testes automatizados para ser aprovado. Há duas possibilidades em caso de falha:
- Feature modificou o comportamento da função 
  - Deve ser corrigido o código, pois demonstra **falta de retrocompatibilidade**.
- Correção modificou o comportamento da função 
  - O **teste pode precisar de revisão**, mas vale analisar se a **correção pode ter impactado em algo**.

Na página de Overview do seu Merge Request, em caso de sucesso nos testes, irá aparecer um resumo dos testes, com um ícone verde.
- Em baixo, haverá o seguinte indicador:
  ```
  Test coverage 49.00% (0.00%) from 1 job
  ```
- O número entre parênteses irá mostrar quantos porcento da cobertura foi alterado, em relação ao código da `main` e seu MR, caso seja um número negativo, é ideal revisar o teste relacionado à funcionalidade que foi alterada, e, se possível, ajustar os testes para contemplarem maior parte do código.

## Gerenciamento de Dependências

Para adicionar ou atualizar uma dependência de "nível superior", siga os passos abaixo:

1. **Registre a mudança no `requirements.in`:**
   Adicione ou atualize a dependência no arquivo `requirements.in`. Exemplo:
   ```plaintext
   fastapi~=0.110
   numpy>=1.25
   httpx[http2]  # ← nova dependência, por ex.
   ```

2. **Recompile o lock:**
   - Para atualizar apenas o pacote novo (e suas dependências):
     ```sh
     pip install pip-tools
     pip-compile --upgrade-package httpx requirements.in
     ```
   - Para revisar todas as dependências de uma vez (recomendado para features maiores):
     ```sh
     ./scripts/update_requirements
     ```

   Isso irá reescrever o `requirements.txt` com versões pinadas e hashes.

3. **Instale localmente a partir do lock:**
   Para garantir paridade com o ambiente de CI:
   ```sh
   pip-sync
   ```

4. **Comite os arquivos atualizados:**
   Certifique-se de incluir tanto o `requirements.in` quanto o `requirements.txt` no commit. Isso permite que o diff da PR mostre claramente:
   - A intenção do desenvolvedor (linha nova/alterada em `requirements.in`).
   - O impacto completo (sub-dependências geradas no `requirements.txt`).
