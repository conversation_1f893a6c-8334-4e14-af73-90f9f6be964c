ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-bookworm

ENV FLASK_DEBUG=${FLASK_DEBUG:-"False"}
ENV SERVER_PORT=${SERVER_PORT:-"8080"}
ENV DEBUG=${DEBUG:-"False"}
ENV TZ=${TZ:-"America/Sao_Paulo"}
ENV Z_API_CLIENT_TOKEN=${Z_API_CLIENT_TOKEN:-"F3b14fd459eb9462ebd51b359510e2c4dS"}
ENV GOOGLE_OAUTH_CLIENT_ID=${GOOGLE_OAUTH_CLIENT_ID:-"955715990156-id9k373knlsuh8r0rk5c3ppntrfnfvv6.apps.googleusercontent.com"}
ENV GOOGLE_OAUTH_CLIENT_SECRET=${GOOGLE_OAUTH_CLIENT_SECRET:-"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"}
ENV ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-"http://localhost:8080, https://api.z-api.io, ************"}

RUN apt-get update && apt-get install -y curl && apt-get clean

WORKDIR /app

COPY requirements.txt .
RUN pip install --require-hashes --no-deps -r requirements.txt

COPY ./src/api /app/src/api
COPY ./src/connections /app/src/connections
COPY ./src/data /app/src/data
COPY ./src/extras /app/src/extras
COPY ./src/integrations /app/src/integrations
COPY ./tests /app/tests
COPY ./src/api/.coveragerc /app/.coveragerc

EXPOSE $SERVER_PORT

ENV PYTHONPATH=/app:$PYTHONPATH
CMD ["python", "-u", "src/api/run.py"]
