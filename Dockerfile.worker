ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-bookworm

ENV DEBUG=${DEBUG:-"False"}
ENV TZ=${TZ:-"America/Sao_Paulo"}

ENV GOOGLE_MAPS_API=${GOOGLE_MAPS_API:-"teste"}
ENV BOT_CONVERSA_API_KEY=${BOT_CONVERSA_API_KEY:-"c0b9d8e4-3595-4580-9fa8-0f1926bd7801"}
ENV Z_API_CLIENT_TOKEN=${Z_API_CLIENT_TOKEN:-"F3b14fd459eb9462ebd51b359510e2c4dS"}
ENV URL_PACTO_DISCOVERY_MS=${URL_PACTO_DISCOVERY_MS:-"https://discovery.ms.pactosolucoes.com.br/"}
ENV NUM_THREADS=${NUM_THREADS:-"4"}
ENV PENDENCY_VERIFICATION_TIME=${PENDENCY_VERIFICATION_TIME:-"5"}
ENV SMTP_USERNAME=${SMTP_USERNAME:-"<EMAIL>"}
ENV SMTP_PASSWORD=${SMTP_PASSWORD:-"a99162af848595da80cdecf7ca9b1ca2-0920befd-9ec2e0a9"}
ENV DOMAIN=${DOMAIN:-"https://orion.pactosolucoes.com.br"}
ENV ROLES_TO_KEEP_REDIS=${ROLES_TO_KEEP_REDIS:-"assistant, user"}

WORKDIR /app

RUN apt-get update && apt-get install -y ffmpeg curl && apt-get clean
COPY requirements.txt .
RUN pip install --require-hashes --no-deps -r requirements.txt

COPY ./src/worker /app/src/worker
COPY ./src/connections /app/src/connections
COPY ./src/data /app/src/data
COPY ./src/extras /app/src/extras
COPY ./src/integrations /app/src/integrations
COPY ./tests /app/tests
COPY ./src/worker/.coveragerc /app/.coveragerc

ENV PYTHONPATH=/app:$PYTHONPATH
CMD ["python", "-u", "src/worker/entrypoint.py"]
