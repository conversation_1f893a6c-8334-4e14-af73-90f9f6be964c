__pycache__/
*.pyc
lib/
venv/
.venv/
UnitTests/**
old/
dados_teste/
.idea/
test.json
general.py
send_message_test.py
*.mp3
*.ogg
web_hook_fake.py
test.ipynb
test_audio.py
test_text.py
*.txt
# Não ignorar os requirements.txt
!requirements.txt
!**/requirements.txt
*.pth
*.wav
*aws_polly*
test.txt
.coverage/
htmlcov/
.pytest_cache/
.ropeproject/
coverage.xml
test_results/report.xml
backup/

*.mp3
*.webm
*.ogg
*.wav
fake_conversation.py
test_audio.py
test_routerllm.py
test_text.py
test.ipynb
web_hook_fake.py
.coverage
.htpasswd
tests/integration/videos/
test/visual/message/videos/
storage.json
tests/api/storage.json