if __name__ == "__main__":
    import argparse
    import sys
    import subprocess

    parser = argparse.ArgumentParser(description="Run Conversas.AI locally")
    parser.add_argument("--run", "-r", action="store_true", help="Run Conversas.AI locally")
    parser.add_argument("--no_router", "-nr", action="store_true", help="Run Conversas.AI locally without the router")
    parser.add_argument("--no_locust", "-nl", action="store_true", help="Run Conversas.AI locally without Locust")
    parser.add_argument("--with_monitor", "-wm", action="store_true", help="Run Conversas.AI locally with BigQuery monitoring")
    args = parser.parse_args()

    if args.run:
        full_args = ["docker-compose", "up", "--build", "api", "worker", "routellm", "redis", "redisinsight", "locust", "jaeger"]
        if args.no_router:
            full_args.remove("routellm")
        if args.no_locust:
            full_args.remove("locust")
        if args.with_monitor:
            full_args.append("monitoring")
    else:
        print("Please specify --run or --no_router")
        sys.exit(1)

    subprocess.run(full_args)
