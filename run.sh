#!/bin/bash

# Script simples para rodar test-docker-compose.yml e abrir no browser
set -e

COMPOSE_FILE="docker-compose.yml"
SERVICE_URL="http://localhost:8300"

echo "🚀 Iniciando docker-compose.yml..."

# Para containers existentes
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

# Inicia os serviços
docker-compose -f "$COMPOSE_FILE" up api worker --build -d

echo "⏳ Aguardando 5 segundos para os serviços iniciarem..."
sleep 5

echo "🌐 Tentando abrir browser..."

# Tenta abrir o browser de várias formas
python3 -c "import webbrowser; webbrowser.open('$SERVICE_URL/apidocs')" 2>/dev/null || \
python -c "import webbrowser; webbrowser.open('$SERVICE_URL/apidocs')" 2>/dev/null || \
xdg-open "$SERVICE_URL/apidocs" 2>/dev/null || \
firefox "$SERVICE_URL/apidocs" 2>/dev/null & \
google-chrome "$SERVICE_URL/apidocs" 2>/dev/null & \
chromium-browser "$SERVICE_URL/apidocs" 2>/dev/null &

echo "✅ Serviços iniciados!"
docker-compose -f "$COMPOSE_FILE" logs -f
