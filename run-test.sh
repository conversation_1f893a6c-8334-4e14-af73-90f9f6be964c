#!/bin/bash

# Script simples para rodar test-docker-compose.yml e abrir no browser
set -e

COMPOSE_FILE="test-docker-compose.yml"
SERVICE_URL="http://localhost:8042"

echo "🚀 Iniciando test-docker-compose.yml..."

# Para containers existentes
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

# Inicia os serviços
docker-compose -f "$COMPOSE_FILE" up --build -d

echo "⏳ Aguardando 30 segundos para os serviços iniciarem..."
sleep 30

echo "🌐 Tentando abrir browser..."

# Tenta abrir o browser de várias formas
python3 -c "import webbrowser; webbrowser.open('$SERVICE_URL')" 2>/dev/null || \
python -c "import webbrowser; webbrowser.open('$SERVICE_URL')" 2>/dev/null || \
xdg-open "$SERVICE_URL" 2>/dev/null || \
firefox "$SERVICE_URL" 2>/dev/null & \
google-chrome "$SERVICE_URL" 2>/dev/null & \
chromium-browser "$SERVICE_URL" 2>/dev/null &

echo "✅ Serviços iniciados!"
docker compose -f "$COMPOSE_FILE" logs -f
