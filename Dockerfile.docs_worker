ARG ARCH
FROM --platform=linux/$ARCH python:3.10-slim-bookworm

ENV TZ=${TZ:-"America/Sao_Paulo"}

WORKDIR /app

COPY src/worker/docs_worker/requirements.txt .
RUN pip install -r requirements.txt
RUN pip install docling --extra-index-url https://download.pytorch.org/whl/cpu

COPY ./src/connections /app/src/connections
COPY ./src/data /app/src/data
COPY ./src/extras /app/src/extras
COPY ./src/worker/docs_worker /app/src/worker/docs_worker
COPY ./src/worker/health_check /app/src/worker/health_check
COPY ./src/worker/llm_modules/openai/openai_embeddings_module.py /app/src/worker/llm_modules/openai/openai_embeddings_module.py

ENV PYTHONPATH=/app:$PYTHONPATH
CMD ["python", "-u", "src/worker/docs_worker/entrypoint.py"]
