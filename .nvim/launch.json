[{"name": "API Tests Debugger", "type": "debugpy", "request": "launch", "program": "${workspaceRoot}/tests/tests.py", "args": ["-u", "-a"], "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceRoot}"}}, {"name": "Worker Attach", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}, {"name": "Functionalities Tests Debugger", "type": "debugpy", "request": "launch", "program": "${workspaceRoot}/tests/tests.py", "args": ["-u", "-f"], "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceRoot}"}}, {"name": "Worker Tests Debugger", "type": "debugpy", "request": "launch", "program": "${workspaceRoot}/tests/tests.py", "args": ["-u", "-w"], "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceRoot}"}}, {"name": "LLM Tests Debugger", "type": "debugpy", "request": "launch", "program": "${workspaceRoot}/tests/unit/test_openai_response_module.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceRoot}"}}, {"name": "API Debugger", "type": "debugpy", "request": "launch", "program": "${workspaceRoot}/src/api/run.py", "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env.api.local", "env": {"PYTHONPATH": "${workspaceRoot}"}}]