#!/bin/bash

# Script para parar os serviços do test-docker-compose.yml
# Autor: Gerado automaticamente

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

COMPOSE_FILE="test-docker-compose.yml"

echo -e "${BLUE}🛑 Parando serviços do test-docker-compose.yml...${NC}"

# Verifica se o arquivo docker-compose existe
if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}❌ Erro: Arquivo $COMPOSE_FILE não encontrado!${NC}"
    exit 1
fi

# Para os serviços
echo -e "${YELLOW}🔄 Parando containers...${NC}"
docker-compose -f "$COMPOSE_FILE" down --volumes

# Remove volumes órfãos (opcional)
echo -e "${YELLOW}🧹 Removendo containers órfãos...${NC}"
docker-compose -f "$COMPOSE_FILE" down --remove-orphans

# Opção para limpar volumes (descomentada por segurança)
# echo -e "${YELLOW}🗑️  Removendo volumes...${NC}"
# docker-compose -f "$COMPOSE_FILE" down --volumes

echo -e "${GREEN}✅ Serviços parados com sucesso!${NC}"

# Mostra containers ainda rodando (se houver)
RUNNING_CONTAINERS=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(web|api|worker|redis|scheduler)" 2>/dev/null || true)

if [ -n "$RUNNING_CONTAINERS" ]; then
    echo -e "${YELLOW}⚠️  Containers relacionados ainda rodando:${NC}"
    echo "$RUNNING_CONTAINERS"
    echo -e "${BLUE}💡 Para parar todos os containers: docker stop \$(docker ps -q)${NC}"
else
    echo -e "${GREEN}✅ Nenhum container relacionado está rodando${NC}"
fi
